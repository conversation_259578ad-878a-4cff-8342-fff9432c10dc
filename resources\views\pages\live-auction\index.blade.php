@extends('partials.app')
@section('title', 'Browse Listings')
@section('description', 'Discover amazing deals and unique items in our premium marketplace.')



@section('content')

<!-- Hero Section - نفس تصميم Dashboard -->
<div class="ads-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="hero-content">
                    <div class="hero-breadcrumb">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Browse Listings</li>
                            </ol>
                        </nav>
                    </div>
                    <h1 class="hero-title">Discover Amazing Deals</h1>
                    <p class="hero-subtitle">Browse thousands of verified listings from trusted sellers</p>

                    <!-- Hero Stats -->
                    <div class="hero-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="stat-content">
                                <h3>{{ number_format($ads->total()) }}</h3>
                                <p>Active Listings</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <div class="stat-content">
                                <h3>{{ $categories->count() }}</h3>
                                <p>Categories</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3>24/7</h3>
                                <p>Support</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="ads-main-section">
    <div class="container">
        <!-- Filter Section - نفس تصميم Dashboard Cards -->
        <div class="filter-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i>
                    Find What You're Looking For
                </h3>
                <p class="card-subtitle">Use our advanced filters to discover exactly what you need</p>
            </div>

            <div class="card-body">
                <form class="filter-form" method="GET" id="filterForm">
                    <div class="row g-3">
                        <!-- Search Input -->
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-search me-1"></i>Search
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-search"></i>
                                    <input type="text" name="search" class="form-control"
                                           placeholder="What are you looking for?"
                                           value="{{ request('search') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Main Category Filter -->
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-list me-1"></i>Main Category
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-list"></i>
                                    <select name="category" id="mainCategory" class="form-control">
                                        <option value="">All Categories</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->slug }}" @selected($category->slug == request()->category)>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Sub Category Filter -->
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tags me-1"></i>Sub Category
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-tags"></i>
                                    <select name="subcategory" id="subCategory" class="form-control" disabled>
                                        <option value="">Select Main Category First</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Country Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-globe me-1"></i>Country
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-globe"></i>
                                    <select name="country" class="form-control">
                                        <option value="">All Countries</option>
                                        @foreach ($countries as $country)
                                            <option value="{{ $country->iso2 }}" @selected($country->iso2 == request()->country)>
                                                {{ $country->emoji ?? '🌍' }} {{ $country->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Price Range Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>Price Range
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-dollar-sign"></i>
                                    <select name="price_range" class="form-control">
                                        <option value="">Any Price</option>
                                        @foreach ($priceRanges as $range)
                                            <option value="{{ $range->value }}" @selected($range->value == request()->price_range)>
                                                {{ $range->label() }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Sort Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-sort me-1"></i>Sort By
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-sort"></i>
                                    <select name="sort" class="form-control">
                                        <option value="newest" @selected(request('sort') == 'newest')>Newest First</option>
                                        <option value="oldest" @selected(request('sort') == 'oldest')>Oldest First</option>
                                        <option value="price_low" @selected(request('sort') == 'price_low')>Price: Low to High</option>
                                        <option value="price_high" @selected(request('sort') == 'price_high')>Price: High to Low</option>
                                        <option value="popular" @selected(request('sort') == 'popular')>Most Popular</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Price Min -->
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-money-bill me-1"></i>Min Price
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-money-bill"></i>
                                    <input type="number" name="price_min" class="form-control"
                                           placeholder="Min price"
                                           value="{{ request('price_min') }}"
                                           min="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- Custom Price Max -->
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-money-bill-wave me-1"></i>Max Price
                                </label>
                                <div class="input-wrapper">
                                    <i class="input-icon fas fa-money-bill-wave"></i>
                                    <input type="number" name="price_max" class="form-control"
                                           placeholder="Max price"
                                           value="{{ request('price_max') }}"
                                           min="0" step="0.01">
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Attributes Container -->
                        <div id="attributesContainer" class="col-12">
                            <!-- Dynamic attributes will be loaded here -->
                        </div>

                        <!-- Filter Buttons -->
                        <div class="col-md-6 col-lg-3">
                            <div class="form-group">
                                <label class="form-label">&nbsp;</label>
                                <div class="filter-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                        Search
                                    </button>
                                    @if(request()->hasAny(['search', 'category', 'country', 'price_range', 'sort']))
                                        <a href="{{ route('live-auction') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i>
                                            Clear
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section">
            <div class="results-header">
                <div class="results-info">
                    <h3>
                        @if(request()->hasAny(['search', 'category', 'country', 'price_range']))
                            <i class="fas fa-search"></i>
                            Search Results
                        @else
                            <i class="fas fa-list"></i>
                            All Listings
                        @endif
                    </h3>
                    <p class="results-count">
                        Showing {{ $ads->firstItem() ?? 0 }}-{{ $ads->lastItem() ?? 0 }} of {{ number_format($ads->total()) }} listings
                        @if(request('search'))
                            for "<strong>{{ request('search') }}</strong>"
                        @endif
                    </p>
                </div>

                <div class="view-toggle">
                    <button class="view-btn" data-view="grid" title="Grid View">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="view-btn active" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Listings Grid -->
            <div class="listings-container list-view" id="listings-container">
                @forelse ($ads as $ad)
                    @php
                        $userRank = $ad->rank ?? ($ad->user->rank ?? 0);
                        $isTrusted = $ad->is_trusted || ($ad->user && $ad->user->is_trusted);
                        $isSponsored = $ad->sponsoredAd && $ad->sponsoredAd->isActive();
                    @endphp
                    <div class="listing-item
                        @if($userRank == 1) vip-listing @endif
                        @if($userRank == 3) company-listing @endif
                        @if($isSponsored) sponsored-listing @endif
                        @if(!$isTrusted) untrusted-listing @endif
                    " data-category="{{ $ad->category->slug ?? '' }}"
                    @if($isSponsored) data-sponsored-id="{{ $ad->id }}" @endif>
                        <div class="listing-card
                            @if($userRank == 1) vip-card @endif
                            @if($userRank == 3) company-card @endif
                            @if($isSponsored) sponsored-card @endif
                        ">
                            {{-- Premium Badge Overlay --}}
                            @if($userRank == 1)
                                <div class="premium-badge vip-premium">
                                    <i class="fas fa-crown"></i>
                                    <span>VIP</span>
                                </div>
                            @elseif($userRank == 3)
                                <div class="premium-badge company-premium">
                                    <i class="fas fa-building"></i>
                                    <span>COMPANY</span>
                                </div>
                            @elseif($isSponsored)
                                <div class="premium-badge sponsored-premium">
                                    <i class="fas fa-star"></i>
                                    <span>SPONSORED</span>
                                </div>
                            @endif
                            <div class="listing-image">
                                <a href="{{ route('auction-details', $ad->slug) }}">
                                    @if($ad->media->count() > 0)
                                        <img src="{{ $ad->media->first()->url }}" alt="{{ $ad->title }}" loading="lazy">
                                    @else
                                        <div class="no-image">
                                            <i class="fas fa-image"></i>
                                            <span>No Image</span>
                                        </div>
                                    @endif
                                </a>

                                <!-- Status Badge -->
                                @if($ad->active())
                                    <div class="status-badge status-active">
                                        <i class="fas fa-circle"></i>
                                        Active
                                    </div>
                                @elseif($ad->expired())
                                    <div class="status-badge status-expired">
                                        <i class="fas fa-clock"></i>
                                        Expired
                                    </div>
                                @else
                                    <div class="status-badge status-upcoming">
                                        <i class="fas fa-hourglass-half"></i>
                                        Upcoming
                                    </div>
                                @endif

                                <!-- Favorite Button -->
                                <button class="favorite-btn" data-listing-id="{{ $ad->id }}">
                                    <i class="far fa-heart"></i>
                                </button>

                                <!-- Image Count -->
                                @if($ad->media->count() > 1)
                                    <div class="image-count">
                                        <i class="fas fa-images"></i>
                                        {{ $ad->media->count() }}
                                    </div>
                                @endif
                            </div>

                            <div class="listing-content">
                                <div class="listing-header">
                                    <h4 class="listing-title">
                                        <a href="{{ route('auction-details', $ad->slug) }}">{{ $ad->title }}</a>
                                    </h4>
                                    <div class="listing-price">{{ money($ad->price) }}</div>
                                </div>

                                <div class="listing-meta">
                                    {{-- <div class="meta-item">
                                        <i class="fas fa-tag"></i>
                                        <span>{{ $ad->category->name ?? 'Uncategorized' }}</span>
                                    </div> --}}
                                    <div class="meta-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>{{ $ad->seller_address ?? 'Location not specified' }}</span>
                                    </div>
                                    <div class="meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>{{ $ad->created_at->diffForHumans() }}</span>
                                    </div>
                                </div>

                                {{-- <p class="listing-description">
                                    {{ shorten_chars($ad->description, 100, true) }}
                                </p> --}}

                                <!-- Ad Attributes -->
                                {{-- @if($ad->attributes && $ad->attributes->count() > 0)
                                    <div class="listing-attributes">
                                        @foreach($ad->attributes->take(2) as $attribute)
                                            <div class="attribute-item">
                                                <span class="attribute-value">{{ $attribute->formatted_value }}</span>
                                            </div>
                                        @endforeach
                                        @if($ad->attributes->count() > 1)
                                            <div class="attribute-item more-attributes">
                                                <span class="more-text">+{{ $ad->attributes->count() - 1 }} more</span>
                                            </div>
                                        @endif
                                    </div>
                                @endif --}}

                                <div class="listing-footer">
                                    <div class="seller-info">
                                                <!-- User Rank Badge -->
                                                @php
                                                    $userRank = $ad->rank ?? ($ad->user->rank ?? 0);
                                                @endphp
                                                @if($userRank == 1)
                                                    <span class="rank-badge vip-badge">
                                                        <i class="fas fa-crown"></i> VIP
                                                    </span>
                                                @elseif($userRank == 2)
                                                    <span class="rank-badge trader-badge">
                                                        <i class="fas fa-briefcase"></i> Trader
                                                    </span>
                                                @elseif($userRank == 3)
                                                    <span class="rank-badge company-badge">
                                                        <i class="fas fa-building"></i> Company
                                                    </span>
                                                @elseif($userRank == 4)
                                                    <span class="rank-badge admin-badge">
                                                        <i class="fas fa-user-shield"></i> Admin
                                                    </span>
                                                @endif
                                            </div>

                                            <div class="seller-badges">
                                                @if($ad->is_trusted || ($ad->user && $ad->user->is_trusted))
                                                    <span class="verified-badge">
                                                        <i class="fas fa-shield-check"></i>
                                                        Verified Identity
                                                    </span>
                                                @endif

                                                @if($ad->is_sponsored)
                                                    <span class="sponsored-badge">
                                                        <i class="fas fa-star"></i>
                                                        Sponsored
                                                    </span>
                                                @endif

                                                @if($ad->needs_brokering && $userRank != 1) {{-- Don't show for brokers --}}
                                                    <span class="broker-badge">
                                                        <i class="fas fa-handshake"></i>
                                                        Broker Available
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <div class="listing-actions">
                                        @if($ad->bids->count() > 0)
                                            <span class="bid-count">
                                                <i class="fas fa-gavel"></i>
                                                {{ $ad->bids->count() }} {{ $ad->bids->count() == 1 ? 'bid' : 'bids' }}
                                            </span>
                                        @endif
                                        <a href="{{ route('auction-details', $ad->slug) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>No Listings Found</h3>
                        <p>
                            @if(request()->hasAny(['search', 'category', 'country', 'price_range']))
                                Try adjusting your search criteria or browse all listings.
                            @else
                                Be the first to post a listing in our marketplace!
                            @endif
                        </p>
                        <div class="empty-actions">
                            @if(request()->hasAny(['search', 'category', 'country', 'price_range']))
                                <a href="{{ route('live-auction') }}" class="btn btn-secondary">
                                    <i class="fas fa-list"></i>
                                    Browse All Listings
                                </a>
                            @endif
                            <a href="{{ route('add-listing') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Post Your Listing
                            </a>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($ads->hasPages())
                <div class="pagination-section">
                    <div class="pagination-info">
                        <p>
                            Showing {{ $ads->firstItem() }}-{{ $ads->lastItem() }} of {{ number_format($ads->total()) }} results
                        </p>
                    </div>
                    <div class="pagination-wrapper">
                        {{ $ads->appends(request()->query())->links('pagination.custom') }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="stats-section">
    <div class="container">
        <x-metric-card />
    </div>
</div>

@push('styles')
<style>
/* نفس تصميم Dashboard و Profile */

/* Hero Section */
.ads-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem 0 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.ads-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.hero-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.hero-breadcrumb .breadcrumb-item.active {
    color: white;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* Hero Stats - نفس تصميم Dashboard */
.hero-stats {
    display: flex;
    gap: 1.5rem;
    margin-top: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.25rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 140px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.stat-content h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: white;
}

.stat-content p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Main Section */
.ads-main-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

/* Enhanced Filter Card */
.filter-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.filter-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    padding: 2rem 2rem 0;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
}

.card-title {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-subtitle {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

.card-body {
    padding: 0 2rem 2rem;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.875rem;
}

.form-label i {
    color: #3498db;
    font-size: 0.8rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 1rem;
    color: #6c757d;
    z-index: 2;
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
    transform: translateY(-1px);
}

.form-control:focus + .input-icon,
.form-control:focus ~ .input-icon {
    color: #3498db;
}

/* Enhanced Attributes Container */
#attributesContainer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px solid rgba(52, 152, 219, 0.1);
    transition: all 0.3s ease;
}

#attributesContainer:not(:empty) {
    animation: slideDown 0.3s ease-out;
}

#attributesContainer h6 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
    display: inline-block;
}

/* Loading States */
.form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
    cursor: not-allowed;
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

.btn-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
}

/* Results Section */
.results-section {
    margin-bottom: 2rem;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.results-info h3 {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-count {
    color: #6c757d;
    margin: 0;
    font-size: 0.875rem;
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.view-btn:hover,
.view-btn.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

/* Listings Container */
.listings-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
}

.listings-container.list-view {
    grid-template-columns: 1fr;
}

.listings-container.list-view .listing-card {
    display: flex;
    align-items: stretch;
}

.listings-container.list-view .listing-image {
    width: 250px;
    flex-shrink: 0;
}

.listings-container.list-view .listing-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Listing Card - نفس تصميم Dashboard Cards */
.listing-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.listing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.listing-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.listing-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.listing-card:hover .listing-image img {
    transform: scale(1.05);
}

.no-image {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    gap: 0.5rem;
}

.no-image i {
    font-size: 2rem;
    opacity: 0.5;
}

.no-image span {
    font-size: 0.875rem;
    opacity: 0.7;
}

.status-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-active {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.status-expired {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

.status-upcoming {
    background: rgba(255, 193, 7, 0.9);
    color: white;
}

.favorite-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.favorite-btn:hover {
    background: white;
    color: #dc3545;
    transform: scale(1.1);
}

.image-count {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Listing Content */
.listing-content {
    padding: 1.25rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.listing-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.listing-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.3;
    flex: 1;
}

.listing-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.listing-title a:hover {
    color: #667eea;
}

.listing-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #28a745;
    white-space: nowrap;
}

.listing-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.8rem;
}

.meta-item i {
    color: #667eea;
    width: 12px;
    font-size: 0.75rem;
}

.listing-description {
    color: #495057;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex: 1;
    font-size: 0.875rem;
}

.listing-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
    margin-top: auto;
}

.seller-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.seller-avatar-container {
    position: relative;
}

.seller-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e9ecef;
    position: relative;
}

.avatar-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    border: 1px solid white;
}

.trust-badge {
    background: #28a745;
    color: white;
}

.untrust-badge {
    background: #dc3545;
    color: white;
}

.seller-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.seller-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex-grow: 1;
}

.seller-name-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.seller-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.25rem;
}

.seller-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.8rem;
}

.seller-name a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.seller-name a:hover {
    color: #667eea;
    text-decoration: underline;
}

/* Rank Badges */
.rank-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.15rem 0.4rem;
    border-radius: 12px;
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
}

.vip-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 1px solid #ffd700;
}

.trader-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.company-badge {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.admin-badge {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    color: white;
}

/* Status Badges */
.verified-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #28a745;
    font-size: 0.65rem;
    font-weight: 600;
    background: #d4edda;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
}

.sponsored-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #ff6b35;
    font-size: 0.65rem;
    font-weight: 600;
    background: #fff3e0;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
}

.broker-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6f42c1;
    font-size: 0.65rem;
    font-weight: 600;
    background: #f3e5f5;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
}

.listing-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.bid-count {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6c757d;
    font-size: 0.75rem;
}

.listing-actions .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 8px;
    font-weight: 600;
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.empty-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.empty-icon i {
    font-size: 1.5rem;
    color: white;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    font-size: 0.9rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-card {
        min-width: auto;
        width: 100%;
        max-width: 300px;
    }

    .results-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .listings-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .ads-hero-section {
        padding: 2rem 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .stat-card {
        min-width: 200px;
        justify-content: center;
    }

    .card-header,
    .card-body {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .filter-actions {
        flex-direction: column;
    }

    .listings-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .listings-container.list-view .listing-card {
        flex-direction: column;
    }

    .listings-container.list-view .listing-image {
        width: 100%;
        height: 200px;
    }

    .listing-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .listing-actions {
        justify-content: space-between;
    }

    .pagination-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-card {
        min-width: auto;
        width: 100%;
    }

    .empty-actions {
        flex-direction: column;
        align-items: center;
    }

    .empty-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
@endpush



</script>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing filters...');

    // Get filter elements
    const filterForm = document.getElementById('filterForm');
    const mainCategorySelect = document.getElementById('mainCategory');
    const subCategorySelect = document.getElementById('subCategory');
    const attributesContainer = document.getElementById('attributesContainer');

    console.log('Elements found:', {
        filterForm: !!filterForm,
        mainCategorySelect: !!mainCategorySelect,
        subCategorySelect: !!subCategorySelect,
        attributesContainer: !!attributesContainer
    });

    // Function to load subcategories
    function loadSubcategories(categorySlug) {
        console.log('🔄 Loading subcategories for category:', categorySlug);

        if (!subCategorySelect) {
            console.error('❌ subCategorySelect not found!');
            return;
        }

        // Show loading state
        subCategorySelect.innerHTML = '<option value="">Loading subcategories...</option>';
        subCategorySelect.disabled = true;

        const url = `/api/subcategories/${categorySlug}`;
        console.log('📡 Fetching:', url);

        fetch(url)
            .then(response => {
                console.log('📥 Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('📊 API Response:', data);

                if (data.success && data.data && Array.isArray(data.data)) {
                    console.log('✅ Found', data.data.length, 'subcategories');

                    // Clear and add default option
                    subCategorySelect.innerHTML = '<option value="">All Subcategories</option>';

                    // Add subcategories
                    data.data.forEach((subcategory, index) => {
                        console.log(`  ${index + 1}. ${subcategory.name} (${subcategory.slug})`);
                        const option = document.createElement('option');
                        option.value = subcategory.slug;
                        option.textContent = subcategory.name;
                        subCategorySelect.appendChild(option);
                    });

                    // Enable the select
                    subCategorySelect.disabled = false;
                    console.log('✅ Subcategories loaded successfully!');

                    // Visual feedback
                    subCategorySelect.style.borderColor = '#28a745';
                    setTimeout(() => {
                        subCategorySelect.style.borderColor = '';
                    }, 1000);

                } else {
                    console.log('⚠️ No subcategories found');
                    subCategorySelect.innerHTML = '<option value="">No subcategories found</option>';
                    subCategorySelect.disabled = false;
                }
            })
            .catch(error => {
                console.error('❌ Error loading subcategories:', error);
                subCategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
                subCategorySelect.disabled = false;

                // Visual error feedback
                subCategorySelect.style.borderColor = '#dc3545';
                setTimeout(() => {
                    subCategorySelect.style.borderColor = '';
                }, 2000);
            });
    }

    // Function to load attributes
    function loadAttributes(categorySlug) {
        console.log('🔄 Loading attributes for category:', categorySlug);

        if (!attributesContainer) {
            console.log('⚠️ attributesContainer not found, skipping attributes');
            return;
        }

        // Show loading state
        attributesContainer.innerHTML = `
            <div class="text-center py-3">
                <div class="loading-spinner"></div>
                <span class="ms-2">Loading additional filters...</span>
            </div>
        `;

        fetch(`/api/attributes/${categorySlug}`)
            .then(response => response.json())
            .then(data => {
                console.log('📊 Attributes response:', data);

                if (data.success && data.attributes && data.attributes.length > 0) {
                    console.log('✅ Found', data.attributes.length, 'attributes');
                    renderAttributes(data.attributes);
                } else {
                    console.log('⚠️ No attributes found');
                    attributesContainer.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('❌ Error loading attributes:', error);
                attributesContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load additional filters.
                    </div>
                `;
            });
    }

    // Function to render attributes
    function renderAttributes(attributes) {
        let html = '<div class="row g-3 mt-2"><div class="col-12"><h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>Additional Filters</h6></div>';

        attributes.forEach(attribute => {
            const currentValue = new URLSearchParams(window.location.search).get(`attributes[${attribute.name}]`) || '';

            html += `<div class="col-md-6 col-lg-3">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-tag me-1"></i>${attribute.label}
                    </label>
                    <div class="input-wrapper">
                        <i class="input-icon fas fa-tag"></i>`;

            if (attribute.type === 'select' && attribute.options) {
                html += `<select name="attributes[${attribute.name}]" class="form-control">
                    <option value="">Any ${attribute.label}</option>`;

                attribute.options.forEach(option => {
                    const selected = option === currentValue ? 'selected' : '';
                    html += `<option value="${option}" ${selected}>${option}</option>`;
                });

                html += '</select>';
            } else {
                html += `<input type="text" name="attributes[${attribute.name}]"
                    class="form-control" placeholder="Enter ${attribute.label}"
                    value="${currentValue}">`;
            }

            html += `</div></div></div>`;
        });

        html += '</div>';
        attributesContainer.innerHTML = html;
    }

    // Main category change handler
    if (mainCategorySelect) {
        mainCategorySelect.addEventListener('change', function() {
            const categorySlug = this.value;
            console.log('🎯 Main category changed to:', categorySlug);

            // Reset subcategory and attributes
            if (subCategorySelect) {
                subCategorySelect.innerHTML = '<option value="">Loading...</option>';
                subCategorySelect.disabled = true;
            }

            if (attributesContainer) {
                attributesContainer.innerHTML = '';
            }

            if (categorySlug) {
                // Load subcategories and attributes
                loadSubcategories(categorySlug);
                loadAttributes(categorySlug);
            } else {
                // Reset to default state
                if (subCategorySelect) {
                    subCategorySelect.innerHTML = '<option value="">Select Main Category First</option>';
                    subCategorySelect.disabled = true;
                }
                console.log('🔄 Category cleared, reset to default state');
            }
        });

        console.log('✅ Main category event listener attached');
    } else {
        console.error('❌ mainCategorySelect not found!');
    }

    // View toggle functionality
    const viewButtons = document.querySelectorAll('.view-btn');
    const listingsContainer = document.getElementById('listings-container');

    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.dataset.view;

            // Update active button
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update container class
            listingsContainer.className = view === 'list' ? 'listings-container list-view' : 'listings-container';
        });
    });

    // Test if elements exist
    console.log('Script loaded successfully!');
    console.log('mainCategorySelect:', mainCategorySelect);
    console.log('subCategorySelect:', subCategorySelect);
    console.log('attributesContainer:', attributesContainer);

    // Add a simple test
    if (!mainCategorySelect) {
        console.error('mainCategory element not found! Trying alternative selector...');
        const altSelect = document.querySelector('select[name="category"]');
        console.log('Alternative selector found:', altSelect);
    }

    // Main Category Change Handler
    if (mainCategorySelect) {
        mainCategorySelect.addEventListener('change', function() {
            const categorySlug = this.value;
            console.log('Category changed to:', categorySlug);

            // Reset subcategory and attributes
            subCategorySelect.innerHTML = '<option value="">Loading...</option>';
            subCategorySelect.disabled = true;
            attributesContainer.innerHTML = '';

            if (categorySlug) {
                // Load subcategories
                loadSubcategories(categorySlug);
                // Load attributes for main category
                loadAttributes(categorySlug);
            } else {
                subCategorySelect.innerHTML = '<option value="">Select Main Category First</option>';
                subCategorySelect.disabled = true;
            }
        });
    } else {
        console.error('mainCategorySelect not found!');
    }

    // Subcategory Change Handler
    subCategorySelect.addEventListener('change', function() {
        const subcategorySlug = this.value;

        if (subcategorySlug) {
            // Load attributes for subcategory
            loadAttributes(subcategorySlug);
        }
    });

    // Load Subcategories Function
    function loadSubcategories(categorySlug) {
        console.log('Loading subcategories for:', categorySlug);

        // Add loading state
        subCategorySelect.innerHTML = '<option value="">Loading subcategories...</option>';
        subCategorySelect.disabled = true;

        const url = `/api/subcategories/${categorySlug}`;
        console.log('Fetching URL:', url);

        fetch(url)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API Response:', data);

                if (data.success && data.data && Array.isArray(data.data)) {
                    console.log('Found', data.data.length, 'subcategories');

                    subCategorySelect.innerHTML = '<option value="">All Subcategories</option>';

                    data.data.forEach(subcategory => {
                        console.log('Adding subcategory:', subcategory.name, subcategory.slug);
                        const option = document.createElement('option');
                        option.value = subcategory.slug;
                        option.textContent = subcategory.name;
                        option.selected = subcategory.slug === '{{ request("subcategory") }}';
                        subCategorySelect.appendChild(option);
                    });

                    subCategorySelect.disabled = false;

                    // Add success animation
                    subCategorySelect.style.borderColor = '#28a745';
                    setTimeout(() => {
                        subCategorySelect.style.borderColor = '';
                    }, 1000);
                } else {
                    console.log('No subcategories found or invalid response');
                    subCategorySelect.innerHTML = '<option value="">No subcategories found</option>';
                    subCategorySelect.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error loading subcategories:', error);
                subCategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
                subCategorySelect.disabled = false;
                subCategorySelect.style.borderColor = '#dc3545';
                setTimeout(() => {
                    subCategorySelect.style.borderColor = '';
                }, 2000);
            });
    }

    // Load Attributes Function
    function loadAttributes(categorySlug) {
        // Show loading state
        attributesContainer.innerHTML = `
            <div class="text-center py-3">
                <span class="loading-spinner"></span>
                <span class="ms-2">Loading additional filters...</span>
            </div>
        `;

        fetch(`/api/attributes/${categorySlug}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.attributes.length > 0) {
                    renderAttributes(data.attributes);
                } else {
                    attributesContainer.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('Error loading attributes:', error);
                attributesContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load additional filters. Please try again.
                    </div>
                `;
            });
    }

    // Render Attributes Function
    function renderAttributes(attributes) {
        let attributesHtml = '<div class="row g-3 mt-2"><div class="col-12"><h6 class="mb-3"><i class="fas fa-sliders-h me-2"></i>Additional Filters</h6></div>';

        attributes.forEach(attribute => {
            const currentValue = new URLSearchParams(window.location.search).get(`attributes[${attribute.name}]`) || '';

            attributesHtml += `<div class="col-md-6 col-lg-3">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-tag me-1"></i>${attribute.label}
                    </label>
                    <div class="input-wrapper">
                        <i class="input-icon fas fa-tag"></i>`;

            if (attribute.type === 'select' && attribute.options) {
                attributesHtml += `<select name="attributes[${attribute.name}]" class="form-control">
                    <option value="">Any ${attribute.label}</option>`;

                attribute.options.forEach(option => {
                    const selected = option === currentValue ? 'selected' : '';
                    attributesHtml += `<option value="${option}" ${selected}>${option}</option>`;
                });

                attributesHtml += '</select>';
            } else {
                attributesHtml += `<input type="text" name="attributes[${attribute.name}]"
                    class="form-control" placeholder="Enter ${attribute.label}"
                    value="${currentValue}">`;
            }

            attributesHtml += `</div></div></div>`;
        });

        attributesHtml += '</div>';
        attributesContainer.innerHTML = attributesHtml;
    }

    // Initialize on page load
    const currentCategory = mainCategorySelect.value;
    const currentSubcategory = '{{ request("subcategory") }}';

    if (currentCategory) {
        console.log('Initializing with category:', currentCategory);
        loadSubcategories(currentCategory);
        loadAttributes(currentCategory);
    } else {
        // Make sure subcategory is properly disabled
        subCategorySelect.innerHTML = '<option value="">Select Main Category First</option>';
        subCategorySelect.disabled = true;
    }

    // Favorite functionality
    const favoriteButtons = document.querySelectorAll('.favorite-btn');
    favoriteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const icon = this.querySelector('i');

            // Toggle favorite state with animation
            this.style.transform = 'scale(0.8)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);

            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.classList.add('active');
                // Show success message (you can replace with toast notification)
                console.log('Added to favorites!');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                this.classList.remove('active');
                console.log('Removed from favorites!');
            }
        });
    });

    // Add loading state to filter form
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const submitButton = this.querySelector('.btn-primary');
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
                submitButton.disabled = true;
                submitButton.style.opacity = '0.7';

                // Re-enable after a delay (in case of errors)
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                    submitButton.style.opacity = '1';
                }, 5000);
            }
        });
    }

    // Add smooth scroll to listings when filter is applied
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('search') || urlParams.has('category') || urlParams.has('location')) {
        setTimeout(() => {
            const listingsSection = document.getElementById('listings-container');
            if (listingsSection) {
                listingsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }, 100);
    }

    }, 100); // Wait 100ms for elements to be ready
});
</script>
@endpush

@push('styles')
<style>
/* Listing Attributes Styles */
.listing-attributes {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.attribute-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 0.85rem;
}

.attribute-label {
    color: #666;
    font-weight: 500;
    flex-shrink: 0;
    margin-right: 8px;
}

.attribute-value {
    color: #333;
    font-weight: 600;
    text-align: right;
    flex-grow: 1;
}

.more-attributes .more-text {
    color: #007bff;
    font-size: 0.8rem;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .attribute-item {
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 8px;
    }

    .attribute-label {
        margin-bottom: 2px;
        margin-right: 0;
    }

    .attribute-value {
        text-align: left;
    }
}

/* Premium Badge Overlay */
.premium-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.vip-premium {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b6914;
    border: 2px solid #ffd700;
    animation: vip-glow 2s ease-in-out infinite alternate;
}

.company-premium {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
    border: 2px solid #f5576c;
}

.sponsored-premium {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: 2px solid #ff6b35;
}

@keyframes vip-glow {
    0% { box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3); }
    100% { box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6); }
}

@keyframes golden-bg {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Special Listing Types */
.vip-listing .listing-card {
    border: 4px solid #ffd700;
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.6);
    position: relative;
    overflow: hidden;
    background: linear-gradient(145deg, #fffbf0, #fff8e1, #fffacd, #fff8e1);
    background-size: 400% 400%;
    animation: golden-bg 3s ease-in-out infinite;
}

.vip-listing .listing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
    animation: shimmer 3s infinite;
}

.company-listing .listing-card {
    border: 3px solid #f5576c;
    box-shadow: 0 12px 35px rgba(245, 87, 108, 0.5);
    background: linear-gradient(145deg, #fef7f8, #fce4ec, #f8bbd9, #fce4ec);
    background-size: 400% 400%;
    animation: company-bg 4s ease-in-out infinite;
}

.company-listing .listing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #f093fb, #f5576c, #f093fb);
    animation: company-shimmer 2.5s infinite;
}

.sponsored-listing .listing-card {
    border: 3px solid #ff6b35;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
    position: relative;
    background: linear-gradient(145deg, #fff9f7, #ffe0d1, #ffccbc);
    background-size: 200% 200%;
    animation: sponsored-bg 4.5s ease-in-out infinite;
}

.sponsored-listing .listing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b35, #f7931e, #ff6b35);
    animation: sponsored-shimmer 2s infinite;
}

@keyframes company-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes sponsored-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes company-bg {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes sponsored-bg {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.untrusted-listing .listing-card {
    border: 1px solid #dc3545;
    opacity: 0.9;
}

.untrusted-listing .listing-card::before {
    content: '⚠️ UNVERIFIED';
    position: absolute;
    top: 10px;
    left: 10px;
    background: #dc3545;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 600;
    z-index: 2;
    white-space: nowrap;
    max-width: calc(100% - 20px);
    overflow: hidden;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Hover effects for special listings */
.vip-listing .listing-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 50px rgba(255, 215, 0, 0.5);
    border-color: #ffed4e;
}

.vip-listing .listing-card:hover .premium-badge {
    transform: scale(1.1);
    animation: vip-pulse 1s ease-in-out infinite;
}

.company-listing .listing-card:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow: 0 15px 40px rgba(245, 87, 108, 0.4);
    border-color: #f093fb;
}

.sponsored-listing .listing-card:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.4);
    border-color: #f7931e;
}

@keyframes vip-pulse {
    0%, 100% { transform: scale(1.1); }
    50% { transform: scale(1.15); }
}

/* Premium badge hover effects */
.premium-badge:hover {
    transform: scale(1.05);
    cursor: pointer;
}
</style>
@endpush

@endsection