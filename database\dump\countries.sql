-- -------------------------------------------------------------
-- TablePlus 5.4.0(504)
--
-- https://tableplus.com/
--
-- Database: bazaar
-- Generation Time: 2023-08-22 20:37:37.6600
-- -------------------------------------------------------------


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


DROP TABLE IF EXISTS `countries`;
CREATE TABLE `countries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `iso2` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL,
  `iso3` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `capital` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_symbol` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_code` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emoji` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `countries_iso2_index` (`iso2`),
  KEY `countries_iso3_index` (`iso3`),
  KEY `countries_name_index` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=251 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `countries` (`id`, `iso2`, `iso3`, `name`, `phone_code`, `capital`, `currency`, `currency_symbol`, `currency_code`, `emoji`, `created_at`, `updated_at`) VALUES
(1, 'AD', 'AND', 'Andorra', '376', 'Andorra la Vella', 'Euro', '€', 'EUR', '🇦🇩', '2023-08-22 19:22:46', '2023-08-22 19:22:46'),
(2, 'AE', 'ARE', 'United Arab Emirates', '971', 'Abu Dhabi', 'United Arab Emirates Dirham', 'إ.د', 'AED', '🇦🇪', '2023-08-22 19:22:47', '2023-08-22 19:22:47'),
(3, 'AF', 'AFG', 'Afghanistan', '93', 'Kabul', 'Afghan Afghani', '؋', 'AFN', '🇦🇫', '2023-08-22 19:22:48', '2023-08-22 19:22:48'),
(4, 'AG', 'ATG', 'Antigua and Barbuda', '1268', 'Saint John\'s', 'Eastern Caribbean Dollar', '$', 'XCD', '🇦🇬', '2023-08-22 19:22:49', '2023-08-22 19:22:49'),
(5, 'AI', 'AIA', 'Anguilla', '1264', 'The Valley', 'Eastern Caribbean Dollar', '$', 'XCD', '🇦🇮', '2023-08-22 19:22:50', '2023-08-22 19:22:50'),
(6, 'AL', 'ALB', 'Albania', '355', 'Tirana', 'Albanian Lek', 'Lek', 'ALL', '🇦🇱', '2023-08-22 19:22:51', '2023-08-22 19:22:51'),
(7, 'AM', 'ARM', 'Armenia', '374', 'Yerevan', 'Armenian Dram', '֏', 'AMD', '🇦🇲', '2023-08-22 19:22:52', '2023-08-22 19:22:52'),
(8, 'AO', 'AGO', 'Angola', '244', 'Luanda', 'Angolan Kwanza', 'Kz', 'AOA', '🇦🇴', '2023-08-22 19:22:53', '2023-08-22 19:22:53'),
(9, 'AQ', 'ATA', 'Antarctica', '672', '', '', '$', '', '🇦🇶', '2023-08-22 19:22:54', '2023-08-22 19:22:54'),
(10, 'AR', 'ARG', 'Argentina', '54', 'Buenos Aires', 'Argentine Peso', '$', 'ARS', '🇦🇷', '2023-08-22 19:22:54', '2023-08-22 19:22:54'),
(11, 'AS', 'ASM', 'American Samoa', '1684', 'Pago Pago', 'United States Dollar', '$', 'USD', '🇦🇸', '2023-08-22 19:22:56', '2023-08-22 19:22:56'),
(12, 'AT', 'AUT', 'Austria', '43', 'Vienna', 'Euro', '€', 'EUR', '🇦🇹', '2023-08-22 19:22:57', '2023-08-22 19:22:57'),
(13, 'AU', 'AUS', 'Australia', '61', 'Canberra', 'Australian Dollar', '$', 'AUD', '🇦🇺', '2023-08-22 19:22:59', '2023-08-22 19:22:59'),
(14, 'AW', 'ABW', 'Aruba', '297', 'Oranjestad', 'Aruban Florin', 'ƒ', 'AWG', '🇦🇼', '2023-08-22 19:23:02', '2023-08-22 19:23:02'),
(15, 'AX', 'ALA', 'Aland', '358', 'Mariehamn', 'Euro', '€', 'EUR', '🇦🇽', '2023-08-22 19:23:03', '2023-08-22 19:23:03'),
(16, 'AZ', 'AZE', 'Azerbaijan', '994', 'Baku', 'Azerbaijani Manat', 'm', 'AZN', '🇦🇿', '2023-08-22 19:23:04', '2023-08-22 19:23:04'),
(17, 'BA', 'BIH', 'Bosnia and Herzegovina', '387', 'Sarajevo', 'Bosnia And Herzegovina Convertible Mark', 'KM', 'BAM', '🇧🇦', '2023-08-22 19:23:05', '2023-08-22 19:23:05'),
(18, 'BB', 'BRB', 'Barbados', '1246', 'Bridgetown', 'Barbadian Dollar', 'Bds$', 'BBD', '🇧🇧', '2023-08-22 19:23:06', '2023-08-22 19:23:06'),
(19, 'BD', 'BGD', 'Bangladesh', '880', 'Dhaka', 'Bangladeshi Taka', '৳', 'BDT', '🇧🇩', '2023-08-22 19:23:07', '2023-08-22 19:23:07'),
(20, 'BE', 'BEL', 'Belgium', '32', 'Brussels', 'Euro', '€', 'EUR', '🇧🇪', '2023-08-22 19:23:08', '2023-08-22 19:23:08'),
(21, 'BF', 'BFA', 'Burkina Faso', '226', 'Ouagadougou', 'West African Cfa Franc', 'CFA', 'XOF', '🇧🇫', '2023-08-22 19:23:09', '2023-08-22 19:23:09'),
(22, 'BG', 'BGR', 'Bulgaria', '359', 'Sofia', 'Bulgarian Lev', 'Лв.', 'BGN', '🇧🇬', '2023-08-22 19:23:10', '2023-08-22 19:23:10'),
(23, 'BH', 'BHR', 'Bahrain', '973', 'Manama', 'Bahraini Dinar', '.د.ب', 'BHD', '🇧🇭', '2023-08-22 19:23:11', '2023-08-22 19:23:11'),
(24, 'BI', 'BDI', 'Burundi', '257', 'Bujumbura', 'Burundian Franc', 'FBu', 'BIF', '🇧🇮', '2023-08-22 19:23:12', '2023-08-22 19:23:12'),
(25, 'BJ', 'BEN', 'Benin', '229', 'Porto-Novo', 'West African Cfa Franc', 'CFA', 'XOF', '🇧🇯', '2023-08-22 19:23:14', '2023-08-22 19:23:14'),
(26, 'BL', 'BLM', 'Saint Barthelemy', '590', 'Gustavia', 'Euro', '€', 'EUR', '🇧🇱', '2023-08-22 19:23:16', '2023-08-22 19:23:16'),
(27, 'BM', 'BMU', 'Bermuda', '1441', 'Hamilton', 'Bermudian Dollar', '$', 'BMD', '🇧🇲', '2023-08-22 19:23:17', '2023-08-22 19:23:17'),
(28, 'BN', 'BRN', 'Brunei', '673', 'Bandar Seri Begawan', 'Brunei Dollar', 'B$', 'BND', '🇧🇳', '2023-08-22 19:23:18', '2023-08-22 19:23:18'),
(29, 'BO', 'BOL', 'Bolivia', '591', 'Sucre', 'Bolivian Boliviano', 'Bs.', 'BOB,BOV', '🇧🇴', '2023-08-22 19:23:19', '2023-08-22 19:23:19'),
(30, 'BQ', 'BES', 'Bonaire', '5997', 'Kralendijk', 'United States Dollar', '$', 'USD', '🇧🇶', '2023-08-22 19:23:20', '2023-08-22 19:23:20'),
(31, 'BR', 'BRA', 'Brazil', '55', 'Brasília', 'Brazilian Real', 'R$', 'BRL', '🇧🇷', '2023-08-22 19:23:21', '2023-08-22 19:23:21'),
(32, 'BS', 'BHS', 'Bahamas', '1242', 'Nassau', 'Bahamian Dollar', 'B$', 'BSD', '🇧🇸', '2023-08-22 19:23:25', '2023-08-22 19:23:25'),
(33, 'BT', 'BTN', 'Bhutan', '975', 'Thimphu', 'Bhutanese Ngultrum', 'Nu.', 'BTN,INR', '🇧🇹', '2023-08-22 19:23:26', '2023-08-22 19:23:26'),
(34, 'BV', 'BVT', 'Bouvet Island', '47', '', 'Krone', 'kr', 'NOK', '🇧🇻', '2023-08-22 19:23:27', '2023-08-22 19:23:27'),
(35, 'BW', 'BWA', 'Botswana', '267', 'Gaborone', 'Botswana Pula', 'P', 'BWP', '🇧🇼', '2023-08-22 19:23:28', '2023-08-22 19:23:28'),
(36, 'BY', 'BLR', 'Belarus', '375', 'Minsk', 'Belarusian Ruble', 'Br', 'BYN', '🇧🇾', '2023-08-22 19:23:29', '2023-08-22 19:23:29'),
(37, 'BZ', 'BLZ', 'Belize', '501', 'Belmopan', 'Belize Dollar', '$', 'BZD', '🇧🇿', '2023-08-22 19:23:30', '2023-08-22 19:23:30'),
(38, 'CA', 'CAN', 'Canada', '1', 'Ottawa', 'Canadian Dollar', '$', 'CAD', '🇨🇦', '2023-08-22 19:23:31', '2023-08-22 19:23:31'),
(39, 'CC', 'CCK', 'Cocos (Keeling) Islands', '61', 'West Island', 'Australian Dollar', '$', 'AUD', '🇨🇨', '2023-08-22 19:23:33', '2023-08-22 19:23:33'),
(40, 'CD', 'COD', 'Democratic Republic of the Congo', '243', 'Kinshasa', 'Congolese Franc', 'FC', 'CDF', '🇨🇩', '2023-08-22 19:23:34', '2023-08-22 19:23:34'),
(41, 'CF', 'CAF', 'Central African Republic', '236', 'Bangui', 'Central African Cfa Franc', 'FCFA', 'XAF', '🇨🇫', '2023-08-22 19:23:35', '2023-08-22 19:23:35'),
(42, 'CG', 'COG', 'Republic of the Congo', '242', 'Brazzaville', 'Central African Cfa Franc', 'FC', 'XAF', '🇨🇬', '2023-08-22 19:23:36', '2023-08-22 19:23:36'),
(43, 'CH', 'CHE', 'Switzerland', '41', 'Bern', 'Swiss Franc', 'CHf', 'CHF,CHE,CHW', '🇨🇭', '2023-08-22 19:23:37', '2023-08-22 19:23:37'),
(44, 'CI', 'CIV', 'Ivory Coast', '225', 'Yamoussoukro', 'West African Cfa Franc', 'CFA', 'XOF', '🇨🇮', '2023-08-22 19:23:39', '2023-08-22 19:23:39'),
(45, 'CK', 'COK', 'Cook Islands', '682', 'Avarua', 'New Zealand Dollar', '$', 'NZD', '🇨🇰', '2023-08-22 19:23:40', '2023-08-22 19:23:40'),
(46, 'CL', 'CHL', 'Chile', '56', 'Santiago', 'Chilean Peso', '$', 'CLP,CLF', '🇨🇱', '2023-08-22 19:23:41', '2023-08-22 19:23:41'),
(47, 'CM', 'CMR', 'Cameroon', '237', 'Yaoundé', 'Central African Cfa Franc', 'FCFA', 'XAF', '🇨🇲', '2023-08-22 19:23:42', '2023-08-22 19:23:42'),
(48, 'CN', 'CHN', 'China', '86', 'Beijing', 'Chinese Yuan', '¥', 'CNY', '🇨🇳', '2023-08-22 19:23:43', '2023-08-22 19:23:43'),
(49, 'CO', 'COL', 'Colombia', '57', 'Bogotá', 'Colombian Peso', '$', 'COP', '🇨🇴', '2023-08-22 19:23:45', '2023-08-22 19:23:45'),
(50, 'CR', 'CRI', 'Costa Rica', '506', 'San José', 'Costa Rican Colón', '₡', 'CRC', '🇨🇷', '2023-08-22 19:23:46', '2023-08-22 19:23:46'),
(51, 'CU', 'CUB', 'Cuba', '53', 'Havana', 'Cuban Convertible Peso', '$', 'CUC,CUP', '🇨🇺', '2023-08-22 19:23:47', '2023-08-22 19:23:47'),
(52, 'CV', 'CPV', 'Cape Verde', '238', 'Praia', 'Cape Verdean Escudo', '$', 'CVE', '🇨🇻', '2023-08-22 19:23:48', '2023-08-22 19:23:48'),
(53, 'CW', 'CUW', 'Curacao', '5999', 'Willemstad', 'Netherlands Antillean Guilder', 'ƒ', 'ANG', '🇨🇼', '2023-08-22 19:23:49', '2023-08-22 19:23:49'),
(54, 'CX', 'CXR', 'Christmas Island', '61', 'Flying Fish Cove', 'Australian Dollar', '$', 'AUD', '🇨🇽', '2023-08-22 19:23:50', '2023-08-22 19:23:50'),
(55, 'CY', 'CYP', 'Cyprus', '357', 'Nicosia', 'Euro', '€', 'EUR', '🇨🇾', '2023-08-22 19:23:50', '2023-08-22 19:23:50'),
(56, 'CZ', 'CZE', 'Czech Republic', '420', 'Prague', 'Czech Koruna', 'Kč', 'CZK', '🇨🇿', '2023-08-22 19:23:51', '2023-08-22 19:23:51'),
(57, 'DE', 'DEU', 'Germany', '49', 'Berlin', 'Euro', '€', 'EUR', '🇩🇪', '2023-08-22 19:23:53', '2023-08-22 19:23:53'),
(58, 'DJ', 'DJI', 'Djibouti', '253', 'Djibouti', 'Djiboutian Franc', 'Fdj', 'DJF', '🇩🇯', '2023-08-22 19:23:57', '2023-08-22 19:23:57'),
(59, 'DK', 'DNK', 'Denmark', '45', 'Copenhagen', 'Krone', 'Kr.', 'DKK', '🇩🇰', '2023-08-22 19:23:58', '2023-08-22 19:23:58'),
(60, 'DM', 'DMA', 'Dominica', '1767', 'Roseau', 'Eastern Caribbean Dollar', '$', 'XCD', '🇩🇲', '2023-08-22 19:24:00', '2023-08-22 19:24:00'),
(61, 'DO', 'DOM', 'Dominican Republic', '1809,1829,1849', 'Santo Domingo', 'Dominican Peso', '$', 'DOP', '🇩🇴', '2023-08-22 19:24:00', '2023-08-22 19:24:00'),
(62, 'DZ', 'DZA', 'Algeria', '213', 'Algiers', 'Algerian Dinar', 'دج', 'DZD', '🇩🇿', '2023-08-22 19:24:02', '2023-08-22 19:24:02'),
(63, 'EC', 'ECU', 'Ecuador', '593', 'Quito', 'United States Dollar', '$', 'USD', '🇪🇨', '2023-08-22 19:24:03', '2023-08-22 19:24:03'),
(64, 'EE', 'EST', 'Estonia', '372', 'Tallinn', 'Euro', '€', 'EUR', '🇪🇪', '2023-08-22 19:24:04', '2023-08-22 19:24:04'),
(65, 'EG', 'EGY', 'Egypt', '20', 'Cairo', 'Egyptian Pound', 'ج.م', 'EGP', '🇪🇬', '2023-08-22 19:24:05', '2023-08-22 19:24:05'),
(66, 'EH', 'ESH', 'Western Sahara', '212', 'El Aaiún', 'Moroccan Dirham', 'MAD', 'MAD,DZD,MRU', '🇪🇭', '2023-08-22 19:24:06', '2023-08-22 19:24:06'),
(67, 'ER', 'ERI', 'Eritrea', '291', 'Asmara', 'Eritrean Nakfa', 'Nfk', 'ERN', '🇪🇷', '2023-08-22 19:24:07', '2023-08-22 19:24:07'),
(68, 'ES', 'ESP', 'Spain', '34', 'Madrid', 'Euro', '€', 'EUR', '🇪🇸', '2023-08-22 19:24:07', '2023-08-22 19:24:07'),
(69, 'ET', 'ETH', 'Ethiopia', '251', 'Addis Ababa', 'Ethiopian Birr', 'Nkf', 'ETB', '🇪🇹', '2023-08-22 19:24:15', '2023-08-22 19:24:15'),
(70, 'FI', 'FIN', 'Finland', '358', 'Helsinki', 'Euro', '€', 'EUR', '🇫🇮', '2023-08-22 19:24:16', '2023-08-22 19:24:16'),
(71, 'FJ', 'FJI', 'Fiji', '679', 'Suva', 'Fijian Dollar', 'FJ$', 'FJD', '🇫🇯', '2023-08-22 19:24:18', '2023-08-22 19:24:18'),
(72, 'FK', 'FLK', 'Falkland Islands', '500', 'Stanley', 'Falkland Islands Pound', '£', 'FKP', '🇫🇰', '2023-08-22 19:24:19', '2023-08-22 19:24:19'),
(73, 'FM', 'FSM', 'Micronesia', '691', 'Palikir', 'United States Dollar', '$', 'USD', '🇫🇲', '2023-08-22 19:24:19', '2023-08-22 19:24:19'),
(74, 'FO', 'FRO', 'Faroe Islands', '298', 'Tórshavn', 'Krone', 'Kr.', 'DKK', '🇫🇴', '2023-08-22 19:24:20', '2023-08-22 19:24:20'),
(75, 'FR', 'FRA', 'France', '33', 'Paris', 'Euro', '€', 'EUR', '🇫🇷', '2023-08-22 19:24:21', '2023-08-22 19:24:21'),
(76, 'GA', 'GAB', 'Gabon', '241', 'Libreville', 'Central African Cfa Franc', 'FCFA', 'XAF', '🇬🇦', '2023-08-22 19:24:26', '2023-08-22 19:24:26'),
(77, 'GB', 'GBR', 'United Kingdom', '44', 'London', 'Pound Sterling', '£', 'GBP', '🇬🇧', '2023-08-22 19:24:27', '2023-08-22 19:24:27'),
(78, 'GD', 'GRD', 'Grenada', '1473', 'St. George\'s', 'Eastern Caribbean Dollar', '$', 'XCD', '🇬🇩', '2023-08-22 19:24:31', '2023-08-22 19:24:31'),
(79, 'GE', 'GEO', 'Georgia', '995', 'Tbilisi', 'Lari', 'ლ', 'GEL', '🇬🇪', '2023-08-22 19:24:32', '2023-08-22 19:24:32'),
(80, 'GF', 'GUF', 'French Guiana', '594', 'Cayenne', 'Euro', '€', 'EUR', '🇬🇫', '2023-08-22 19:24:33', '2023-08-22 19:24:33'),
(81, 'GG', 'GGY', 'Guernsey', '44', 'St. Peter Port', 'Pound Sterling', '£', 'GBP', '🇬🇬', '2023-08-22 19:24:34', '2023-08-22 19:24:34'),
(82, 'GH', 'GHA', 'Ghana', '233', 'Accra', 'Ghanaian Cedi', 'GH₵', 'GHS', '🇬🇭', '2023-08-22 19:24:35', '2023-08-22 19:24:35'),
(83, 'GI', 'GIB', 'Gibraltar', '350', 'Gibraltar', 'Gibraltar Pound', '£', 'GIP', '🇬🇮', '2023-08-22 19:24:36', '2023-08-22 19:24:36'),
(84, 'GL', 'GRL', 'Greenland', '299', 'Nuuk', 'Krone', 'Kr.', 'DKK', '🇬🇱', '2023-08-22 19:24:37', '2023-08-22 19:24:37'),
(85, 'GM', 'GMB', 'Gambia', '220', 'Banjul', 'Dalasi', 'D', 'GMD', '🇬🇲', '2023-08-22 19:24:38', '2023-08-22 19:24:38'),
(86, 'GN', 'GIN', 'Guinea', '224', 'Conakry', 'Guinean Franc', 'FG', 'GNF', '🇬🇳', '2023-08-22 19:24:39', '2023-08-22 19:24:39'),
(87, 'GP', 'GLP', 'Guadeloupe', '590', 'Basse-Terre', 'Euro', '€', 'EUR', '🇬🇵', '2023-08-22 19:24:41', '2023-08-22 19:24:41'),
(88, 'GQ', 'GNQ', 'Equatorial Guinea', '240', 'Malabo', 'Central African Cfa Franc', 'FCFA', 'XAF', '🇬🇶', '2023-08-22 19:24:41', '2023-08-22 19:24:41'),
(89, 'GR', 'GRC', 'Greece', '30', 'Athens', 'Euro', '€', 'EUR', '🇬🇷', '2023-08-22 19:24:42', '2023-08-22 19:24:42'),
(90, 'GS', 'SGS', 'South Georgia and the South Sandwich Islands', '500', 'King Edward Point', 'Pound Sterling', '£', 'GBP', '🇬🇸', '2023-08-22 19:24:44', '2023-08-22 19:24:44'),
(91, 'GT', 'GTM', 'Guatemala', '502', 'Guatemala City', 'Guatemalan Quetzal', 'Q', 'GTQ', '🇬🇹', '2023-08-22 19:24:45', '2023-08-22 19:24:45'),
(92, 'GU', 'GUM', 'Guam', '1671', 'Hagåtña', 'United States Dollar', '$', 'USD', '🇬🇺', '2023-08-22 19:24:46', '2023-08-22 19:24:46'),
(93, 'GW', 'GNB', 'Guinea-Bissau', '245', 'Bissau', 'West African Cfa Franc', 'CFA', 'XOF', '🇬🇼', '2023-08-22 19:24:47', '2023-08-22 19:24:47'),
(94, 'GY', 'GUY', 'Guyana', '592', 'Georgetown', 'Guyanese Dollar', '$', 'GYD', '🇬🇾', '2023-08-22 19:24:48', '2023-08-22 19:24:48'),
(95, 'HK', 'HKG', 'Hong Kong', '852', 'City of Victoria', 'Hong Kong Dollar', '$', 'HKD', '🇭🇰', '2023-08-22 19:24:49', '2023-08-22 19:24:49'),
(96, 'HM', 'HMD', 'Heard Island and McDonald Islands', '61', '', 'Australian Dollar', '$', 'AUD', '🇭🇲', '2023-08-22 19:24:50', '2023-08-22 19:24:50'),
(97, 'HN', 'HND', 'Honduras', '504', 'Tegucigalpa', 'Honduran Lempira', 'L', 'HNL', '🇭🇳', '2023-08-22 19:24:50', '2023-08-22 19:24:50'),
(98, 'HR', 'HRV', 'Croatia', '385', 'Zagreb', 'Croatian Kuna', 'kn', 'HRK', '🇭🇷', '2023-08-22 19:24:52', '2023-08-22 19:24:52'),
(99, 'HT', 'HTI', 'Haiti', '509', 'Port-au-Prince', 'Haitian Gourde', 'G', 'HTG,USD', '🇭🇹', '2023-08-22 19:24:54', '2023-08-22 19:24:54'),
(100, 'HU', 'HUN', 'Hungary', '36', 'Budapest', 'Hungarian Forint', 'Ft', 'HUF', '🇭🇺', '2023-08-22 19:24:55', '2023-08-22 19:24:55'),
(101, 'ID', 'IDN', 'Indonesia', '62', 'Jakarta', 'Indonesian Rupiah', 'Rp', 'IDR', '🇮🇩', '2023-08-22 19:24:56', '2023-08-22 19:24:56'),
(102, 'IE', 'IRL', 'Ireland', '353', 'Dublin', 'Euro', '€', 'EUR', '🇮🇪', '2023-08-22 19:24:58', '2023-08-22 19:24:58'),
(103, 'IL', 'ISR', 'Israel', '972', 'Jerusalem', 'Israeli New Shekel', '₪', 'ILS', '🇮🇱', '2023-08-22 19:25:00', '2023-08-22 19:25:00'),
(104, 'IM', 'IMN', 'Isle of Man', '44', 'Douglas', 'Pound Sterling', '£', 'GBP', '🇮🇲', '2023-08-22 19:25:04', '2023-08-22 19:25:04'),
(105, 'IN', 'IND', 'India', '91', 'New Delhi', 'Indian Rupee', '₹', 'INR', '🇮🇳', '2023-08-22 19:25:06', '2023-08-22 19:25:06'),
(106, 'IO', 'IOT', 'British Indian Ocean Territory', '246', 'Diego Garcia', 'United States Dollar', '$', 'USD', '🇮🇴', '2023-08-22 19:25:12', '2023-08-22 19:25:12'),
(107, 'IQ', 'IRQ', 'Iraq', '964', 'Baghdad', 'Iraqi Dinar', 'د.ع', 'IQD', '🇮🇶', '2023-08-22 19:25:14', '2023-08-22 19:25:14'),
(108, 'IR', 'IRN', 'Iran', '98', 'Tehran', 'Iranian Rial', '﷼', 'IRR', '🇮🇷', '2023-08-22 19:25:16', '2023-08-22 19:25:16'),
(109, 'IS', 'ISL', 'Iceland', '354', 'Reykjavik', 'Icelandic Króna', 'kr', 'ISK', '🇮🇸', '2023-08-22 19:25:21', '2023-08-22 19:25:21'),
(110, 'IT', 'ITA', 'Italy', '39', 'Rome', 'Euro', '€', 'EUR', '🇮🇹', '2023-08-22 19:25:25', '2023-08-22 19:25:25'),
(111, 'JE', 'JEY', 'Jersey', '44', 'Saint Helier', 'Pound Sterling', '£', 'GBP', '🇯🇪', '2023-08-22 19:25:49', '2023-08-22 19:25:49'),
(112, 'JM', 'JAM', 'Jamaica', '1876', 'Kingston', 'Jamaican Dollar', 'J$', 'JMD', '🇯🇲', '2023-08-22 19:25:51', '2023-08-22 19:25:51'),
(113, 'JO', 'JOR', 'Jordan', '962', 'Amman', 'Jordanian Dinar', 'ا.د', 'JOD', '🇯🇴', '2023-08-22 19:25:55', '2023-08-22 19:25:55'),
(114, 'JP', 'JPN', 'Japan', '81', 'Tokyo', 'Japanese Yen', '¥', 'JPY', '🇯🇵', '2023-08-22 19:25:57', '2023-08-22 19:25:57'),
(115, 'KE', 'KEN', 'Kenya', '254', 'Nairobi', 'Kenyan Shilling', 'KSh', 'KES', '🇰🇪', '2023-08-22 19:26:04', '2023-08-22 19:26:04'),
(116, 'KG', 'KGZ', 'Kyrgyzstan', '996', 'Bishkek', 'Kyrgyzstani Som', 'лв', 'KGS', '🇰🇬', '2023-08-22 19:26:10', '2023-08-22 19:26:10'),
(117, 'KH', 'KHM', 'Cambodia', '855', 'Phnom Penh', 'Cambodian Riel', 'KHR', 'KHR', '🇰🇭', '2023-08-22 19:26:12', '2023-08-22 19:26:12'),
(118, 'KI', 'KIR', 'Kiribati', '686', 'South Tarawa', 'Australian Dollar', '$', 'AUD', '🇰🇮', '2023-08-22 19:26:14', '2023-08-22 19:26:14'),
(119, 'KM', 'COM', 'Comoros', '269', 'Moroni', 'Comorian Franc', 'CF', 'KMF', '🇰🇲', '2023-08-22 19:26:15', '2023-08-22 19:26:15'),
(120, 'KN', 'KNA', 'Saint Kitts and Nevis', '1869', 'Basseterre', 'Eastern Caribbean Dollar', '$', 'XCD', '🇰🇳', '2023-08-22 19:26:16', '2023-08-22 19:26:16'),
(121, 'KP', 'PRK', 'North Korea', '850', 'Pyongyang', 'North Korean Won', '₩', 'KPW', '🇰🇵', '2023-08-22 19:26:19', '2023-08-22 19:26:19'),
(122, 'KR', 'KOR', 'South Korea', '82', 'Seoul', 'South Korean Won', '₩', 'KRW', '🇰🇷', '2023-08-22 19:26:20', '2023-08-22 19:26:20'),
(123, 'KW', 'KWT', 'Kuwait', '965', 'Kuwait City', 'Kuwaiti Dinar', 'ك.د', 'KWD', '🇰🇼', '2023-08-22 19:26:21', '2023-08-22 19:26:21'),
(124, 'KY', 'CYM', 'Cayman Islands', '1345', 'George Town', 'Cayman Islands Dollar', '$', 'KYD', '🇰🇾', '2023-08-22 19:26:23', '2023-08-22 19:26:23'),
(125, 'KZ', 'KAZ', 'Kazakhstan', '76,77', 'Astana', 'Kazakhstani Tenge', 'лв', 'KZT', '🇰🇿', '2023-08-22 19:26:25', '2023-08-22 19:26:25'),
(126, 'LA', 'LAO', 'Laos', '856', 'Vientiane', 'Lao Kip', '₭', 'LAK', '🇱🇦', '2023-08-22 19:26:26', '2023-08-22 19:26:26'),
(127, 'LB', 'LBN', 'Lebanon', '961', 'Beirut', 'Lebanese Pound', '£', 'LBP', '🇱🇧', '2023-08-22 19:26:28', '2023-08-22 19:26:28'),
(128, 'LC', 'LCA', 'Saint Lucia', '1758', 'Castries', 'Eastern Caribbean Dollar', '$', 'XCD', '🇱🇨', '2023-08-22 19:26:33', '2023-08-22 19:26:33'),
(129, 'LI', 'LIE', 'Liechtenstein', '423', 'Vaduz', 'Swiss Franc', 'CHf', 'CHF', '🇱🇮', '2023-08-22 19:26:37', '2023-08-22 19:26:37'),
(130, 'LK', 'LKA', 'Sri Lanka', '94', 'Colombo', 'Sri Lankan Rupee', 'Rs', 'LKR', '🇱🇰', '2023-08-22 19:26:38', '2023-08-22 19:26:38'),
(131, 'LR', 'LBR', 'Liberia', '231', 'Monrovia', 'Liberian Dollar', '$', 'LRD', '🇱🇷', '2023-08-22 19:26:39', '2023-08-22 19:26:39'),
(132, 'LS', 'LSO', 'Lesotho', '266', 'Maseru', 'Lesotho Loti', 'L', 'LSL,ZAR', '🇱🇸', '2023-08-22 19:26:40', '2023-08-22 19:26:40'),
(133, 'LT', 'LTU', 'Lithuania', '370', 'Vilnius', 'Euro', '€', 'EUR', '🇱🇹', '2023-08-22 19:26:40', '2023-08-22 19:26:40'),
(134, 'LU', 'LUX', 'Luxembourg', '352', 'Luxembourg', 'Euro', '€', 'EUR', '🇱🇺', '2023-08-22 19:26:41', '2023-08-22 19:26:41'),
(135, 'LV', 'LVA', 'Latvia', '371', 'Riga', 'Euro', '€', 'EUR', '🇱🇻', '2023-08-22 19:26:43', '2023-08-22 19:26:43'),
(136, 'LY', 'LBY', 'Libya', '218', 'Tripoli', 'Libyan Dinar', 'د.ل', 'LYD', '🇱🇾', '2023-08-22 19:26:44', '2023-08-22 19:26:44'),
(137, 'MA', 'MAR', 'Morocco', '212', 'Rabat', 'Moroccan Dirham', 'DH', 'MAD', '🇲🇦', '2023-08-22 19:26:45', '2023-08-22 19:26:45'),
(138, 'MC', 'MCO', 'Monaco', '377', 'Monaco', 'Euro', '€', 'EUR', '🇲🇨', '2023-08-22 19:26:46', '2023-08-22 19:26:46'),
(139, 'MD', 'MDA', 'Moldova', '373', 'Chișinău', 'Moldovan Leu', 'L', 'MDL', '🇲🇩', '2023-08-22 19:26:47', '2023-08-22 19:26:47'),
(140, 'ME', 'MNE', 'Montenegro', '382', 'Podgorica', 'Euro', '€', 'EUR', '🇲🇪', '2023-08-22 19:26:48', '2023-08-22 19:26:48'),
(141, 'MF', 'MAF', 'Saint Martin (French part)', '590', 'Marigot', 'Euro', '€', 'EUR', '🇲🇫', '2023-08-22 19:26:49', '2023-08-22 19:26:49'),
(142, 'SX', 'SXM', 'Sint Maarten (Dutch part)', '1721', 'Philipsburg', 'Netherlands Antillean Guilder', 'ƒ', 'ANG', '🇸🇽', '2023-08-22 19:26:50', '2023-08-22 19:26:50'),
(143, 'MG', 'MDG', 'Madagascar', '261', 'Antananarivo', 'Malagasy Ariary', 'Ar', 'MGA', '🇲🇬', '2023-08-22 19:26:51', '2023-08-22 19:26:51'),
(144, 'MH', 'MHL', 'Marshall Islands', '692', 'Majuro', 'United States Dollar', '$', 'USD', '🇲🇭', '2023-08-22 19:26:51', '2023-08-22 19:26:51'),
(145, 'MK', 'MKD', 'North Macedonia', '389', 'Skopje', 'Denar', 'ден', 'MKD', '🇲🇰', '2023-08-22 19:26:52', '2023-08-22 19:26:52'),
(146, 'ML', 'MLI', 'Mali', '223', 'Bamako', 'West African Cfa Franc', 'CFA', 'XOF', '🇲🇱', '2023-08-22 19:26:53', '2023-08-22 19:26:53'),
(147, 'MM', 'MMR', 'Myanmar (Burma)', '95', 'Naypyidaw', 'Burmese Kyat', 'K', 'MMK', '🇲🇲', '2023-08-22 19:26:54', '2023-08-22 19:26:54'),
(148, 'MN', 'MNG', 'Mongolia', '976', 'Ulan Bator', 'Mongolian Tögrög', '₮', 'MNT', '🇲🇳', '2023-08-22 19:26:55', '2023-08-22 19:26:55'),
(149, 'MO', 'MAC', 'Macao', '853', '', 'Macanese Pataca', '$', 'MOP', '🇲🇴', '2023-08-22 19:26:56', '2023-08-22 19:26:56'),
(150, 'MP', 'MNP', 'Northern Mariana Islands', '1670', 'Saipan', 'United States Dollar', '$', 'USD', '🇲🇵', '2023-08-22 19:26:57', '2023-08-22 19:26:57'),
(151, 'MQ', 'MTQ', 'Martinique', '596', 'Fort-de-France', 'Euro', '€', 'EUR', '🇲🇶', '2023-08-22 19:26:58', '2023-08-22 19:26:58'),
(152, 'MR', 'MRT', 'Mauritania', '222', 'Nouakchott', 'Mauritanian Ouguiya', 'MRU', 'MRU', '🇲🇷', '2023-08-22 19:26:58', '2023-08-22 19:26:58'),
(153, 'MS', 'MSR', 'Montserrat', '1664', 'Plymouth', 'Eastern Caribbean Dollar', '$', 'XCD', '🇲🇸', '2023-08-22 19:26:59', '2023-08-22 19:26:59'),
(154, 'MT', 'MLT', 'Malta', '356', 'Valletta', 'Euro', '€', 'EUR', '🇲🇹', '2023-08-22 19:27:00', '2023-08-22 19:27:00'),
(155, 'MU', 'MUS', 'Mauritius', '230', 'Port Louis', 'Mauritian Rupee', '₨', 'MUR', '🇲🇺', '2023-08-22 19:27:01', '2023-08-22 19:27:01'),
(156, 'MV', 'MDV', 'Maldives', '960', 'Malé', 'Maldivian Rufiyaa', 'Rf', 'MVR', '🇲🇻', '2023-08-22 19:27:02', '2023-08-22 19:27:02'),
(157, 'MW', 'MWI', 'Malawi', '265', 'Lilongwe', 'Malawian Kwacha', 'MK', 'MWK', '🇲🇼', '2023-08-22 19:27:03', '2023-08-22 19:27:03'),
(158, 'MX', 'MEX', 'Mexico', '52', 'Mexico City', 'Mexican Peso', '$', 'MXN', '🇲🇽', '2023-08-22 19:27:04', '2023-08-22 19:27:04'),
(159, 'MY', 'MYS', 'Malaysia', '60', 'Kuala Lumpur', 'Malaysian Ringgit', 'RM', 'MYR', '🇲🇾', '2023-08-22 19:27:09', '2023-08-22 19:27:09'),
(160, 'MZ', 'MOZ', 'Mozambique', '258', 'Maputo', 'Mozambican Metical', 'MT', 'MZN', '🇲🇿', '2023-08-22 19:27:10', '2023-08-22 19:27:10'),
(161, 'NA', 'NAM', 'Namibia', '264', 'Windhoek', 'Namibian Dollar', '$', 'NAD,ZAR', '🇳🇦', '2023-08-22 19:27:11', '2023-08-22 19:27:11'),
(162, 'NC', 'NCL', 'New Caledonia', '687', 'Nouméa', 'Cfp Franc', '₣', 'XPF', '🇳🇨', '2023-08-22 19:27:12', '2023-08-22 19:27:12'),
(163, 'NE', 'NER', 'Niger', '227', 'Niamey', 'West African Cfa Franc', 'CFA', 'XOF', '🇳🇪', '2023-08-22 19:27:13', '2023-08-22 19:27:13'),
(164, 'NF', 'NFK', 'Norfolk Island', '672', 'Kingston', 'Australian Dollar', '$', 'AUD', '🇳🇫', '2023-08-22 19:27:14', '2023-08-22 19:27:14'),
(165, 'NG', 'NGA', 'Nigeria', '234', 'Abuja', 'Nigerian Naira', '₦', 'NGN', '🇳🇬', '2023-08-22 19:27:15', '2023-08-22 19:27:15'),
(166, 'NI', 'NIC', 'Nicaragua', '505', 'Managua', 'Nicaraguan Córdoba', 'C$', 'NIO', '🇳🇮', '2023-08-22 19:27:16', '2023-08-22 19:27:16'),
(167, 'NL', 'NLD', 'Netherlands', '31', 'Amsterdam', 'Euro', '€', 'EUR', '🇳🇱', '2023-08-22 19:27:17', '2023-08-22 19:27:17'),
(168, 'NO', 'NOR', 'Norway', '47', 'Oslo', 'Krone', 'kr', 'NOK', '🇳🇴', '2023-08-22 19:27:19', '2023-08-22 19:27:19'),
(169, 'NP', 'NPL', 'Nepal', '977', 'Kathmandu', 'Nepalese Rupee', '₨', 'NPR', '🇳🇵', '2023-08-22 19:27:21', '2023-08-22 19:27:21'),
(170, 'NR', 'NRU', 'Nauru', '674', 'Yaren', 'Australian Dollar', '$', 'AUD', '🇳🇷', '2023-08-22 19:27:22', '2023-08-22 19:27:22'),
(171, 'NU', 'NIU', 'Niue', '683', 'Alofi', 'New Zealand Dollar', '$', 'NZD', '🇳🇺', '2023-08-22 19:27:22', '2023-08-22 19:27:22'),
(172, 'NZ', 'NZL', 'New Zealand', '64', 'Wellington', 'New Zealand Dollar', '$', 'NZD', '🇳🇿', '2023-08-22 19:27:23', '2023-08-22 19:27:23'),
(173, 'OM', 'OMN', 'Oman', '968', 'Muscat', 'Omani Rial', '.ع.ر', 'OMR', '🇴🇲', '2023-08-22 19:27:24', '2023-08-22 19:27:24'),
(174, 'PA', 'PAN', 'Panama', '507', 'Panama City', 'Panamanian Balboa', 'B/.', 'PAB,USD', '🇵🇦', '2023-08-22 19:27:25', '2023-08-22 19:27:25'),
(175, 'PE', 'PER', 'Peru', '51', 'Lima', 'Peruvian Sol', 'S/.', 'PEN', '🇵🇪', '2023-08-22 19:27:27', '2023-08-22 19:27:27'),
(176, 'PF', 'PYF', 'French Polynesia', '689', 'Papeetē', 'Cfp Franc', '₣', 'XPF', '🇵🇫', '2023-08-22 19:27:29', '2023-08-22 19:27:29'),
(177, 'PG', 'PNG', 'Papua New Guinea', '675', 'Port Moresby', 'Papua New Guinean Kina', 'K', 'PGK', '🇵🇬', '2023-08-22 19:27:29', '2023-08-22 19:27:29'),
(178, 'PH', 'PHL', 'Philippines', '63', 'Manila', 'Philippine Peso', '₱', 'PHP', '🇵🇭', '2023-08-22 19:27:30', '2023-08-22 19:27:30'),
(179, 'PK', 'PAK', 'Pakistan', '92', 'Islamabad', 'Pakistani Rupee', '₨', 'PKR', '🇵🇰', '2023-08-22 19:27:36', '2023-08-22 19:27:36'),
(180, 'PL', 'POL', 'Poland', '48', 'Warsaw', 'Polish Złoty', 'zł', 'PLN', '🇵🇱', '2023-08-22 19:27:37', '2023-08-22 19:27:37'),
(181, 'PM', 'SPM', 'Saint Pierre and Miquelon', '508', 'Saint-Pierre', 'Euro', '€', 'EUR', '🇵🇲', '2023-08-22 19:27:40', '2023-08-22 19:27:40'),
(182, 'PN', 'PCN', 'Pitcairn Islands', '64', 'Adamstown', 'New Zealand Dollar', '$', 'NZD', '🇵🇳', '2023-08-22 19:27:40', '2023-08-22 19:27:40'),
(183, 'PR', 'PRI', 'Puerto Rico', '1787,1939', 'San Juan', 'United States Dollar', '$', 'USD', '🇵🇷', '2023-08-22 19:27:41', '2023-08-22 19:27:41'),
(184, 'PS', 'PSE', 'Palestine', '970', 'Ramallah', 'Israeli New Shekel', '₪', 'ILS', '🇵🇸', '2023-08-22 19:27:42', '2023-08-22 19:27:42'),
(185, 'PT', 'PRT', 'Portugal', '351', 'Lisbon', 'Euro', '€', 'EUR', '🇵🇹', '2023-08-22 19:27:43', '2023-08-22 19:27:43'),
(186, 'PW', 'PLW', 'Palau', '680', 'Ngerulmud', 'United States Dollar', '$', 'USD', '🇵🇼', '2023-08-22 19:27:44', '2023-08-22 19:27:44'),
(187, 'PY', 'PRY', 'Paraguay', '595', 'Asunción', 'Paraguayan Guaraní', '₲', 'PYG', '🇵🇾', '2023-08-22 19:27:45', '2023-08-22 19:27:45'),
(188, 'QA', 'QAT', 'Qatar', '974', 'Doha', 'Qatari Riyal', 'ق.ر', 'QAR', '🇶🇦', '2023-08-22 19:27:46', '2023-08-22 19:27:46'),
(189, 'RE', 'REU', 'Reunion', '262', 'Saint-Denis', 'Euro', '€', 'EUR', '🇷🇪', '2023-08-22 19:27:47', '2023-08-22 19:27:47'),
(190, 'RO', 'ROU', 'Romania', '40', 'Bucharest', 'Romanian Leu', 'lei', 'RON', '🇷🇴', '2023-08-22 19:27:48', '2023-08-22 19:27:48'),
(191, 'RS', 'SRB', 'Serbia', '381', 'Belgrade', 'Serbian Dinar', 'din', 'RSD', '🇷🇸', '2023-08-22 19:27:52', '2023-08-22 19:27:52'),
(192, 'RU', 'RUS', 'Russia', '7', 'Moscow', 'Russian Ruble', '₽', 'RUB', '🇷🇺', '2023-08-22 19:27:53', '2023-08-22 19:27:53'),
(193, 'RW', 'RWA', 'Rwanda', '250', 'Kigali', 'Rwandan Franc', 'FRw', 'RWF', '🇷🇼', '2023-08-22 19:27:57', '2023-08-22 19:27:57'),
(194, 'SA', 'SAU', 'Saudi Arabia', '966', 'Riyadh', 'Saudi Riyal', '﷼', 'SAR', '🇸🇦', '2023-08-22 19:27:57', '2023-08-22 19:27:57'),
(195, 'SB', 'SLB', 'Solomon Islands', '677', 'Honiara', 'Solomon Islands Dollar', 'Si$', 'SBD', '🇸🇧', '2023-08-22 19:27:58', '2023-08-22 19:27:58'),
(196, 'SC', 'SYC', 'Seychelles', '248', 'Victoria', 'Seychellois Rupee', 'SRe', 'SCR', '🇸🇨', '2023-08-22 19:27:59', '2023-08-22 19:27:59'),
(197, 'SD', 'SDN', 'Sudan', '249', 'Khartoum', 'Sudanese Pound', '.س.ج', 'SDG', '🇸🇩', '2023-08-22 19:28:00', '2023-08-22 19:28:00'),
(198, 'SE', 'SWE', 'Sweden', '46', 'Stockholm', 'Swedish Krona', 'kr', 'SEK', '🇸🇪', '2023-08-22 19:28:01', '2023-08-22 19:28:01'),
(199, 'SG', 'SGP', 'Singapore', '65', 'Singapore', 'Singapore Dollar', '$', 'SGD', '🇸🇬', '2023-08-22 19:28:03', '2023-08-22 19:28:03'),
(200, 'SH', 'SHN', 'Saint Helena', '290', 'Jamestown', 'Saint Helena Pound', '£', 'SHP', '🇸🇭', '2023-08-22 19:28:03', '2023-08-22 19:28:03'),
(201, 'SI', 'SVN', 'Slovenia', '386', 'Ljubljana', 'Euro', '€', 'EUR', '🇸🇮', '2023-08-22 19:28:04', '2023-08-22 19:28:04'),
(202, 'SJ', 'SJM', 'Svalbard and Jan Mayen', '4779', 'Longyearbyen', 'Krone', 'kr', 'NOK', '🇸🇯', '2023-08-22 19:28:06', '2023-08-22 19:28:06'),
(203, 'SK', 'SVK', 'Slovakia', '421', 'Bratislava', 'Euro', '€', 'EUR', '🇸🇰', '2023-08-22 19:28:07', '2023-08-22 19:28:07'),
(204, 'SL', 'SLE', 'Sierra Leone', '232', 'Freetown', 'Sierra Leonean Leone', 'Le', 'SLL', '🇸🇱', '2023-08-22 19:28:08', '2023-08-22 19:28:08'),
(205, 'SM', 'SMR', 'San Marino', '378', 'City of San Marino', 'Euro', '€', 'EUR', '🇸🇲', '2023-08-22 19:28:09', '2023-08-22 19:28:09'),
(206, 'SN', 'SEN', 'Senegal', '221', 'Dakar', 'West African Cfa Franc', 'CFA', 'XOF', '🇸🇳', '2023-08-22 19:28:10', '2023-08-22 19:28:10'),
(207, 'SO', 'SOM', 'Somalia', '252', 'Mogadishu', 'Somali Shilling', 'Sh.so.', 'SOS', '🇸🇴', '2023-08-22 19:28:11', '2023-08-22 19:28:11'),
(208, 'SR', 'SUR', 'Suriname', '597', 'Paramaribo', 'Surinamese Dollar', '$', 'SRD', '🇸🇷', '2023-08-22 19:28:11', '2023-08-22 19:28:11'),
(209, 'SS', 'SSD', 'South Sudan', '211', 'Juba', 'South Sudanese Pound', '£', 'SSP', '🇸🇸', '2023-08-22 19:28:12', '2023-08-22 19:28:12'),
(210, 'ST', 'STP', 'Sao Tome and Principe', '239', 'São Tomé', 'São Tomé And Príncipe Dobra', 'Db', 'STN', '🇸🇹', '2023-08-22 19:28:13', '2023-08-22 19:28:13'),
(211, 'SV', 'SLV', 'El Salvador', '503', 'San Salvador', 'Salvadoran Colón', '$', 'SVC,USD', '🇸🇻', '2023-08-22 19:28:14', '2023-08-22 19:28:14'),
(212, 'SY', 'SYR', 'Syria', '963', 'Damascus', 'Syrian Pound', 'LS', 'SYP', '🇸🇾', '2023-08-22 19:28:15', '2023-08-22 19:28:15'),
(213, 'SZ', 'SWZ', 'Swaziland', '268', 'Lobamba', 'Swazi Lilangeni', 'E', 'SZL', '🇸🇿', '2023-08-22 19:28:16', '2023-08-22 19:28:16'),
(214, 'TC', 'TCA', 'Turks and Caicos Islands', '1649', 'Cockburn Town', 'United States Dollar', '$', 'USD', '🇹🇨', '2023-08-22 19:28:17', '2023-08-22 19:28:17'),
(215, 'TD', 'TCD', 'Chad', '235', 'N\'Djamena', 'Central African Cfa Franc', 'FCFA', 'XAF', '🇹🇩', '2023-08-22 19:28:18', '2023-08-22 19:28:18'),
(216, 'TF', 'ATF', 'French Southern Territories', '262', 'Port-aux-Français', 'Euro', '€', 'EUR', '🇹🇫', '2023-08-22 19:28:19', '2023-08-22 19:28:19'),
(217, 'TG', 'TGO', 'Togo', '228', 'Lomé', 'West African Cfa Franc', 'CFA', 'XOF', '🇹🇬', '2023-08-22 19:28:20', '2023-08-22 19:28:20'),
(218, 'TH', 'THA', 'Thailand', '66', 'Bangkok', 'Thai Baht', '฿', 'THB', '🇹🇭', '2023-08-22 19:28:21', '2023-08-22 19:28:21'),
(219, 'TJ', 'TJK', 'Tajikistan', '992', 'Dushanbe', 'Tajikistani Somoni', 'SM', 'TJS', '🇹🇯', '2023-08-22 19:28:23', '2023-08-22 19:28:23'),
(220, 'TK', 'TKL', 'Tokelau', '690', 'Fakaofo', 'New Zealand Dollar', '$', 'NZD', '🇹🇰', '2023-08-22 19:28:24', '2023-08-22 19:28:24'),
(221, 'TL', 'TLS', 'East Timor', '670', 'Dili', 'United States Dollar', '$', 'USD', '🇹🇱', '2023-08-22 19:28:24', '2023-08-22 19:28:24'),
(222, 'TM', 'TKM', 'Turkmenistan', '993', 'Ashgabat', 'Turkmenistan Manat', 'T', 'TMT', '🇹🇲', '2023-08-22 19:28:25', '2023-08-22 19:28:25'),
(223, 'TN', 'TUN', 'Tunisia', '216', 'Tunis', 'Tunisian Dinar', 'ت.د', 'TND', '🇹🇳', '2023-08-22 19:28:26', '2023-08-22 19:28:26'),
(224, 'TO', 'TON', 'Tonga', '676', 'Nuku\'alofa', 'Tongan PaʻAnga', '$', 'TOP', '🇹🇴', '2023-08-22 19:28:27', '2023-08-22 19:28:27'),
(225, 'TR', 'TUR', 'Turkey', '90', 'Ankara', 'Turkish Lira', '₺', 'TRY', '🇹🇷', '2023-08-22 19:28:28', '2023-08-22 19:28:28'),
(226, 'TT', 'TTO', 'Trinidad and Tobago', '1868', 'Port of Spain', 'Trinidad And Tobago Dollar', '$', 'TTD', '🇹🇹', '2023-08-22 19:28:30', '2023-08-22 19:28:30'),
(227, 'TV', 'TUV', 'Tuvalu', '688', 'Funafuti', 'Australian Dollar', '$', 'AUD', '🇹🇻', '2023-08-22 19:28:31', '2023-08-22 19:28:31'),
(228, 'TW', 'TWN', 'Taiwan', '886', 'Taipei', 'New Taiwan Dollar', '$', 'TWD', '🇹🇼', '2023-08-22 19:28:32', '2023-08-22 19:28:32'),
(229, 'TZ', 'TZA', 'Tanzania', '255', 'Dodoma', 'Tanzanian Shilling', 'TSh', 'TZS', '🇹🇿', '2023-08-22 19:28:33', '2023-08-22 19:28:33'),
(230, 'UA', 'UKR', 'Ukraine', '380', 'Kyiv', 'Ukrainian Hryvnia', '₴', 'UAH', '🇺🇦', '2023-08-22 19:28:34', '2023-08-22 19:28:34'),
(231, 'UG', 'UGA', 'Uganda', '256', 'Kampala', 'Ugandan Shilling', 'USh', 'UGX', '🇺🇬', '2023-08-22 19:28:37', '2023-08-22 19:28:37'),
(232, 'UM', 'UMI', 'U.S. Minor Outlying Islands', '1', '', 'United States Dollar', '$', 'USD', '🇺🇲', '2023-08-22 19:28:38', '2023-08-22 19:28:38'),
(233, 'US', 'USA', 'United States', '1', 'Washington D.C.', 'United States Dollar', '$', 'USD,USN,USS', '🇺🇸', '2023-08-22 19:28:38', '2023-08-22 19:28:38'),
(234, 'UY', 'URY', 'Uruguay', '598', 'Montevideo', 'Uruguayan Peso', '$', 'UYI,UYU', '🇺🇾', '2023-08-22 19:30:28', '2023-08-22 19:30:28'),
(235, 'UZ', 'UZB', 'Uzbekistan', '998', 'Tashkent', 'Uzbekistani SoʻM', 'лв', 'UZS', '🇺🇿', '2023-08-22 19:30:29', '2023-08-22 19:30:29'),
(236, 'VA', 'VAT', 'Vatican City', '379', 'Vatican City', 'Euro', '€', 'EUR', '🇻🇦', '2023-08-22 19:30:30', '2023-08-22 19:30:30'),
(237, 'VC', 'VCT', 'Saint Vincent and the Grenadines', '1784', 'Kingstown', 'Eastern Caribbean Dollar', '$', 'XCD', '🇻🇨', '2023-08-22 19:30:31', '2023-08-22 19:30:31'),
(238, 'VE', 'VEN', 'Venezuela', '58', 'Caracas', 'Venezuelan Bolívar Soberano', 'Bs', 'VES', '🇻🇪', '2023-08-22 19:30:32', '2023-08-22 19:30:32'),
(239, 'VG', 'VGB', 'British Virgin Islands', '1284', 'Road Town', 'United States Dollar', '$', 'USD', '🇻🇬', '2023-08-22 19:30:33', '2023-08-22 19:30:33'),
(240, 'VI', 'VIR', 'U.S. Virgin Islands', '1340', 'Charlotte Amalie', 'United States Dollar', '$', 'USD', '🇻🇮', '2023-08-22 19:30:33', '2023-08-22 19:30:33'),
(241, 'VN', 'VNM', 'Vietnam', '84', 'Hanoi', 'Vietnamese Đồng', '₫', 'VND', '🇻🇳', '2023-08-22 19:30:34', '2023-08-22 19:30:34'),
(242, 'VU', 'VUT', 'Vanuatu', '678', 'Port Vila', 'Vanuatu Vatu', 'VT', 'VUV', '🇻🇺', '2023-08-22 19:30:36', '2023-08-22 19:30:36'),
(243, 'WF', 'WLF', 'Wallis and Futuna', '681', 'Mata-Utu', 'Cfp Franc', '₣', 'XPF', '🇼🇫', '2023-08-22 19:30:37', '2023-08-22 19:30:37'),
(244, 'WS', 'WSM', 'Samoa', '685', 'Apia', 'Samoan Tālā', 'SAT', 'WST', '🇼🇸', '2023-08-22 19:30:37', '2023-08-22 19:30:37'),
(245, 'XK', 'XKX', 'Kosovo', '377,381,383,386', 'Pristina', 'Euro', '€', 'EUR', '🇽🇰', '2023-08-22 19:30:38', '2023-08-22 19:30:38'),
(246, 'YE', 'YEM', 'Yemen', '967', 'Sana\'a', 'Yemeni Rial', '﷼', 'YER', '🇾🇪', '2023-08-22 19:30:39', '2023-08-22 19:30:39'),
(247, 'YT', 'MYT', 'Mayotte', '262', 'Mamoudzou', 'Euro', '€', 'EUR', '🇾🇹', '2023-08-22 19:30:40', '2023-08-22 19:30:40'),
(248, 'ZA', 'ZAF', 'South Africa', '27', 'Pretoria', 'South African Rand', 'R', 'ZAR', '🇿🇦', '2023-08-22 19:30:41', '2023-08-22 19:30:41'),
(249, 'ZM', 'ZMB', 'Zambia', '260', 'Lusaka', 'Zambian Kwacha', 'ZK', 'ZMW', '🇿🇲', '2023-08-22 19:30:42', '2023-08-22 19:30:42'),
(250, 'ZW', 'ZWE', 'Zimbabwe', '263', 'Harare', 'United States Dollar', '$', 'USD,ZAR,BWP,GBP,AUD,CNY,INR,JPY', '🇿🇼', '2023-08-22 19:30:43', '2023-08-22 19:30:43');


/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;