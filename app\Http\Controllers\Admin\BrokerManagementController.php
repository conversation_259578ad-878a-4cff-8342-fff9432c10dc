<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BrokerInterest;
use App\Models\User;
use App\Models\Ad;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class BrokerManagementController extends Controller
{
    /**
     * عرض جميع اهتمامات المندوبين
     */
    public function interests(Request $request): View
    {
        $query = BrokerInterest::with(['broker', 'ad.category', 'ad.user'])
            ->orderBy('created_at', 'desc');

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب المندوب
        if ($request->filled('broker_id')) {
            $query->where('broker_id', $request->broker_id);
        }

        // فلترة حسب الإعلان
        if ($request->filled('ad_search')) {
            $query->whereHas('ad', function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->ad_search . '%');
            });
        }

        // فلترة حسب إدخال الكود
        if ($request->filled('code_entered')) {
            $query->where('code_entered', $request->code_entered == '1');
        }

        $interests = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_interests' => BrokerInterest::count(),
            'active_interests' => BrokerInterest::where('status', 'active')->count(),
            'code_entered' => BrokerInterest::where('code_entered', true)->count(),
            'total_points_paid' => BrokerInterest::sum('points_paid'),
            'total_points_earned' => BrokerInterest::sum('points_earned'),
        ];

        // قائمة المندوبين للفلترة
        $brokers = User::where('is_broker', 1)->select('id', 'name')->get();

        return view('admin.broker-management.interests', compact('interests', 'stats', 'brokers'));
    }

    /**
     * عرض المندوبين الذين دخلوا الكود بنجاح
     */
    public function successfulCodes(Request $request): View
    {
        $query = BrokerInterest::with(['broker', 'ad.category', 'ad.user'])
            ->where('code_entered', true)
            ->orderBy('code_entered_at', 'desc');

        // فلترة حسب المندوب
        if ($request->filled('broker_id')) {
            $query->where('broker_id', $request->broker_id);
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('code_entered_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('code_entered_at', '<=', $request->date_to);
        }

        $successfulCodes = $query->paginate(20);

        // إحصائيات
        $stats = [
            'total_successful' => BrokerInterest::where('code_entered', true)->count(),
            'total_points_earned' => BrokerInterest::where('code_entered', true)->sum('points_earned'),
            'today_successful' => BrokerInterest::where('code_entered', true)
                ->whereDate('code_entered_at', today())->count(),
            'this_month_successful' => BrokerInterest::where('code_entered', true)
                ->whereMonth('code_entered_at', now()->month)->count(),
        ];

        // قائمة المندوبين للفلترة
        $brokers = User::where('is_broker', 1)->select('id', 'name')->get();

        return view('admin.broker-management.successful-codes', compact('successfulCodes', 'stats', 'brokers'));
    }

    /**
     * مراقبة نقاط المندوبين
     */
    public function pointsMonitoring(Request $request): View
    {
        $query = User::where('is_broker', 1)
            ->withCount([
                'brokerInterests',
                'brokerInterests as successful_codes' => function($q) {
                    $q->where('code_entered', true);
                }
            ])
            ->withSum('brokerInterests', 'points_paid')
            ->withSum('brokerInterests', 'points_earned')
            ->orderBy('points', 'desc');

        // فلترة حسب النقاط
        if ($request->filled('min_points')) {
            $query->where('points', '>=', $request->min_points);
        }

        if ($request->filled('max_points')) {
            $query->where('points', '<=', $request->max_points);
        }

        // فلترة حسب الاسم
        if ($request->filled('name_search')) {
            $query->where('name', 'like', '%' . $request->name_search . '%');
        }

        $brokers = $query->paginate(20);

        // إحصائيات عامة
        $stats = [
            'total_brokers' => User::where('is_broker', 1)->count(),
            'total_points_in_system' => User::where('is_broker', 1)->sum('points'),
            'total_points_spent' => BrokerInterest::sum('points_paid'),
            'total_points_earned' => BrokerInterest::sum('points_earned'),
            'suspicious_accounts' => User::where('is_broker', 1)
                ->where('points', '>', 50000)->count(), // حسابات مشبوهة
        ];

        return view('admin.broker-management.points-monitoring', compact('brokers', 'stats'));
    }

    /**
     * تعديل نقاط مندوب
     */
    public function updatePoints(Request $request, User $broker)
    {
        $request->validate([
            'points' => 'required|integer|min:0',
            'reason' => 'required|string|max:255',
        ]);

        $oldPoints = $broker->points;
        $broker->update(['points' => $request->points]);

        // تسجيل العملية في لوج
        Log::info('تم تعديل نقاط المندوب', [
            'broker_id' => $broker->id,
            'broker_name' => $broker->name,
            'admin_id' => auth()->id(),
            'admin_name' => auth()->user()->name,
            'old_points' => $oldPoints,
            'new_points' => $request->points,
            'reason' => $request->reason,
            'timestamp' => now(),
        ]);

        return back()->with('success', 'تم تحديث النقاط بنجاح');
    }

    /**
     * حذف اهتمام مندوب
     */
    public function deleteInterest(BrokerInterest $interest)
    {
        // إرجاع النقاط للمندوب
        $interest->broker->increment('points', $interest->points_paid);

        // تسجيل العملية
        activity()
            ->performedOn($interest)
            ->causedBy(auth()->user())
            ->withProperties([
                'broker_name' => $interest->broker->name,
                'ad_title' => $interest->ad->title,
                'points_refunded' => $interest->points_paid,
            ])
            ->log('تم حذف اهتمام مندوب وإرجاع النقاط');

        $interest->delete();

        return back()->with('success', 'تم حذف الاهتمام وإرجاع النقاط بنجاح');
    }
}
