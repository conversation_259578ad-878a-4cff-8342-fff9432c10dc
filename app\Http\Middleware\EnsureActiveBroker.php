<?php

namespace App\Http\Middleware;

use App\Models\BrokerInterest;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureActiveBroker
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // التحقق من تسجيل الدخول
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً.');
        }

        // التحقق من أن المستخدم مندوب
        if (!($user->is_broker ?? false)) {
            return redirect()->route('home')->with('error', 'هذه الصفحة مخصصة للمندوبين فقط.');
        }

        // التحقق من نشاط المندوب (آخر كود ناجح خلال 30 يوم أو فترة سماح للمندوب الجديد)
        $isActiveBroker = false;

        if ($user->last_successful_code_at) {
            // لديه أكواد ناجحة - نحسب من آخر كود ناجح في جدول المستخدمين
            $daysSinceLastCode = $user->last_successful_code_at->diffInDays(now());
            $isActiveBroker = $daysSinceLastCode < 30;
        } else {
            // لا يوجد أكواد ناجحة - نتحقق من تاريخ تفعيل المندوب
            // المندوب الجديد له فترة سماح 30 يوم من تاريخ التفعيل
            // هذه الفترة لا تتجدد إلا بكود ناجح (ليس بالاهتمام)
            $brokerActivationDate = $user->broker_activated_at ?? $user->updated_at;
            $daysSinceBecameBroker = $brokerActivationDate->diffInDays(now());
            $isActiveBroker = $daysSinceBecameBroker <= 30;
        }

        if (!$isActiveBroker) {
            return redirect()->route('user.dashboard')->with('error', 'حسابك كمندوب غير نشط. يجب إكمال عملية بيع خلال آخر 30 يوم لاستعادة النشاط.');
        }

        return $next($request);
    }
}
