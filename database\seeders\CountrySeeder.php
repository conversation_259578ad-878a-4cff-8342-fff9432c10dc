<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class CountrySeeder extends Seeder
{
    public function run()
    {
        // جلب البيانات من الـ API
        $response = Http::get('https://restcountries.com/v3.1/all');  // الـ endpoint الخاص بالدول
        $countries = $response->json();  // تحويل الـ JSON إلى مصفوفة

        // إدخال البيانات في جدول 'countries'
         // إدخال البيانات في جدول 'countries'
            // إدخال البيانات في جدول 'countries'
        foreach ($countries as $country) {
            DB::table('countries')->insert([
                'name' => $country['name']['common'] ?? 'Unknown',  // لو مش موجود نكتب 'Unknown'
                'iso2' => $country['cca2'] ?? null,
                'iso3' => $country['cca3'] ?? null,
                'phone_code' => isset($country['idd']['root']) && isset($country['idd']['suffixes']) 
                    ? $country['idd']['root'] . ($country['idd']['suffixes'][0] ?? '')
                    : null,
                'capital' => $country['capital'][0] ?? null,
                'currency' => isset($country['currencies']) && !empty($country['currencies']) 
                    ? key($country['currencies']) 
                    : null,
                'currency_symbol' => isset($country['currencies']) && !empty($country['currencies']) 
                    ? current($country['currencies'])['symbol'] 
                    : null,
                'currency_code' => isset($country['currencies']) && !empty($country['currencies']) 
                    ? key($country['currencies']) 
                    : null,
                'emoji' => $country['flags']['png'] ?? null, // أو استخدم رمز الإيموجي الخاص بالدولة
            ]);
        }
    }
}
