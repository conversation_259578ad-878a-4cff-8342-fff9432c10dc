@extends('partials.admin')

@section('title', 'Categories Management')

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'categories'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Categories Management', 'hasBack' => true, 'backTitle' => 'Dashboard', 'backUrl' => route('admin.dashboard')])
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Categories Management</h3>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus"></i> Add Category
                    </button>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Slug</th>
                                    <th>Subcategories</th>
                                    <th>Attributes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($categories as $category)
                                    <tr>
                                        <td>{{ $category->id }}</td>
                                        <td>
                                            <strong>{{ $category->name }}</strong>
                                            @if($category->icon)
                                                <i class="ms-2 {{ $category->icon }}"></i>
                                            @endif
                                        </td>
                                        <td><code>{{ $category->slug }}</code></td>
                                        <td>
                                            @if($category->children->count() > 0)
                                                <span class="badge bg-info">{{ $category->children->count() }} subcategories</span>
                                                <button class="btn btn-sm btn-outline-primary ms-2" type="button" 
                                                        data-bs-toggle="collapse" data-bs-target="#subcategories-{{ $category->id }}">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                            @else
                                                <span class="text-muted">No subcategories</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $category->attributes->count() }} attributes</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editCategory({{ $category->id }}, '{{ $category->name }}', '{{ $category->description }}', '{{ $category->icon }}', '{{ $category->image }}')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-success" 
                                                        onclick="addSubcategory({{ $category->id }}, '{{ $category->name }}')">
                                                    <i class="fas fa-plus"></i> Sub
                                                </button>
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="manageAttributes({{ $category->id }}, '{{ $category->name }}')">
                                                    <i class="fas fa-cogs"></i> Attr
                                                </button>
                                                <form method="POST" action="{{ route('admin.categories.destroy', $category) }}" 
                                                      style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    
                                    <!-- Subcategories collapse -->
                                    @if($category->children->count() > 0)
                                        <tr>
                                            <td colspan="6" class="p-0">
                                                <div class="collapse" id="subcategories-{{ $category->id }}">
                                                    <div class="card card-body m-3">
                                                        <h6>Subcategories of {{ $category->name }}:</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>ID</th>
                                                                        <th>Name</th>
                                                                        <th>Slug</th>
                                                                        <th>Attributes</th>
                                                                        <th>Actions</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach($category->children as $subcategory)
                                                                        <tr>
                                                                            <td>{{ $subcategory->id }}</td>
                                                                            <td>{{ $subcategory->name }}</td>
                                                                            <td><code>{{ $subcategory->slug }}</code></td>
                                                                            <td>
                                                                                <span class="badge bg-secondary">{{ $subcategory->attributes->count() }} attributes</span>
                                                                            </td>
                                                                            <td>
                                                                                <div class="btn-group" role="group">
                                                                                    <button type="button" class="btn btn-sm btn-warning" 
                                                                                            onclick="editCategory({{ $subcategory->id }}, '{{ $subcategory->name }}', '{{ $subcategory->description }}', '{{ $subcategory->icon }}', '{{ $subcategory->image }}')">
                                                                                        <i class="fas fa-edit"></i>
                                                                                    </button>
                                                                                    <button type="button" class="btn btn-sm btn-info" 
                                                                                            onclick="manageAttributes({{ $subcategory->id }}, '{{ $subcategory->name }}')">
                                                                                        <i class="fas fa-cogs"></i> Attr
                                                                                    </button>
                                                                                    <form method="POST" action="{{ route('admin.categories.destroy', $subcategory) }}" 
                                                                                          style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                                                                        @csrf
                                                                                        @method('DELETE')
                                                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                                                            <i class="fas fa-trash"></i>
                                                                                        </button>
                                                                                    </form>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.categories.store') }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_name" class="form-label">Name *</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="add_icon" class="form-label">Icon Class</label>
                        <input type="text" class="form-control" id="add_icon" name="icon" placeholder="fas fa-car">
                    </div>
                    <div class="mb-3">
                        <label for="add_image" class="form-label">Image Path</label>
                        <input type="text" class="form-control" id="add_image" name="image" placeholder="assets/categories/bg/vehicles.png">
                    </div>
                    <input type="hidden" id="add_parent_id" name="parent_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="editCategoryForm">
                @csrf
                @method('PUT')
                <div class="modal-header">
                    <h5 class="modal-title">Edit Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_icon" class="form-label">Icon Class</label>
                        <input type="text" class="form-control" id="edit_icon" name="icon" placeholder="fas fa-car">
                    </div>
                    <div class="mb-3">
                        <label for="edit_image" class="form-label">Image Path</label>
                        <input type="text" class="form-control" id="edit_image" name="image" placeholder="assets/categories/bg/vehicles.png">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Attributes Management Modal -->
<div class="modal fade" id="attributesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="attributesModalTitle">Manage Attributes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Add New Attribute Form -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">Add New Attribute</h6>
                    </div>
                    <div class="card-body">
                        <form id="addAttributeForm">
                            @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="attr_name" class="form-label">Attribute Name *</label>
                                        <input type="text" class="form-control" id="attr_name" name="attribute_name" required>
                                        <small class="text-muted">e.g., brand, year, condition</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="attr_label" class="form-label">Attribute Label *</label>
                                        <input type="text" class="form-control" id="attr_label" name="attribute_label" required>
                                        <small class="text-muted">e.g., نوع السيارة, سنة الصنع</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="attr_type" class="form-label">Type *</label>
                                        <select class="form-control" id="attr_type" name="attribute_type" required>
                                            <option value="select">Select (Dropdown)</option>
                                            <option value="input">Input (Text)</option>
                                            <option value="textarea">Textarea</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="attr_sort" class="form-label">Sort Order</label>
                                        <input type="number" class="form-control" id="attr_sort" name="sort_order" value="0">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="attr_required" name="is_required">
                                            <label class="form-check-label" for="attr_required">Required</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3" id="optionsContainer" style="display: none;">
                                <label class="form-label">Options (for Select type)</label>
                                <div id="optionsList">
                                    <div class="input-group mb-2">
                                        <input type="text" class="form-control option-key" placeholder="Key (e.g., toyota)">
                                        <input type="text" class="form-control option-value" placeholder="Value (e.g., تويوتا)">
                                        <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addOption()">
                                    <i class="fas fa-plus"></i> Add Option
                                </button>
                            </div>
                            <button type="submit" class="btn btn-success">Add Attribute</button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="resetAttributeForm()">Reset</button>
                        </form>
                    </div>
                </div>

                <!-- Existing Attributes List -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Existing Attributes</h6>
                    </div>
                    <div class="card-body">
                        <div id="attributesList">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function editCategory(id, name, description, icon, image) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_description').value = description || '';
    document.getElementById('edit_icon').value = icon || '';
    document.getElementById('edit_image').value = image || '';

    document.getElementById('editCategoryForm').action = `/admin/categories/${id}`;

    new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
}

function addSubcategory(parentId, parentName) {
    document.getElementById('add_name').value = '';
    document.getElementById('add_description').value = '';
    document.getElementById('add_icon').value = '';
    document.getElementById('add_image').value = '';
    document.getElementById('add_parent_id').value = parentId;

    document.querySelector('#addCategoryModal .modal-title').textContent = `Add Subcategory to ${parentName}`;

    new bootstrap.Modal(document.getElementById('addCategoryModal')).show();
}

function manageAttributes(categoryId, categoryName) {
    document.getElementById('attributesModalTitle').textContent = `Manage Attributes for ${categoryName}`;
    document.getElementById('addAttributeForm').setAttribute('data-category-id', categoryId);

    // Load existing attributes
    loadCategoryAttributes(categoryId);

    new bootstrap.Modal(document.getElementById('attributesModal')).show();
}

function loadCategoryAttributes(categoryId) {
    console.log('Loading attributes for category:', categoryId);
    document.getElementById('attributesList').innerHTML = '<p class="text-muted">Loading attributes...</p>';

    fetch(`/admin/categories/${categoryId}/attributes`)
        .then(response => {
            console.log('Attributes response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Attributes loaded:', data);
            if (data.success) {
                displayAttributes(data.attributes);
            } else {
                console.error('Failed to load attributes:', data);
                document.getElementById('attributesList').innerHTML = '<p class="text-danger">Error loading attributes.</p>';
            }
        })
        .catch(error => {
            console.error('Error loading attributes:', error);
            document.getElementById('attributesList').innerHTML = '<p class="text-danger">Error loading attributes: ' + error.message + '</p>';
        });
}

function displayAttributes(attributes) {
    const container = document.getElementById('attributesList');

    // Store attributes data globally for edit function
    attributesData = attributes;
    console.log('Attributes data stored:', attributesData);

    if (attributes.length === 0) {
        container.innerHTML = '<p class="text-muted">No attributes found for this category.</p>';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-sm table-bordered">';
    html += '<thead><tr><th>Name</th><th>Label</th><th>Type</th><th>Required</th><th>Options</th><th>Actions</th></tr></thead><tbody>';

    attributes.forEach(attr => {
        html += `<tr>
            <td><code>${attr.attribute_name}</code></td>
            <td>${attr.attribute_label}</td>
            <td><span class="badge bg-info">${attr.attribute_type}</span></td>
            <td>${attr.is_required ? '<span class="badge bg-warning">Required</span>' : '<span class="badge bg-secondary">Optional</span>'}</td>
            <td>`;

        if (attr.attribute_options && Object.keys(attr.attribute_options).length > 0) {
            html += '<small>';
            Object.entries(attr.attribute_options).forEach(([key, value]) => {
                html += `<span class="badge bg-light text-dark me-1">${key}: ${value}</span>`;
            });
            html += '</small>';
        } else {
            html += '<span class="text-muted">-</span>';
        }

        html += `</td>
            <td>
                <button type="button" class="btn btn-sm btn-warning" onclick="editAttribute(${attr.id}, '${attr.attribute_name}', '${attr.attribute_label}', '${attr.attribute_type}', ${attr.is_required}, ${attr.sort_order})">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="deleteAttribute(${attr.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>`;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// Handle attribute type change
document.getElementById('attr_type').addEventListener('change', function() {
    const optionsContainer = document.getElementById('optionsContainer');
    if (this.value === 'select') {
        optionsContainer.style.display = 'block';
    } else {
        optionsContainer.style.display = 'none';
    }
});

function addOption() {
    const optionsList = document.getElementById('optionsList');
    const newOption = document.createElement('div');
    newOption.className = 'input-group mb-2';
    newOption.innerHTML = `
        <input type="text" class="form-control option-key" placeholder="Key (e.g., toyota)">
        <input type="text" class="form-control option-value" placeholder="Value (e.g., تويوتا)">
        <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    optionsList.appendChild(newOption);
}

function removeOption(button) {
    button.closest('.input-group').remove();
}

// Handle add/edit attribute form submission
document.getElementById('addAttributeForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const categoryId = this.getAttribute('data-category-id');
    const editId = this.getAttribute('data-edit-id');

    console.log('Form submission started', { categoryId, editId });

    // Collect form data
    const formData = {
        attribute_name: document.getElementById('attr_name').value.trim(),
        attribute_label: document.getElementById('attr_label').value.trim(),
        attribute_type: document.getElementById('attr_type').value,
        sort_order: document.getElementById('attr_sort').value || 0,
        is_required: document.getElementById('attr_required').checked ? 1 : 0
    };

    // Basic validation
    if (!formData.attribute_name) {
        alert('Attribute Name is required');
        return;
    }

    if (!formData.attribute_label) {
        alert('Attribute Label is required');
        return;
    }

    // Collect options if type is select
    if (formData.attribute_type === 'select') {
        const options = {};
        const optionKeys = document.querySelectorAll('.option-key');
        const optionValues = document.querySelectorAll('.option-value');

        for (let i = 0; i < optionKeys.length; i++) {
            const key = optionKeys[i].value.trim();
            const value = optionValues[i].value.trim();
            if (key && value) {
                options[key] = value;
            }
        }

        formData.attribute_options = JSON.stringify(options);
        console.log('Options collected:', options);
    }

    console.log('Form data prepared:', formData);

    // Determine URL and method based on edit mode
    let url, method;
    if (editId) {
        url = `/admin/attributes/${editId}`;
        method = 'PUT';
        formData._method = 'PUT';
    } else {
        url = `/admin/categories/${categoryId}/attributes`;
        method = 'POST';
    }

    console.log('Request details:', { url, method });

    // Convert to FormData for submission
    const submitData = new FormData();
    Object.keys(formData).forEach(key => {
        submitData.append(key, formData[key]);
    });

    // Submit via AJAX
    fetch(url, {
        method: 'POST', // Always POST for FormData with _method
        body: submitData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            // Reset form and reload attributes
            resetAttributeForm();
            loadCategoryAttributes(categoryId);

            // Show success message
            alert(editId ? 'Attribute updated successfully!' : 'Attribute added successfully!');
        } else {
            console.error('Server error:', data);
            alert('Error ' + (editId ? 'updating' : 'adding') + ' attribute: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Request error:', error);
        alert('Error ' + (editId ? 'updating' : 'adding') + ' attribute. Please check console for details.');
    });
});

// Store attributes data globally for easier access
let attributesData = [];

function editAttribute(id, name, label, type, isRequired, sortOrder) {
    console.log('Edit attribute called:', { id, name, label, type, isRequired, sortOrder });

    // Find the attribute data from the stored array
    const attr = attributesData.find(a => a.id === id);
    if (!attr) {
        console.error('Attribute not found:', id);
        alert('Error: Attribute data not found');
        return;
    }

    console.log('Found attribute:', attr);

    // Fill the form with existing data
    document.getElementById('attr_name').value = name;
    document.getElementById('attr_label').value = label;
    document.getElementById('attr_type').value = type;
    document.getElementById('attr_required').checked = isRequired;
    document.getElementById('attr_sort').value = sortOrder;

    // Handle options for select type
    if (type === 'select' && attr.attribute_options) {
        document.getElementById('optionsContainer').style.display = 'block';
        const optionsList = document.getElementById('optionsList');
        optionsList.innerHTML = '';

        console.log('Loading options:', attr.attribute_options);

        Object.entries(attr.attribute_options).forEach(([key, value]) => {
            const newOption = document.createElement('div');
            newOption.className = 'input-group mb-2';
            newOption.innerHTML = `
                <input type="text" class="form-control option-key" placeholder="Key" value="${key}">
                <input type="text" class="form-control option-value" placeholder="Value" value="${value}">
                <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            optionsList.appendChild(newOption);
        });
    } else {
        document.getElementById('optionsContainer').style.display = 'none';
    }

    // Trigger change event to show/hide options container
    document.getElementById('attr_type').dispatchEvent(new Event('change'));

    // Change form to edit mode
    const form = document.getElementById('addAttributeForm');
    form.setAttribute('data-edit-id', id);
    form.querySelector('button[type="submit"]').textContent = 'Update Attribute';
    form.querySelector('button[type="submit"]').className = 'btn btn-warning';

    console.log('Form updated for editing');
}

function deleteAttribute(id) {
    if (!confirm('Are you sure you want to delete this attribute?')) {
        return;
    }

    fetch(`/admin/attributes/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const categoryId = document.getElementById('addAttributeForm').getAttribute('data-category-id');
            loadCategoryAttributes(categoryId);
            alert('Attribute deleted successfully!');
        } else {
            alert('Error deleting attribute: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error deleting attribute. Please try again.');
    });
}

function resetAttributeForm() {
    const form = document.getElementById('addAttributeForm');
    form.reset();
    form.removeAttribute('data-edit-id');
    form.querySelector('button[type="submit"]').textContent = 'Add Attribute';
    form.querySelector('button[type="submit"]').className = 'btn btn-success';
    document.getElementById('optionsContainer').style.display = 'none';
    document.getElementById('optionsList').innerHTML = `
        <div class="input-group mb-2">
            <input type="text" class="form-control option-key" placeholder="Key (e.g., toyota)">
            <input type="text" class="form-control option-value" placeholder="Value (e.g., تويوتا)">
            <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
}

// Reset modal when closed
document.getElementById('addCategoryModal').addEventListener('hidden.bs.modal', function () {
    document.querySelector('#addCategoryModal .modal-title').textContent = 'Add New Category';
    document.getElementById('add_parent_id').value = '';
});
</script>
@endpush
