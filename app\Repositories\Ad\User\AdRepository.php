<?php

namespace App\Repositories\Ad\User;

use App\Abstracts\BaseCrudRepository;
use App\Models\Ad;
use App\Models\AdAttribute;
use App\Contracts\Repositories\AdRepositoryInterface;
use App\Enums\AdStatus;
use App\Enums\PriceRange;
use App\Enums\StorageDiskType;
use App\Exceptions\AdException;
use App\Models\ReportAd;
use App\Models\User;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\Country\CountryRepository;
use App\Traits\MediaHandler;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use HTMLPurifier;
use HTMLPurifier_Config;
use App\Helpers\SecurityHelper;
use App\Services\SponsoredAdService;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class AdRepository extends BaseCrudRepository implements AdRepositoryInterface
{
    use MediaHandler;

    protected SponsoredAdService $sponsoredAdService;

    public function __construct(Ad $model, SponsoredAdService $sponsoredAdService)
    {
        parent::__construct($model);
        $this->sponsoredAdService = $sponsoredAdService;
    }

    /**
     * Create an ad listing.
     * 
     * @param ?User $user|null
     * @param array $data
     * @return Ad
     */
    public function create(?User $user, array $data): Ad
    {
        return DB::transaction(function () use ($user, $data) {
            Log::info('Starting ad creation with media upload', [
                'user_id' => $user->id,
                'images_count' => count($data['images'] ?? [])
            ]);

            $ad = $this->store($user, $data);

            Log::info('Ad created, now uploading media', [
                'ad_id' => $ad->id,
                'images_count' => count($data['images'] ?? [])
            ]);

            $this->uploadMedia($ad, $data['images'] ?? [], StorageDiskType::LOCAL, 'ad', 1200, 900, $user);

            Log::info('Media upload completed', [
                'ad_id' => $ad->id,
                'media_count' => $ad->media()->count()
            ]);

            return $ad;
        });
    }

    /**
     * Get ad by slug with cache
     *
     * @param string $slug
     * @return \App\Models\Ad
     */
    public function getAd(string $slug): Ad
    {
        $cacheKey = "ad_details_{$slug}";

        return Cache::remember($cacheKey, now()->addMinutes(10), function () use ($slug) {
            return $this->model->with([
                'user:id,name,avatar,username,created_at,rank,is_trusted,is_broker,email',
                'user.receivedRatings',
                'media',
                'category:id,name,slug',
                'attributes.categoryAttribute',
                'bids',
                'bids.user:id,name,avatar,username',
                'relatedAds:id,title,slug,price',
                'relatedAds.media',
                'relatedAds.user:id,username,avatar',
                'highestBid:id,amount'
            ])
            ->where('status', AdStatus::PUBLISHED)
            ->where('slug', $slug)
            ->firstOr(function () {
                throw new AdException('Ad not found.');
            });
        });
    }

    /**
     * Get latest active|upcoming ads
     * 
     * @param int $limit
     * @param string $type = 'active' <active|upcoming>
     * @param array $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getLatestAds(int $limit = 10, string $type = 'active', array $filters = null): LengthAwarePaginator
    {
        return $this->model->query()->with(['user:id,name,avatar,username', 'media', 'category:id,name'])
            ->when($type === 'active', function ($query) {
                $query->active();
            }, function ($query) {
                $query->upcoming();
            })
            ->when($filters, function ($query) use ($filters) {
                $query->when(isset($filters['category']), function ($query) use ($filters) {
                    $query->where('category_id', app(CategoryRepository::class)->findBySlug($filters['category'])->id);
                })
                    ->when(isset($filters['country']), function ($query) use ($filters) {
                        $query->where('country_id', app(CountryRepository::class)->findByIso2Code($filters['country'])->id);
                    })
                    ->when(isset($filters['price_range']), function ($query) use ($filters) {
                        $query->whereBetween('price', PriceRange::range($filters['price_range']));
                    });
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit)
            ->appends([
                'category' => $filters['category'] ?? null,
                'country' => $filters['country'] ?? null,
                'price_range' =>  isset($filters['amount_from']) ? PriceRange::range($filters['price_range']) : null,
            ]);
    }

    /**
     * Get ads by user
     * 
     * @param \App\Models\User $user
     * @param int $limit
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getUserAds(User $user, int $limit = 10, array $filters = null): Collection|LengthAwarePaginator
    {
        return $this->model->query()->where('user_id', $user->id)->with(['media', 'latestAdminNote.admin'])
            ->when(isset($filters['status']), function ($query) use ($filters) {
                $status = $filters['status'];
                $query->$status();
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
    }

    /**
     * Get user ad by slug
     * 
     * @param \App\Models\User $user
     * @param string $slug
     * @return \App\Models\Ad
     */
    public function getUserAd(User $user, string $slug): Ad
    {
        return $this->model->query()->with(['user:id,name,avatar,username', 'media', 'category:id,name', 'subcategory:parent_id,id,name', 'bids', 'bids.user:id,name,avatar,username', 'country:id,name,iso2', 'state:id,name,code', 'city:id,name'])
            ->where('user_id', $user->id)
            ->where('slug', $slug)
            ->firstOr(function () {
                throw new AdException('Ad cannot be found, or it does not belong to you.');
            });
    }

    /**
     * Update an ad
     * 
     * @param \App\Models\User $user
     * @param string $ad
     * @param array $data
     * @return void
     */
    public function updateUserAd(User $user, string $ad, array $data): void
    {
        $ad = $this->model->where('user_id', $user->id)->where('slug', $ad)->firstOr(function () {
            throw new AdException('You cannot update this ad, or it does not belong to you.');
        });

        $ad->update([
            'title' => $data['title'] ?? $ad->title,
            'description' => isset($data['description']) ? SecurityHelper::cleanAdDescription($data['description']) : $ad->description,
            'price' => $data['price'] ?? $ad->price,
            'video_url' => $data['video_url'] ?? $ad->video_url,
            'seller_name' => $data['seller_name'] ?? $ad->seller_name,
            'seller_mobile' => $data['seller_mobile'] ?? $ad->seller_mobile,
            'seller_address' => $data['seller_address'] ?? $ad->seller_address,
        ]);
    }

    /**
     * Get ad by slug
     * 
     * @param string $slug
     * @return \App\Models\Ad
     */
    public function getReportAd(string $slug): Ad
    {
        return $this->model->query()
            ->whereNot('status', AdStatus::PENDING)
            ->where('slug', $slug)
            ->firstOr(function () {
                throw new AdException('Ad not be reported because it does not exist.');
            });
    }

    /**
     * Report an ad
     * 
     * @param \App\Models\User $user
     * @param string $slug
     * @param array $data
     * @return void
     */
    public function reportAd(string $slug, array $data): void
    {
        ReportAd::create([
            'ad_id' => $this->getReportAd($slug)->id,
            'reason' => $data['reason'],
            'description' => $data['description'],
            'email' => $data['email'],
        ]);
    }

    /**
     * Store an ad listing.
     *
     * @param ?User $user|null
     * @param array $data
     * @return Ad
     */
    public function store(User $user, array $data): Ad
    {
        return DB::transaction(function () use ($user, $data) {
            Log::info('Creating ad', [
                'user_id' => $user->id,
                'title' => $data['title'],
                'images_count' => count($data['images'] ?? [])
            ]);

            $category = app(CategoryRepository::class)->findBySlug($data['category']);
            $subcategory = app(CategoryRepository::class)->findBySlug($data['subcategory'] ?? null);

            // تنظيف الـ description بشكل آمن
            $cleaned = SecurityHelper::cleanAdDescription($data['description']);

            $ad = $this->model->create([
                'user_id' => $user->id,
                'category_id' => $category->id ?? null,
                'sub_category_id' => $subcategory->id ?? null,
                'title' => $data['title'],
                'description' => $cleaned,
                'price' => $data['price'],
                'unique_ad_code' => strtoupper(Str::random(15)),
                'country_id' => $user->country_id ?? null,
                'state_id' => $user->state_id ?? null,
                'city_id' => $user->city_id ?? null,
                'status' => AdStatus::PENDING,
                'seller_name' => $user->name,
                'seller_email' => $user->email,
                'seller_mobile' => $user->mobile,
                'seller_address' => $user->address,
                'Number_Ads' => $user->Number_Ads,
                'rank' => $user->rank,
                'is_trusted' => $user->is_trusted,
                'needs_brokering' => $data['needs_brokering'] ?? false,
                'broker_commission' => $data['broker_commission'] ?? 0,
                'expires_at' => now()->addDays(30),
            ]);

            // حفظ الخصائص إذا كانت موجودة
            $this->saveAdAttributes($ad, $data, $subcategory);

            // إنشاء سجل رعاية إذا كان الإعلان ممولاً
            if (isset($data['is_sponsored']) && $data['is_sponsored'] &&
                isset($data['sponsorship_cost']) && $data['sponsorship_cost'] >= 10) {

                $totalMinutes = \App\Models\SponsoredAd::calculateMinutes($data['sponsorship_cost']);

                \App\Models\SponsoredAd::create([
                    'ad_id' => $ad->id,
                    'cost' => $data['sponsorship_cost'],
                    'total_minutes' => $totalMinutes,
                    'status' => 'pending', // سيتم تفعيله عند موافقة الأدمن
                    'is_active' => false,
                ]);
            }

            // Clear cache after creating new ad
            $this->clearAdCache($ad->slug);

            return $ad;
        });
    }

    /**
     * Save ad attributes.
     *
     * @param Ad $ad
     * @param array $data
     * @param \App\Models\Category|null $subcategory
     * @return void
     */
    private function saveAdAttributes(Ad $ad, array $data, $subcategory): void
    {
        if (!$subcategory) {
            return;
        }

        $categoryAttributes = $subcategory->attributes;

        foreach ($categoryAttributes as $categoryAttribute) {
            $attributeName = $categoryAttribute->attribute_name;

            // التحقق من وجود قيمة لهذه الخاصية في البيانات المرسلة
            if (isset($data[$attributeName]) && !empty($data[$attributeName])) {
                AdAttribute::create([
                    'ad_id' => $ad->id,
                    'category_attribute_id' => $categoryAttribute->id,
                    'attribute_name' => $categoryAttribute->attribute_name,
                    'attribute_label' => $categoryAttribute->attribute_label,
                    'attribute_value' => $data[$attributeName],
                    'attribute_type' => $categoryAttribute->attribute_type,
                ]);
            }
        }
    }

    /**
     * Get latest ads with attributes and cache
     *
     * @param int $limit
     * @param string $status
     * @param array $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getLatestAdsWithAttributes(int $limit = 12, string $status = 'active', array $filters = []): LengthAwarePaginator
    {
        $cacheKey = "latest_ads_" . md5(serialize($filters)) . "_{$limit}_{$status}";

        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($limit, $status, $filters) {
            $query = $this->model->with([
                'user:id,name,avatar,username,rank,is_trusted',
                'media',
                'category:id,name,slug',
                'subCategory:id,name,slug,parent_id',
                'country:id,name,iso2',
                'attributes:id,ad_id,attribute_name,attribute_label,attribute_value',
                'sponsoredAd:id,ad_id,status,is_active,expires_at'
            ]);

            if ($status === 'active') {
                $query->where('status', AdStatus::PUBLISHED)
                      // استبعاد الإعلانات الممولة المعلقة من التبويب العادي
                      ->where(function($q) {
                          $q->whereDoesntHave('sponsoredAd')
                            ->orWhereHas('sponsoredAd', function($sponsoredQuery) {
                                $sponsoredQuery->where('status', '!=', 'pending');
                            });
                      });
            }

            // Search filter
            if (!empty($filters['search'])) {
                $searchTerm = $filters['search'];
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('title', 'like', "%{$searchTerm}%")
                      ->orWhere('description', 'like', "%{$searchTerm}%")
                      ->orWhereHas('attributes', function ($q) use ($searchTerm) {
                          $q->where('attribute_value', 'like', "%{$searchTerm}%");
                      });
                });
            }

            // Category filter
            if (!empty($filters['category'])) {
                $query->whereHas('category', function ($q) use ($filters) {
                    $q->where('slug', $filters['category']);
                });
            }

            // Subcategory filter
            if (!empty($filters['subcategory'])) {
                $query->whereHas('subCategory', function ($q) use ($filters) {
                    $q->where('slug', $filters['subcategory']);
                });
            }

            // Country filter
            if (!empty($filters['country'])) {
                $query->whereHas('country', function ($q) use ($filters) {
                    $q->where('iso2', $filters['country']);
                });
            }

            // Price range filter
            if (!empty($filters['price_range'])) {
                $range = PriceRange::range($filters['price_range']);
                $query->whereBetween('price', $range);
            }

            // Custom price filter
            if (!empty($filters['price_min'])) {
                $query->where('price', '>=', $filters['price_min']);
            }
            if (!empty($filters['price_max'])) {
                $query->where('price', '<=', $filters['price_max']);
            }

            // Attributes filter
            if (!empty($filters['attributes'])) {
                foreach ($filters['attributes'] as $attrName => $attrValue) {
                    if (!empty($attrValue)) {
                        $query->whereHas('attributes', function ($q) use ($attrName, $attrValue) {
                            $q->where('attribute_name', $attrName)
                              ->where('attribute_value', $attrValue);
                        });
                    }
                }
            }

            // Sorting
            $sort = $filters['sort'] ?? 'newest';
            switch ($sort) {
                case 'oldest':
                    $query->oldest();
                    break;
                case 'price_low':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price_high':
                    $query->orderBy('price', 'desc');
                    break;
                case 'popular':
                    $query->orderBy('views', 'desc');
                    break;
                default: // newest
                    $query->latest();
                    break;
            }

            // جلب الإعلانات العادية
            $regularAds = $query->get();

            // دمج الإعلانات الممولة مع العادية
            $mergedAds = $this->sponsoredAdService->mergeWithRegularAds($regularAds, $filters);

            // تحويل إلى paginator
            $currentPage = request()->get('page', 1);
            $perPage = $limit;
            $offset = ($currentPage - 1) * $perPage;

            $paginatedItems = $mergedAds->slice($offset, $perPage);
            $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                $paginatedItems,
                $mergedAds->count(),
                $perPage,
                $currentPage,
                [
                    'path' => request()->url(),
                    'pageName' => 'page',
                ]
            );

            return $paginator->appends($filters);
        });
    }

    /**
     * الحصول على الإعلانات الممولة المرتبطة بإعلان معين
     *
     * @param Ad $ad
     * @param int $limit
     * @return Collection
     */
    public function getRelatedSponsoredAds(Ad $ad, int $limit = 3): Collection
    {
        return $this->sponsoredAdService->getRelatedSponsoredAds($ad, $limit);
    }

    /**
     * Clear ad cache when ad is updated
     *
     * @param string $slug
     * @return void
     */
    public function clearAdCache(string $slug): void
    {
        Cache::forget("ad_details_{$slug}");
        Cache::flush(); // Clear all cache for simplicity - in production, be more specific
    }

    /**
     * Update an existing ad listing.
     *
     * @param Ad $ad
     * @param array $data
     * @return Ad
     */
    public function updateAd(Ad $ad, array $data): Ad
    {
        return DB::transaction(function () use ($ad, $data) {
            $category = app(CategoryRepository::class)->findBySlug($data['category']);
            $subcategory = app(CategoryRepository::class)->findBySlug($data['subcategory'] ?? null);

            // تنظيف الـ description بشكل آمن
            $cleaned = SecurityHelper::cleanAdDescription($data['description']);

            // تحديث بيانات الإعلان
            $ad->update([
                'title' => $data['title'],
                'description' => $cleaned,
                'price' => $data['price'],
                'category_id' => $subcategory ? $subcategory->id : $category->id,
                'is_negotiable' => isset($data['is_negotiable']) ? (bool) $data['is_negotiable'] : false,
                'mark_as_urgent' => isset($data['mark_as_urgent']) ? (bool) $data['mark_as_urgent'] : false,
                'needs_brokering' => $data['needs_brokering'] ?? false,
                'broker_commission' => $data['broker_commission'] ?? 0,
                'is_sponsored' => $data['is_sponsored'] ?? false,
                'sponsorship_cost' => $data['sponsorship_cost'] ?? 0,
                'status' => AdStatus::PENDING, // Set to pending for review
            ]);

            // حذف الخصائص القديمة
            $ad->attributes()->delete();

            // إضافة الخصائص الجديدة إذا كانت موجودة
            if (isset($data['attributes']) && is_array($data['attributes'])) {
                foreach ($data['attributes'] as $attributeName => $attributeValue) {
                    if (!empty($attributeValue)) {
                        AdAttribute::create([
                            'ad_id' => $ad->id,
                            'attribute_name' => $attributeName,
                            'attribute_value' => $attributeValue,
                        ]);
                    }
                }
            }

            // رفع الصور الجديدة إذا كانت موجودة
            if (isset($data['images']) && !empty($data['images'])) {
                Log::info('Updating ad images', [
                    'ad_id' => $ad->id,
                    'new_images_count' => count($data['images'])
                ]);

                // حذف الصور القديمة
                foreach ($ad->media as $media) {
                    $this->deleteMediaFile($media);
                }

                // رفع الصور الجديدة
                $this->uploadMedia($ad, $data['images'], StorageDiskType::LOCAL, 'ad', 1200, 900, $ad->user);

                Log::info('Ad images updated', [
                    'ad_id' => $ad->id,
                    'final_media_count' => $ad->fresh()->media()->count()
                ]);
            }

            // Clear cache for this ad
            Cache::forget("ad_details_{$ad->slug}");

            return $ad->fresh();
        });
    }

}
