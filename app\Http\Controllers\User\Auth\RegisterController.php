<?php

namespace App\Http\Controllers\User\Auth;

use App\Contracts\Repositories\AuthenticateRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;

class RegisterController extends Controller
{
    /**
     * Instantiate a new controller instance.
     * @param AuthenticateRepositoryInterface $repository
     */
    public function __construct(protected AuthenticateRepositoryInterface $repository)
    {
        $this->repository = $repository;
        $this->middleware('guest');
    }

    /**
     * Register a new user.
     * @param RegisterRequest $request
     * @return RedirectResponse
     */
    public function register(RegisterRequest $request): RedirectResponse
    {
        // إنشاء المستخدم باستخدام النظام الرسمي
        $user = $this->repository->registerWithLaravelVerification($request->validated());

        // تسجيل دخول المستخدم
        Auth::login($user);

        // إرسال إشعار التحقق من الإيميل (النظام الرسمي)
        event(new Registered($user));

        return redirect()->route('user.verification.notice')->with('success', 'Your account has been created successfully! Please verify your email to continue.');
    }
}
