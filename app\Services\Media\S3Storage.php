<?php

namespace App\Services\Media;

use App\Abstracts\BaseMediaStorageService;
use App\Contracts\Services\MediaStorageServiceInterface;
use App\Enums\StorageDiskType;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;

class S3Storage extends BaseMediaStorageService implements MediaStorageServiceInterface
{
    protected StorageDiskType $disk;

    public function __construct()
    {
        parent::__construct(StorageDiskType::S3);
    }

    /**
     * Resize image for s3 aws
     * 
     * @param UploadedFile $file
     * @param string $directory
     * @param int $width
     * @param int $height
     * @return void
     */
    protected function resizeImage(UploadedFile $file, string $directory, int $width, int $height): void
    {
        // رفع الملف بدون معالجة للسرعة وتجنب مشاكل ImageMagick/GD
        try {
            Storage::disk(strtolower($this->disk->label()))->put($file->getFilename(), file_get_contents($file->getRealPath()));
            Log::info('Image uploaded to S3 successfully', [
                'file' => $file->getClientOriginalName(),
                'size' => $file->getSize()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to upload image to S3', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName()
            ]);
            throw $e;
        }
    }
}
