<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Image Compression Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for advanced image compression settings
    | used throughout the application to optimize image file sizes.
    |
    */

    'default_driver' => env('IMAGE_COMPRESSION_DRIVER', 'advanced'),

    'drivers' => [
        'basic' => [
            'class' => \App\Services\SimpleFileCompressionService::class,
            'settings' => [
                'quality' => 75,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg',
                'max_file_size' => 500 * 1024, // 500KB
            ]
        ],

        'advanced' => [
            'class' => \App\Services\AdvancedImageOptimizer::class,
            'settings' => [
                'target_size_kb' => 200,
                'min_quality' => 30,
                'max_width' => 1000,
                'max_height' => 1000,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => true
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Type Specific Settings
    |--------------------------------------------------------------------------
    |
    | Define compression settings for different types of images used in the app
    |
    */

    'image_types' => [
        'ad_images' => [
            'target_size_kb' => 120,     // Very aggressive for ad images
            'min_quality' => 25,
            'max_width' => 900,
            'max_height' => 900,
            'format' => 'webp',
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => true,
            'blur_threshold' => 0.4,
            'create_variants' => true,
            'variants' => [
                'thumbnail' => [
                    'target_size_kb' => 25,
                    'max_width' => 300,
                    'max_height' => 300,
                    'quality' => 50
                ],
                'medium' => [
                    'target_size_kb' => 60,
                    'max_width' => 600,
                    'max_height' => 600,
                    'quality' => 55
                ]
            ]
        ],

        'profile_avatar' => [
            'target_size_kb' => 35,      // Very small for avatars
            'min_quality' => 35,
            'max_width' => 250,
            'max_height' => 250,
            'format' => 'webp',
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => true,
            'blur_threshold' => 0.2,
            'create_variants' => true,
            'variants' => [
                'small' => [
                    'target_size_kb' => 15,
                    'max_width' => 100,
                    'max_height' => 100,
                    'quality' => 60
                ]
            ]
        ],

        'identity_documents' => [
            'target_size_kb' => 250,     // Larger for document readability
            'min_quality' => 45,
            'max_width' => 1200,
            'max_height' => 900,
            'format' => 'webp',
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => false,  // Keep colors for documents
            'blur_threshold' => 0.1,     // Minimal blur for text clarity
            'create_variants' => false
        ],

        'broker_documents' => [
            'target_size_kb' => 250,
            'min_quality' => 45,
            'max_width' => 1200,
            'max_height' => 900,
            'format' => 'webp',
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => false,
            'blur_threshold' => 0.1,
            'create_variants' => false
        ],

        'chat_attachments' => [
            'target_size_kb' => 100,
            'min_quality' => 40,
            'max_width' => 800,
            'max_height' => 800,
            'format' => 'webp',
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => true,
            'blur_threshold' => 0.3,
            'create_variants' => false
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Compression Techniques
    |--------------------------------------------------------------------------
    |
    | Enable/disable specific compression techniques
    |
    */

    'techniques' => [
        'aggressive_resize' => true,
        'color_optimization' => true,
        'metadata_stripping' => true,
        'progressive_encoding' => true,
        'quality_reduction' => true,
        'format_conversion' => true,
        'blur_for_compression' => true,
        'sharpening_compensation' => true,
        'contrast_adjustment' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings that affect compression performance
    |
    */

    'performance' => [
        'max_processing_time' => 30,     // seconds
        'memory_limit' => '256M',
        'max_attempts' => 5,
        'batch_size' => 10,              // for batch processing
        'enable_caching' => true,
        'cache_duration' => 3600,        // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | File validation settings
    |
    */

    'validation' => [
        'max_upload_size_mb' => 25,      // Increased to handle large images before compression
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp',
            'image/tiff'
        ],
        'allowed_extensions' => [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif'
        ],
        'min_dimensions' => [
            'width' => 50,
            'height' => 50
        ],
        'max_dimensions' => [
            'width' => 10000,
            'height' => 10000
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging and Monitoring
    |--------------------------------------------------------------------------
    |
    | Settings for compression logging and monitoring
    |
    */

    'logging' => [
        'enabled' => true,
        'log_level' => 'info',
        'log_successful_compressions' => true,
        'log_failed_compressions' => true,
        'log_compression_stats' => true,
        'detailed_logging' => env('APP_DEBUG', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Settings
    |--------------------------------------------------------------------------
    |
    | What to do when compression fails
    |
    */

    'fallback' => [
        'on_compression_failure' => 'use_original', // 'use_original', 'reject', 'retry_with_basic'
        'max_original_size_kb' => 2048,             // 2MB max for original files
        'retry_attempts' => 2,
    ],

    /*
    |--------------------------------------------------------------------------
    | WebP Support Detection
    |--------------------------------------------------------------------------
    |
    | Settings for WebP format support
    |
    */

    'webp' => [
        'enabled' => true,
        'fallback_format' => 'jpg',
        'quality_adjustment' => -5,      // Reduce quality by 5 for WebP (it's more efficient)
        'browser_support_check' => false, // Set to true if you want to check browser support
    ]
];
