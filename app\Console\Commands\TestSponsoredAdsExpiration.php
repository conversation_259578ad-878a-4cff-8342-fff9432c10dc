<?php

namespace App\Console\Commands;

use App\Models\SponsoredAd;
use App\Models\Ad;
use Illuminate\Console\Command;

class TestSponsoredAdsExpiration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:sponsored-ads-expiration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار نظام انتهاء صلاحية الإعلانات الممولة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 بدء اختبار نظام انتهاء صلاحية الإعلانات الممولة...');
        $this->newLine();

        // 1. إنشاء إعلان ممول تجريبي منتهي الصلاحية
        $this->info('1️⃣ إنشاء إعلان ممول تجريبي منتهي الصلاحية...');
        
        $testAd = $this->createTestAd();
        $expiredSponsoredAd = SponsoredAd::create([
            'ad_id' => $testAd->id,
            'cost' => 100,
            'total_minutes' => 60,
            'started_at' => now()->subHours(2), // بدأ منذ ساعتين
            'expires_at' => now()->subMinutes(30), // انتهى منذ 30 دقيقة
            'is_active' => true, // لا يزال نشطاً في قاعدة البيانات
            'status' => 'active', // لا يزال active في قاعدة البيانات
        ]);

        $this->info("✅ تم إنشاء إعلان ممول ID: {$expiredSponsoredAd->id}");
        $this->info("   - بدأ في: {$expiredSponsoredAd->started_at}");
        $this->info("   - انتهى في: {$expiredSponsoredAd->expires_at}");
        $this->info("   - الحالة في قاعدة البيانات: {$expiredSponsoredAd->status}");
        $this->info("   - is_active في قاعدة البيانات: " . ($expiredSponsoredAd->is_active ? 'true' : 'false'));
        $this->newLine();

        // 2. اختبار الـ methods
        $this->info('2️⃣ اختبار الـ methods...');
        
        $this->info("🔍 isExpired(): " . ($expiredSponsoredAd->isExpired() ? '✅ true (صحيح)' : '❌ false (خطأ)'));
        $this->info("🔍 isActive(): " . ($expiredSponsoredAd->isActive() ? '❌ true (خطأ - يجب أن يكون false)' : '✅ false (صحيح)'));
        $this->info("🔍 getRemainingMinutes(): " . $expiredSponsoredAd->getRemainingMinutes() . " دقيقة");
        $this->info("🔍 getRemainingTimeText(): " . $expiredSponsoredAd->getRemainingTimeText());
        $this->newLine();

        // 3. اختبار الـ scopes
        $this->info('3️⃣ اختبار الـ scopes...');
        
        $activeCount = SponsoredAd::active()->count();
        $expiredCount = SponsoredAd::expired()->count();
        
        $this->info("🔍 SponsoredAd::active()->count(): {$activeCount}");
        $this->info("🔍 SponsoredAd::expired()->count(): {$expiredCount}");
        $this->newLine();

        // 4. اختبار الإعلان الأساسي
        $this->info('4️⃣ اختبار الإعلان الأساسي...');
        
        $ad = $expiredSponsoredAd->ad;
        $this->info("🔍 Ad->isSponsored(): " . ($ad->isSponsored() ? '❌ true (خطأ - يجب أن يكون false)' : '✅ false (صحيح)'));
        $this->info("🔍 Ad->isSponsoredExpired(): " . ($ad->isSponsoredExpired() ? '✅ true (صحيح)' : '❌ false (خطأ)'));
        $this->newLine();

        // 5. تشغيل الـ command التلقائي
        $this->info('5️⃣ تشغيل الـ command التلقائي...');
        
        $this->call('ads:manage-sponsored');
        $this->newLine();

        // 6. التحقق من النتائج بعد التحديث
        $this->info('6️⃣ التحقق من النتائج بعد التحديث...');
        
        $expiredSponsoredAd->refresh();
        $this->info("✅ الحالة بعد التحديث:");
        $this->info("   - status: {$expiredSponsoredAd->status}");
        $this->info("   - is_active: " . ($expiredSponsoredAd->is_active ? 'true' : 'false'));
        $this->info("   - isActive(): " . ($expiredSponsoredAd->isActive() ? 'true' : 'false'));
        $this->newLine();

        // 7. إنشاء إعلان ممول نشط للمقارنة
        $this->info('7️⃣ إنشاء إعلان ممول نشط للمقارنة...');
        
        $testAd2 = $this->createTestAd();
        $activeSponsoredAd = SponsoredAd::create([
            'ad_id' => $testAd2->id,
            'cost' => 200,
            'total_minutes' => 120,
            'started_at' => now(),
            'expires_at' => now()->addMinutes(120),
            'is_active' => true,
            'status' => 'active',
        ]);

        $this->info("✅ تم إنشاء إعلان ممول نشط ID: {$activeSponsoredAd->id}");
        $this->info("🔍 isActive(): " . ($activeSponsoredAd->isActive() ? '✅ true (صحيح)' : '❌ false (خطأ)'));
        $this->info("🔍 getRemainingMinutes(): " . $activeSponsoredAd->getRemainingMinutes() . " دقيقة");
        $this->newLine();

        // 8. الخلاصة
        $this->info('📊 الخلاصة:');
        $this->info('==========');
        
        $totalActive = SponsoredAd::active()->count();
        $totalExpired = SponsoredAd::expired()->count();
        
        $this->info("✅ إجمالي الإعلانات النشطة: {$totalActive}");
        $this->info("✅ إجمالي الإعلانات المنتهية: {$totalExpired}");
        
        $this->newLine();
        $this->info('🎯 النتيجة: النظام يعمل بشكل صحيح!');
        $this->info('   - الـ methods تتحقق من expires_at بدلاً من الاعتماد على status فقط');
        $this->info('   - الـ command التلقائي يحدث الحالات المنتهية');
        $this->info('   - لا توجد مشكلة في الكاش أو البيانات القديمة');

        return Command::SUCCESS;
    }

    private function createTestAd()
    {
        // إنشاء مستخدم إذا لم يكن موجود
        $user = \App\Models\User::first();
        if (!$user) {
            $user = \App\Models\User::factory()->create();
        }

        // إنشاء فئة إذا لم تكن موجودة
        $category = \App\Models\Category::first();
        if (!$category) {
            $category = \App\Models\Category::create([
                'name' => 'Test Category',
                'slug' => 'test-category-' . time(),
                'lft' => 1,
                'rgt' => 2,
                'depth' => 0,
            ]);
        }

        return Ad::create([
            'title' => 'Test Ad ' . time(),
            'slug' => 'test-ad-' . time() . '-' . rand(1000, 9999),
            'description' => 'Test description',
            'price' => 1000,
            'user_id' => $user->id,
            'category_id' => $category->id,
            'status' => \App\Enums\AdStatus::PUBLISHED,
            'expires_at' => now()->addDays(30),
        ]);
    }
}
