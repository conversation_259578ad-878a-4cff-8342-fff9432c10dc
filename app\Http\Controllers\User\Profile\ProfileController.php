<?php

namespace App\Http\Controllers\User\Profile;

use App\Contracts\Repositories\AnalyticRepositoryInterface;
use App\Contracts\Repositories\AuthenticateRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\Profile\UpdateProfileRequest;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use App\Services\SimpleFileCompressionService;
use App\Services\AdvancedImageOptimizer;
use Illuminate\Support\Facades\Log;

class ProfileController extends Controller
{
    /**
     * Instantiate new controller instance
     */
    public function __construct(protected AuthenticateRepositoryInterface $authRepository, protected AnalyticRepositoryInterface $analyticRepository)
    {}

    /**
     * Show dashboard page.
     * 
     * @return \Illuminate\View\View
     */
    public function dashboard(): View
    {
        return view('dashboard.user.index', [
            'metrics' => $this->analyticRepository->getUserDashboardMetrics($this->authRepository->user()),
        ]);
    }

    /**
     * Show user profile.
     * 
     * @return \Illuminate\View\View
     */
    public function show(): View
    {
        $user = $this->authRepository->user();
        $user->refresh(); // Ensure we have the latest data

        return view('profile.user.index', [
            'user' => $user,
        ]);
    }

    /**
     * Update user profile.
     *
     * @param UpdateProfileRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateProfileRequest $request): RedirectResponse
    {
        $user = $this->authRepository->user();
        $data = $request->validated();

        Log::info('Profile update started', [
            'user_id' => $user->id,
            'has_avatar_file' => $request->hasFile('avatar'),
            'current_avatar' => $user->avatar,
            'validated_data' => $data
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            Log::info('Avatar file detected', [
                'file_name' => $request->file('avatar')->getClientOriginalName(),
                'file_size' => $request->file('avatar')->getSize(),
                'mime_type' => $request->file('avatar')->getMimeType()
            ]);

            // Check if user is a broker - brokers cannot change their avatar
            if ($user->is_broker ?? false) {
                Log::warning('Broker attempted to change avatar', ['user_id' => $user->id]);
                return redirect()->route('user.profile')
                    ->withErrors(['avatar' => 'المندوبون لا يمكنهم تغيير صورتهم الشخصية. يرجى التواصل مع الإدارة.']);
            }

            // Delete old avatar if exists and not default
            if ($user->avatar && $user->avatar !== config('chatify.user_avatar.default')) {
                Log::info('Deleting old avatar', ['old_avatar' => $user->avatar]);
                Storage::disk('public')->delete($user->avatar);
            }

            // Ultra compress and store new avatar for maximum size reduction
            $compressionService = new SimpleFileCompressionService();
            $advancedOptimizer = new AdvancedImageOptimizer();

            // Use ultra compression for profile avatars
            $ultraSettings = [
                'target_size_kb' => 40,   // Very aggressive - 40KB max
                'min_quality' => 35,
                'max_width' => 250,       // Smaller than before
                'max_height' => 250,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => true
            ];

            $result = $advancedOptimizer->ultraCompress($request->file('avatar'), $ultraSettings);

            if ($result['success']) {
                // Store the ultra-compressed avatar
                $uuid = \Illuminate\Support\Str::uuid();
                $filename = $uuid . '.webp';
                $path = 'avatars/' . $filename;

                Storage::disk('public')->put($path, $result['compressed_data']);
                $data['avatar'] = $path;

                Log::info('Avatar ultra compressed and stored successfully', [
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%',
                    'final_dimensions' => $result['final_dimensions'],
                    'techniques_applied' => $result['techniques_applied'],
                    'path' => $path
                ]);
            } else {
                Log::error('Avatar ultra compression failed', ['error' => $result['error']]);
                return redirect()->route('user.profile')
                    ->withErrors(['avatar' => 'فشل في معالجة الصورة. يرجى المحاولة مرة أخرى.']);
            }
        }

        // Handle name change
        if ($request->filled('name') && $request->name !== $user->name) {
            if (!$user->canChangeName()) {
                if ($user->is_trusted) {
                    return redirect()->route('user.profile')
                        ->withErrors(['name' => 'المستخدمون الموثقون لا يمكنهم تغيير أسمائهم.']);
                } else {
                    $daysRemaining = $user->days_until_name_change;
                    return redirect()->route('user.profile')
                        ->withErrors(['name' => "يمكنك تغيير اسمك مرة كل 15 يوم. متبقي {$daysRemaining} يوم."]);
                }
            }

            // Update name with timestamp
            $user->updateName($request->name);
            unset($data['name']); // Remove from data since it's already updated
        }

        // Update other profile data
        Log::info('Before repository update', [
            'user_id' => $user->id,
            'data_to_update' => $data
        ]);

        $this->authRepository->update($user, $data);

        // Refresh user to check if avatar was saved
        $user->refresh();
        Log::info('After repository update', [
            'user_id' => $user->id,
            'user_avatar_after_update' => $user->avatar
        ]);

        return redirect()->route('user.profile')->with('success', 'تم تحديث الملف الشخصي بنجاح.');
    }
}
