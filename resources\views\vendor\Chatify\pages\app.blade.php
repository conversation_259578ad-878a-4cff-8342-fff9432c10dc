<!DOCTYPE html>
<html lang="ar" dir="LTR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @include('Chatify::layouts.headLinks')
</head>
<body>

{{-- تصميم شات بسيط واحترافي --}}
<style>
    /* إعدادات عامة */
    * {
        box-sizing: border-box;
    }

    body {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f5f7fa;
        overflow-x: hidden;
    }

    /* تنسيق الهيدر */
    header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: white;
        box-shadow: 0 2px 20px rgba(0,0,0,0.08);
        height: 70px;
    }

    /* تنسيق الشات الرئيسي */
    .messenger {
        margin-top: 70px;
        height: calc(100vh - 70px);
        display: flex;
        background: white;
        box-shadow: 0 0 30px rgba(0,0,0,0.1);
        border-radius: 0;
        overflow: hidden;
    }

    /* قائمة الاتصالات (الجانب الأيسر) */
    .messenger-listView {
        width: 350px;
        background: #fafbfc;
        border-right: 1px solid #e4e6ea;
        display: flex;
        flex-direction: column;
    }

    /* هيدر قائمة الاتصالات */
    .messenger-listView .m-header {
        padding: 20px;
        background: white;
        border-bottom: 1px solid #e4e6ea;
    }

    .messenger-listView .m-header nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .messenger-headTitle {
        font-size: 24px;
        font-weight: 700;
        color: #1c1e21;
        margin-left: 10px;
    }




    

    

    

    


    /* شريط البحث الجديد */
    .search-container {
        padding: 10px 20px;
        background: transparent;
        position: relative;
        z-index: 10;
    }
    
    .chat-search {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #e4e6ea;
        border-radius: 20px;
        background: #f0f2f5;
        font-size: 14px;
        color: #1c1e21;
        outline: none;
        transition: all 0.3s ease;
        box-sizing: border-box;
        font-family: inherit;
        position: relative;
        z-index: 11;
        pointer-events: auto;
    }
    
    .chat-search:focus {
        background: white;
        border-color: #1877f2;
        box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
    }
    
    .chat-search::placeholder {
        color: #65676b;
        opacity: 0.8;
    }
    
    /* تحسين مظهر شريط البحث */
    .chat-search {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%2365676b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>');
        background-repeat: no-repeat;
        background-position: 12px center;
        background-size: 16px;
        padding-left: 40px;
    }
    
    .chat-search:focus {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%231877f2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>');
    }
    
    .chat-search.searching {
        background: white;
        border-color: #1877f2;
        box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
    }
    
    .chat-search:disabled {
        background: #f8f9fa;
        color: #6c757d;
        cursor: not-allowed;
    }
    
    /* تحسين مظهر شريط البحث */
    .chat-search {
        position: relative;
        z-index: 1;
    }
    
    /* تحسين مظهر رسالة "لا توجد نتائج" */
    .no-results {
        background: #f8f9fa;
        border-radius: 10px;
        margin: 10px 20px;
        border: 1px dashed #c7c7cc;
    }
    
    .no-results p {
        margin: 0;
        font-size: 14px;
    }
    
    /* ضمان عدم تداخل شريط البحث مع عناصر أخرى */
    .search-container * {
        pointer-events: auto !important;
    }
    
    .chat-search {
        background: #f0f2f5 !important;
        border: 1px solid #e4e6ea !important;
        color: #1c1e21 !important;
        pointer-events: auto !important;
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
    }
    
    .chat-search:focus {
        background: white !important;
        border-color: #1877f2 !important;
        box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1) !important;
        z-index: 1000 !important;
        outline: none !important;
    }
    
    .chat-search:disabled {
        background: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed !important;
    }
    
    /* منع تداخل شريط البحث مع عناصر Chatify الأخرى */
    .messenger-listView .search-container {
        position: relative;
        z-index: 100;
    }
    
    .messenger-listView .chat-search {
        position: relative;
        z-index: 101;
    }
    
    /* ضمان أن شريط البحث لا يتأثر بـ CSS آخر */
    .messenger-listView input.chat-search {
        all: unset;
        width: 100%;
        padding: 10px 15px 10px 40px;
        border: 1px solid #e4e6ea;
        border-radius: 20px;
        background: #f0f2f5;
        font-size: 14px;
        color: #1c1e21;
        outline: none;
        transition: all 0.3s ease;
        box-sizing: border-box;
        font-family: inherit;
        position: relative;
        z-index: 11;
        pointer-events: auto;
        cursor: text;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%2365676b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>');
        background-repeat: no-repeat;
        background-position: 12px center;
        background-size: 16px;
    }
    
    .messenger-listView input.chat-search:focus {
        background: white;
        border-color: #1877f2;
        box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%231877f2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>');
    }
    
    .messenger-listView input.chat-search.searching {
        background: white;
        border-color: #1877f2;
        box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.1);
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%231877f2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>');
    }
    
    /* منع تداخل شريط البحث مع عناصر أخرى */
    .messenger-listView .search-container {
        pointer-events: auto !important;
    }
    
    .messenger-listView .search-container * {
        pointer-events: auto !important;
    }
    
    /* قائمة الاتصالات */
    .messenger-list-item {
        padding: 12px 20px;
        margin: 2px 0;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 8px;
        margin: 2px 10px;
    }

    .messenger-list-item:hover {
        background: #f0f2f5;
    }

    .messenger-list-item.m-list-active {
        background: #1877f2;
        color: white;
    }

    /* منطقة المحادثة (الوسط) */
    .messenger-messagingView {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: white;
        height: 100%;
        position: relative;
    }

    /* هيدر المحادثة */
    .messenger-messagingView .m-header {
        padding: 15px 20px;
        background: white;
        border-bottom: 1px solid #e4e6ea;
        box-shadow: 0 1px 2px rgba(0,0,0,0.04);
    }

    .chat-header-user-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .user-name {
        font-size: 16px;
        font-weight: 600;
        color: #1c1e21;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .user-name:hover {
        color: var(--primary-color);
        text-decoration: none;
    }

    .header-avatar {
        cursor: pointer;
        transition: transform 0.3s ease;
        border: 2px solid transparent;
        border-radius: 50%;
    }

    .header-avatar:hover {
        transform: scale(1.05);
        border-color: var(--primary-color);
    }

    /* لينك البروفايل في الجزء الجانبي */
    .info-name-link {
        text-decoration: none;
        color: inherit;
        display: block;
        transition: all 0.3s ease;
    }

    .info-name-link:hover {
        text-decoration: none;
        color: var(--primary-color);
        transform: translateY(-1px);
    }

    .info-name-link .info-name {
        margin: 0;
        transition: all 0.3s ease;
    }

    .info-name-link:hover .info-name {
        color: var(--primary-color);
    }

    .chat-header-badges {
        display: flex;
        gap: 4px;
        align-items: center;
    }

    .chat-header-badge {
        display: inline-flex;
        align-items: center;
        gap: 2px;
        padding: 1px 4px;
        border-radius: 8px;
        font-size: 9px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .chat-header-badge.vip-badge {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #8b6914;
        border: 1px solid #ffd700;
    }

    /* أزرار الحظر وفك الحظر في الهيدر */
    #blockUserBtn, #unblockUserBtn {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        transition: all 0.3s ease;
    }

    #blockUserBtn:hover {
        background-color: #dc3545 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }

    #unblockUserBtn:hover {
        background-color: #28a745 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    #blockStatusMessage {
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 4px;
        background-color: rgba(255, 193, 7, 0.1);
        border: 1px solid #ffc107;
    }

    /* علامة VIP في الهيدر */
    .vip-badge-header {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 12px;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #8b6914;
        border: 1px solid #ffd700;
        font-size: 11px;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        animation: vipGlow 2s ease-in-out infinite alternate;
    }

    @keyframes vipGlow {
        from {
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }
        to {
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.6);
        }
    }

    .vip-badge-header i {
        font-size: 12px;
        color: #b8860b;
    }

    /* تحسين مربع الإرسال ليكون ثابت ومرئي */
    .messenger-sendCard {
        background: white;
        border-top: 1px solid #e4e6ea;
        padding: 15px 20px;
        position: sticky;
        bottom: 0;
        z-index: 100;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        margin-top: auto;
    }

    .messenger-sendCard form {
        display: flex;
        align-items: flex-end;
        gap: 12px;
        max-width: 100%;
    }

    .messenger-sendCard .m-send {
        flex: 1;
        border: 2px solid #e4e6ea;
        background: #f8f9fa;
        resize: none;
        outline: none;
        padding: 12px 16px;
        border-radius: 25px;
        min-height: 44px;
        max-height: 120px;
        font-family: inherit;
        font-size: 14px;
        line-height: 1.4;
        transition: all 0.3s ease;
    }

    .messenger-sendCard .m-send:focus {
        border-color: var(--primary-color);
        background: white;
        box-shadow: 0 0 0 3px rgba(255, 37, 34, 0.1);
    }

    .messenger-sendCard .send-button {
        background: var(--primary-color);
        border: none;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 16px;
        flex-shrink: 0;
    }

    .messenger-sendCard .send-button:hover:not(:disabled) {
        background: #e01e1b;
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(255, 37, 34, 0.3);
    }

    .messenger-sendCard .send-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        background: #ccc;
    }

    /* تأكد من أن منطقة الرسائل تترك مساحة لمربع الإرسال */
    .m-body.messages-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px); /* حساب هيدر الشات + مربع الإرسال فقط */
        min-height: 400px;
    }

    /* تحسين حجم الصفحة الكامل - بدون هيدر أساسي */
    .messenger {
        height: 100vh;
        max-height: 100vh;
        margin-top: 0;
    }

    .messenger-listView {
        height: 100vh;
        max-height: 100vh;
        overflow-y: auto;
    }

    .messenger-messagingView {
        height: 100vh;
        max-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .messenger-header {
        flex-shrink: 0;
        height: 60px;
        min-height: 60px;
    }

    .messages {
        flex: 1;
        overflow-y: auto;
        padding: 15px;
        scroll-behavior: smooth;
        margin-bottom: 0;
    }

    /* تحسين عرض الرسائل */
    .message-card {
        margin-bottom: 10px;
        clear: both;
    }

    /* التأكد من أن آخر رسالة مرئية */
    .messages::after {
        content: '';
        display: block;
        height: 10px;
        clear: both;
    }

    /* تحسين responsive للشاشات المختلفة */
    @media (max-width: 768px) {
        .messenger-sendCard {
            padding: 12px 15px;
        }

        .messenger-sendCard .m-send {
            padding: 10px 14px;
            min-height: 40px;
        }

        .messenger-sendCard .send-button {
            width: 40px;
            height: 40px;
            min-width: 40px;
            min-height: 40px;
        }

        .m-body.messages-container {
            height: calc(100vh - 110px);
        }

        .messenger {
            height: 100vh;
            margin-top: 0;
        }

        .messenger-listView {
            height: 100vh;
        }

        .messenger-messagingView {
            height: 100vh;
        }
    }

    @media (max-width: 480px) {
        .m-body.messages-container {
            height: calc(100vh - 100px);
        }

        .messenger {
            height: 100vh;
            margin-top: 0;
        }

        .messenger-listView {
            height: 100vh;
        }

        .messenger-messagingView {
            height: 100vh;
        }

        .messenger-header {
            height: 50px;
            min-height: 50px;
        }
    }

    /* للشاشات الكبيرة */
    @media (min-width: 1200px) {
        .messenger {
            max-width: 100%;
            margin: 0 auto;
        }
    }

    .chat-header-badge.trader-badge {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .chat-header-badge.company-badge {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
    }

    .chat-header-badge.admin-badge {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        color: white;
    }

    /* منطقة الرسائل */
    .messages-container {
        flex: 1;
        padding: 20px;
        background: #f8f9fa;
        overflow-y: auto;
        height: calc(100vh - 200px); /* احسب الارتفاع بدقة */
        min-height: 300px;
    }

    /* بطاقة الرسالة */
    .message-card {
        margin: 8px 0;
        max-width: 70%;
        clear: both;
    }

    .message-card.mc-sender {
        float: right;
        margin-left: auto;
    }

    .message-card.mc-receiver {
        float: left;
        margin-right: auto;
    }

    .message-card .message {
        padding: 12px 16px;
        border-radius: 18px;
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;
    }

    .message-card.mc-sender .message {
        background: #1877f2;
        color: white;
        border-bottom-right-radius: 4px;
    }

    .message-card.mc-receiver .message {
        background: #e4e6ea;
        color: #1c1e21;
        border-bottom-left-radius: 4px;
    }

    /* وقت الرسالة */
    .message-time {
        font-size: 11px;
        color: #65676b;
        margin-top: 4px;
        text-align: right;
    }

    .message-card.mc-receiver .message-time {
        text-align: left;
    }

    /* نموذج إرسال الرسالة */
    .messenger-sendCard {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 15px 20px;
        background: white;
        border-top: 1px solid #e4e6ea;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        z-index: 100;
        margin-top: auto; /* يدفع المربع للأسفل */
    }

    .messenger-sendCard .m-send {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #e4e6ea;
        border-radius: 20px;
        outline: none;
        font-size: 14px;
        resize: none;
        min-height: 40px;
        max-height: 120px;
        font-family: inherit;
        transition: all 0.3s ease;
    }

    .messenger-sendCard .m-send:focus {
        border-color: #1877f2;
        box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
    }

    /* تحسين شكل الأزرار في مربع الإرسال */
    .messenger-sendCard .attachment-btn,
    .messenger-sendCard .emoji-btn {
        background: none;
        border: none;
        color: #65676b;
        font-size: 18px;
        padding: 8px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 0 5px;
    }

    .messenger-sendCard .attachment-btn:hover,
    .messenger-sendCard .emoji-btn:hover {
        background: #f0f2f5;
        color: #1877f2;
    }

    /* لوحة المعلومات (الجانب الأيمن) */
    .messenger-infoView {
        width: 300px;
        background: #fafbfc;
        border-left: 1px solid #e4e6ea;
        display: none;
    }

    /* الأزرار */
    .m-header-right a {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: #65676b;
        text-decoration: none;
        margin-left: 8px;
        transition: all 0.2s ease;
    }

    .m-header-right a:hover {
        background: #f0f2f5;
        color: #1c1e21;
    }

    /* تحسين تنسيق الهيدر وأزرار My Account */
    header .eg-btn.btn--primary2 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border: none !important;
        border-radius: 25px !important;
        padding: 10px 20px !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    }

    header .eg-btn.btn--primary2:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    }

    header .eg-btn.btn--primary2 a {
        color: white !important;
        font-weight: 600 !important;
        text-decoration: none !important;
        font-size: 14px !important;
    }

    header .join-btn {
        background: rgba(102, 126, 234, 0.1) !important;
        border: 2px solid #667eea !important;
        border-radius: 25px !important;
        padding: 8px 18px !important;
        transition: all 0.3s ease !important;
    }

    header .join-btn:hover {
        background: #667eea !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    }

    header .join-btn a {
        color: #667eea !important;
        font-weight: 600 !important;
        text-decoration: none !important;
        transition: color 0.3s ease !important;
    }

    header .join-btn:hover a {
        color: white !important;
    }

    /* تحسين شكل الهيدر العام */
    header {
        background: white !important;
        box-shadow: 0 2px 20px rgba(0,0,0,0.08) !important;
        backdrop-filter: blur(10px) !important;
    }

    header .menu-list li a {
        color: #333 !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        position: relative !important;
    }

    header .menu-list li a:hover {
        color: #667eea !important;
    }

    header .menu-list li a::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -5px;
        left: 50%;
        background: #667eea;
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    header .menu-list li a:hover::after {
        width: 100%;
    }

    /* تحسينات للموبايل */
    @media (max-width: 768px) {
        .messenger {
            margin-top: 60px;
            height: calc(100vh - 60px);
        }

        .messenger-listView {
            width: 100%;
            position: absolute;
            z-index: 10;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .messenger-listView.conversation-active {
            transform: translateX(0);
        }

        .messenger-messagingView {
            width: 100%;
            height: 100%;
        }

        .messages-container {
            height: calc(100vh - 180px); /* أقل ارتفاع للموبايل */
            padding: 15px;
        }

        .messenger-sendCard {
            padding: 10px 15px;
        }

        .messenger-sendCard .m-send {
            padding: 10px 14px;
            font-size: 16px; /* منع الزوم في iOS */
        }

        .messenger-infoView {
            display: none;
        }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @media (max-width: 480px) {
        .messages-container {
            height: calc(100vh - 160px);
            padding: 10px;
        }

        .messenger-sendCard {
            padding: 8px 10px;
        }

        .message-card {
            max-width: 85%;
        }
    }

    /* تحسينات للشاشات الكبيرة */
    @media (min-width: 1200px) {
        .messages-container {
            height: calc(100vh - 220px);
        }

        .messenger-listView {
            width: 380px;
        }

        .messenger-infoView {
            width: 320px;
        }
    }

    /* تحسين البحث */
    .search-records {
        padding: 0 10px;
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }

    .search-records .messenger-list-item {
        margin: 2px 0;
        padding: 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }

    .search-records .messenger-list-item:hover {
        background: #f0f2f5;
        border-color: #e4e6ea;
    }

    .message-hint {
        text-align: center;
        color: #65676b;
        font-size: 14px;
        padding: 20px;
    }

    .messenger-title {
        font-size: 12px;
        font-weight: 600;
        color: #65676b;
        text-transform: uppercase;
        margin: 15px 0 8px 0;
        padding: 0 10px;
    }

    /* إخفاء الـ preloader المزعج نهائياً */
    .preloader,
    .preloader.style-2,
    .preloader.style-3,
    #global-loader,
    .loader {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        z-index: -9999 !important;
    }
</style>

{{-- إضافة الهيدر --}}

<div class="messenger">
    {{-- ----------------------Users/Groups lists side---------------------- --}}
    <div class="messenger-listView {{ !!$id ? 'conversation-active' : '' }}">
        {{-- Header and search bar --}}
        <div class="m-header">
            <nav>
                <a href="#"><i class="fas fa-inbox"></i> <span class="messenger-headTitle">MESSAGES</span> </a>
                {{-- header buttons --}}
                <nav class="m-header-right">
                    <a href="#"><i class="fas fa-cog settings-btn"></i></a>
                    <a href="#" class="listView-x"><i class="fas fa-times"></i></a>
                </nav>
            </nav>


            {{-- Tabs --}}
            {{-- <div class="messenger-listView-tabs">
                <a href="#" class="active-tab" data-view="users">
                    <span class="far fa-user"></span> Contacts</a>
            </div> --}}
        </div>
        {{-- tabs and lists --}}
        <div class="m-body contacts-container">
           {{-- Lists [Users/Group] --}}
           {{-- ---------------- [ User Tab ] ---------------- --}}
           <div class="show messenger-tab users-tab app-scroll" data-view="users">
               {{-- Auto Starred Contacts --}}
               <div class="favorites-section">
                <p class="messenger-title"><span>Recent Contacts</span></p>
                <div class="messenger-favorites app-scroll-hidden"></div>
               </div>
               
               {{-- Search Bar --}}
               <div class="search-container">
                   <input type="text" class="chat-search" placeholder="البحث في محادثاتك..." />
               </div>
               
               {{-- Saved Messages --}}
               <p class="messenger-title"><span>Your Space</span></p>
               {!! view('Chatify::layouts.listItem', ['get' => 'saved']) !!}
               {{-- Contact --}}
               <p class="messenger-title"><span>All Messages</span></p>
               <div class="listOfContacts" style="width: 100%;height: calc(100% - 272px);position: relative;"></div>
           </div>

        </div>
    </div>

    {{-- ----------------------Messaging side---------------------- --}}
    <div class="messenger-messagingView">
        {{-- header title [conversation name] amd buttons --}}
        <div class="m-header m-header-messaging">
            <nav class="chatify-d-flex chatify-justify-content-between chatify-align-items-center">
                {{-- header back button, avatar and user name --}}
                <div class="chatify-d-flex chatify-justify-content-between chatify-align-items-center">
                    <a href="#" class="show-listView"><i class="fas fa-arrow-left"></i></a>
                    <div class="avatar av-s header-avatar" style="margin: 0px 10px; margin-top: -5px; margin-bottom: -5px;">
                    </div>
                    <div class="chat-header-user-info">
                        <a href="#" class="user-name">{{ config('chatify.name') }}</a>
                        <div class="chat-header-badges" id="chat-header-badges">
                            {{-- Badges will be populated by JavaScript --}}
                        </div>
                    </div>
                </div>
                {{-- header buttons --}}
                <nav class="m-header-right">
                    <!-- علامة VIP -->
                    <div id="vipBadgeHeader" class="vip-badge-header me-2" style="display: none;" title="مستخدم VIP">
                        <i class="fas fa-crown"></i>
                        <span>VIP</span>
                    </div>
                    <!-- أزرار الحظر وفك الحظر -->
                    <button type="button" id="blockUserBtn" class="btn btn-sm btn-danger me-2" onclick="blockCurrentUser()" style="display: none;" title="حظر المستخدم">
                        <i class="fas fa-ban"></i>
                    </button>
                    <button type="button" id="unblockUserBtn" class="btn btn-sm btn-success me-2" onclick="unblockCurrentUser()" style="display: none;" title="فك حظر المستخدم">
                        <i class="fas fa-unlock"></i>
                    </button>
                    <div id="blockStatusMessage" class="text-warning me-2" style="display: none; font-size: 12px;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="blockStatusText"></span>
                    </div>
                    {{-- تم إخفاء زر المفضلة - النظام يضيف تلقائياً --}}
                    <a href="/"><i class="fas fa-home"></i></a>
                </nav>
            </nav>
            {{-- Internet connection --}}
            <div class="internet-connection">
                <span class="ic-connected">Connected</span>
                <span class="ic-connecting">Connecting...</span>
                <span class="ic-noInternet">No internet access</span>
            </div>
        </div>

        {{-- Messaging area --}}
        <div class="m-body messages-container app-scroll">
            <div class="messages">
                <p class="message-hint center-el"><span>Please select a chat to start messaging</span></p>
            </div>
            {{-- Typing indicator --}}
            <div class="typing-indicator">
                <div class="message-card typing">
                    <div class="message">
                        <span class="typing-dots">
                            <span class="dot dot-1"></span>
                            <span class="dot dot-2"></span>
                            <span class="dot dot-3"></span>
                        </span>
                    </div>
                </div>
            </div>

        </div>
        {{-- Send Message Form --}}
        @include('Chatify::layouts.sendForm')
    </div>
    {{-- ---------------------- Info side ---------------------- --}}
</div>

{{-- تم إزالة الفوتر --}}

<style>
/* تم إزالة CSS الخاص بأزرار الإبلاغ والحظر */

.message-card {
    position: relative;
}

/* تحسين عرض الرسائل */
.message-card:not(.mc-sender) {
    padding-right: 60px;
}

/* إخفاء أزرار الحذف */
.message-card .dlt-message {
    display: none !important;
}

/* تحسين modal الإبلاغ */
.report-modal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.report-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.report-modal .btn-close {
    filter: invert(1);
}

/* تحسين تنبيه الحظر */
.block-alert {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
}

.block-alert i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}
</style>

{{-- تم إزالة modal الإبلاغ --}}

<script>
// متغيرات عامة
let currentUserId = null;
let currentMessageId = null;

// تم إزالة دالة الإبلاغ

// دالة حظر المستخدم
function blockUser(userId) {
    if (!confirm('هل أنت متأكد من حظر هذا المستخدم؟ سيتم حظر كلاكما من التواصل مع بعضكما البعض.')) {
        return;
    }

    fetch('/chat/block', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // إعادة تحميل الشات لتطبيق الحظر
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء حظر المستخدم');
    });
}

// دالة حظر المستخدم الحالي
function blockCurrentUser() {
    const currentUserId = getMessengerId();
    if (currentUserId) {
        if (confirm('هل أنت متأكد من حظر هذا المستخدم؟\n\nلن تتمكنوا من التواصل مع بعضكم البعض.')) {
            blockUser(currentUserId);
        }
    }
}

// دالة فك حظر المستخدم الحالي
function unblockCurrentUser() {
    const currentUserId = getMessengerId();
    if (currentUserId) {
        if (confirm('هل أنت متأكد من فك حظر هذا المستخدم؟\n\nسيتمكن من إرسال رسائل لك مرة أخرى.')) {
            unblockUser(currentUserId);
        }
    }
}

// دالة حظر المستخدم
function blockUser(userId) {
    if (!userId) {
        showAlert('danger', 'خطأ: لم يتم تحديد المستخدم للحظر');
        return;
    }

    const blockBtn = document.getElementById('blockUserBtn');
    if (blockBtn) {
        blockBtn.disabled = true;
        blockBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    fetch('/chat/block-user', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            updateBlockUI(true, false, false); // أنا حظرته
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'تم الحظر بنجاح');
    })
    .finally(() => {
        if (blockBtn) {
            blockBtn.disabled = false;
            blockBtn.innerHTML = '<i class="fas fa-ban"></i>';
        }
    });
}

// دالة فك حظر المستخدم
function unblockUser(userId) {
    if (!userId) {
        showAlert('danger', 'خطأ: لم يتم تحديد المستخدم لفك الحظر');
        return;
    }

    const unblockBtn = document.getElementById('unblockUserBtn');
    if (unblockBtn) {
        unblockBtn.disabled = true;
        unblockBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    fetch('/chat/unblock-user', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            updateBlockUI(false, false, false); // لا يوجد حظر
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء فك حظر المستخدم');
    })
    .finally(() => {
        if (unblockBtn) {
            unblockBtn.disabled = false;
            unblockBtn.innerHTML = '<i class="fas fa-unlock"></i>';
        }
    });
}

// دالة تحديث واجهة الحظر
function updateBlockUI(iBlockedThem, theyBlockedMe, hasAnyBlock) {
    const blockBtn = document.getElementById('blockUserBtn');
    const unblockBtn = document.getElementById('unblockUserBtn');
    const blockStatusMessage = document.getElementById('blockStatusMessage');
    const blockStatusText = document.getElementById('blockStatusText');
    const sendForm = document.querySelector('.m-send');

    // إخفاء جميع العناصر أولاً
    if (blockBtn) blockBtn.style.display = 'none';
    if (unblockBtn) unblockBtn.style.display = 'none';
    if (blockStatusMessage) blockStatusMessage.style.display = 'none';

    if (iBlockedThem) {
        // أنا حظرته - إظهار زر فك الحظر
        if (unblockBtn) unblockBtn.style.display = 'inline-block';
        if (blockStatusMessage && blockStatusText) {
            blockStatusText.textContent = 'تم حظر هذا المستخدم';
            blockStatusMessage.style.display = 'inline-block';
        }
        // إخفاء نموذج الإرسال
        if (sendForm) sendForm.style.display = 'none';
    } else if (theyBlockedMe) {
        // هو حظرني - إظهار رسالة فقط
        if (blockStatusMessage && blockStatusText) {
            blockStatusText.textContent = 'تم حظرك من قبل هذا المستخدم';
            blockStatusMessage.style.display = 'inline-block';
        }
        // إخفاء نموذج الإرسال
        if (sendForm) sendForm.style.display = 'none';
    } else {
        // لا يوجد حظر - إظهار زر الحظر
        if (blockBtn) blockBtn.style.display = 'inline-block';
        // إظهار نموذج الإرسال
        if (sendForm) sendForm.style.display = 'flex';
    }
}

// تم إزالة معالج إرسال الإبلاغ

// دالة التحقق من حالة الحظر
function checkBlockStatus(userId) {
    if (!userId) return;

    fetch('/chat/check-block', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateBlockUI(data.i_blocked_them, data.they_blocked_me, data.has_any_block);
        }
    })
    .catch(error => {
        console.error('Error checking block status:', error);
    });
}

// دالة إظهار علامة VIP في الهيدر
function updateVipBadge(userRank) {
    console.log('updateVipBadge called with rank:', userRank);
    const vipBadge = document.getElementById('vipBadgeHeader');
    console.log('vipBadge element:', vipBadge);
    if (vipBadge) {
        if (userRank && userRank > 0) {
            console.log('Showing VIP badge for rank:', userRank);
            vipBadge.style.display = 'inline-flex';
        } else {
            console.log('Hiding VIP badge for rank:', userRank);
            vipBadge.style.display = 'none';
        }
    }
}

// دالة إظهار التنبيهات
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = document.querySelector('.alert:last-of-type');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// فحص الحظر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة CSRF token إذا لم يكن موجوداً
    if (!document.querySelector('meta[name="csrf-token"]')) {
        const meta = document.createElement('meta');
        meta.name = 'csrf-token';
        meta.content = '{{ csrf_token() }}';
        document.head.appendChild(meta);
    }
});

// تم إزالة functions الخاصة بزر الحظر الكبير

// إضافة click handler للصورة للذهاب للبروفايل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة click handler للصورة في الهيدر
    document.addEventListener('click', function(e) {
        if (e.target.closest('.header-avatar')) {
            const userNameLink = document.querySelector('.m-header-messaging .user-name');
            if (userNameLink && userNameLink.href && userNameLink.href !== '#') {
                window.open(userNameLink.href, '_blank');
            }
        }
    });

    // تحسين scroll للرسائل الجديدة
    const messagesContainer = document.querySelector('.messages');
    if (messagesContainer) {
        // مراقبة إضافة رسائل جديدة
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // scroll للأسفل عند إضافة رسالة جديدة
                    setTimeout(() => {
                        messagesContainer.scrollTop = messagesContainer.scrollHeight;
                    }, 100);
                }
            });
        });

        observer.observe(messagesContainer, {
            childList: true,
            subtree: true
        });
    }
});
</script>

@include('Chatify::layouts.modals')
@include('Chatify::layouts.footerLinks')
</body>
</html>
