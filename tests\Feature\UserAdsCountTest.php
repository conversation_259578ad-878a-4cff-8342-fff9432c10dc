<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Ad;
use App\Enums\AdStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserAdsCountTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_increments_user_ads_count_when_ad_is_published()
    {
        // إنشاء مستخدم
        $user = User::factory()->create(['Number_Ads' => 0]);
        
        // إنشاء إعلان pending
        $ad = Ad::factory()->create([
            'user_id' => $user->id,
            'seller_email' => $user->email,
            'status' => AdStatus::PENDING
        ]);

        // تحديث الإعلان إلى published
        $ad->update(['status' => AdStatus::PUBLISHED]);

        // التحقق من زيادة العدد
        $user->refresh();
        $this->assertEquals(1, $user->Number_Ads);
    }

    /** @test */
    public function it_decrements_user_ads_count_when_published_ad_is_rejected()
    {
        // إنشاء مستخدم
        $user = User::factory()->create(['Number_Ads' => 1]);
        
        // إنشاء إعلان published
        $ad = Ad::factory()->create([
            'user_id' => $user->id,
            'seller_email' => $user->email,
            'status' => AdStatus::PUBLISHED
        ]);

        // تحديث الإعلان إلى rejected
        $ad->update(['status' => AdStatus::REJECTED]);

        // التحقق من تقليل العدد
        $user->refresh();
        $this->assertEquals(0, $user->Number_Ads);
    }

    /** @test */
    public function it_decrements_user_ads_count_when_published_ad_is_deleted()
    {
        // إنشاء مستخدم
        $user = User::factory()->create(['Number_Ads' => 1]);
        
        // إنشاء إعلان published
        $ad = Ad::factory()->create([
            'user_id' => $user->id,
            'seller_email' => $user->email,
            'status' => AdStatus::PUBLISHED
        ]);

        // حذف الإعلان
        $ad->delete();

        // التحقق من تقليل العدد
        $user->refresh();
        $this->assertEquals(0, $user->Number_Ads);
    }

    /** @test */
    public function it_does_not_change_count_when_pending_ad_is_rejected()
    {
        // إنشاء مستخدم
        $user = User::factory()->create(['Number_Ads' => 0]);
        
        // إنشاء إعلان pending
        $ad = Ad::factory()->create([
            'user_id' => $user->id,
            'seller_email' => $user->email,
            'status' => AdStatus::PENDING
        ]);

        // تحديث الإعلان إلى rejected
        $ad->update(['status' => AdStatus::REJECTED]);

        // التحقق من عدم تغيير العدد
        $user->refresh();
        $this->assertEquals(0, $user->Number_Ads);
    }

    /** @test */
    public function it_calculates_published_ads_count_correctly()
    {
        // إنشاء مستخدم
        $user = User::factory()->create();
        
        // إنشاء إعلانات مختلفة
        Ad::factory()->create([
            'user_id' => $user->id,
            'status' => AdStatus::PUBLISHED
        ]);
        
        Ad::factory()->create([
            'user_id' => $user->id,
            'status' => AdStatus::PUBLISHED
        ]);
        
        Ad::factory()->create([
            'user_id' => $user->id,
            'status' => AdStatus::PENDING
        ]);
        
        Ad::factory()->create([
            'user_id' => $user->id,
            'status' => AdStatus::REJECTED
        ]);

        // التحقق من العدد الصحيح
        $this->assertEquals(2, $user->published_ads_count);
    }
}
