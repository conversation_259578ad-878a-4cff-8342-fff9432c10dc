<?php

namespace App\Models;

use App\Enums\AdStatus;
use App\Traits\HasMedia;
use App\Traits\HasSlug;
use App\Traits\HasUuids;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Ad extends Model
{
    use HasFactory, HasSlug, HasUuids, HasMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'category_id',
        'sub_category_id',
        'user_id',
        'title',
        'description',
        'notes',
        'price',
        'is_negotiable',
        'seller_name',
        'seller_email',
        'seller_mobile',
        'seller_address',
        'mark_as_urgent',
        'status',
        'views',
        'country_id',
        'state_id',
        'city_id',
        'needs_brokering',
        'broker_commission',
        'expires_at',
        'created_at',
        'is_trusted',
        'Number_Ads',
        'rank',
        'unique_ad_code',
    ];

    /**
     * Get the category that owns the ad.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the ad attributes.
     */
    public function attributes()
    {
        return $this->hasMany(AdAttribute::class);
    }

    /**
     * Get the admin notes for the ad.
     */
    public function adminNotes(): HasMany
    {
        return $this->hasMany(AdminNote::class);
    }

    /**
     * Get the latest admin note for the ad.
     */
    public function latestAdminNote(): HasOne
    {
        return $this->hasOne(AdminNote::class)->latestOfMany();
    }

    /**
     * Get a specific attribute value by name.
     */
    public function getAdAttribute($attributeName)
    {
        $attribute = $this->attributes()->where('attribute_name', $attributeName)->first();
        return $attribute ? $attribute->formatted_value : null;
    }

    /**
     * Get all attributes as key-value pairs.
     */
    public function getAttributesArray()
    {
        return $this->attributes->pluck('formatted_value', 'attribute_name')->toArray();
    }

    /**
     * Scope to filter ads by attribute.
     */
    public function scopeWithAttribute($query, $attributeName, $attributeValue)
    {
        return $query->whereHas('attributes', function ($q) use ($attributeName, $attributeValue) {
            $q->where('attribute_name', $attributeName)
              ->where('attribute_value', $attributeValue);
        });
    }

    /**
     * Scope to filter ads by multiple attributes.
     */
    public function scopeWithAttributes($query, array $attributes)
    {
        foreach ($attributes as $name => $value) {
            if (!empty($value)) {
                $query->withAttribute($name, $value);
            }
        }
        return $query;
    }

    /**
     * Scope to search ads by attribute values.
     */
    public function scopeSearchByAttributes($query, $searchTerm)
    {
        return $query->whereHas('attributes', function ($q) use ($searchTerm) {
            $q->where('attribute_value', 'LIKE', "%{$searchTerm}%");
        });
    }

    /**
     * Get the sub category that owns the ad.
     */
    public function subCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    /**
     * Get the user that owns the ad.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withDefault([
            'name' => 'Guest',
            'username' => 'guest',
        ]);
    }

    /**
     * Get the country that owns the ad.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class)->withDefault([
            'name' => 'N/A',
        ]);
    }

    /**
     * Get the state that owns the ad.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class)->withDefault([
            'name' => 'N/A',
        ]);
    }

    /**
     * Get the city that owns the ad.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class)->withDefault([
            'name' => 'N/A',
        ]);
    }

    /**
     * Get the reports for the ad.
     */
    public function reports(): HasMany
    {
        return $this->hasMany(ReportAd::class);
    }

    /**
     * Get the bids for the ad.
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class);
    }

    /**
     * Get the highest bid amount for the ad.
     */
    public function highestBid(): HasOne
    {
        return $this->hasOne(Bid::class)->orderBy('amount', 'desc')->limit(1);
    }

    /**
     * Get the winning bid for the ad.
     */
    public function winningBid(): HasOne
    {
        return $this->hasOne(Bid::class)->where('is_accepted', true);
    }

    public function brokers()
    {
        return $this->belongsToMany(User::class, 'ad_broker', 'ad_id', 'user_id');
    }
    
    /**
     * Get related ads.
     */
    public function relatedAds(): HasMany
    {
        return $this->hasMany(Ad::class, 'category_id', 'category_id')
            ->where('id', '!=', $this->id)
            ->active()
            ->limit(4);
    }

    /**
     * Get the reviews for this ad.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class, 'ad_id');
    }

    /**
     * Get the sponsored ad record.
     */
    public function sponsoredAd(): HasOne
    {
        return $this->hasOne(SponsoredAd::class);
    }

    /**
     * Check if ad is currently sponsored
     */
    public function isSponsored(): bool
    {
        return $this->sponsoredAd && $this->sponsoredAd->isActive();
    }

    /**
     * Get broker interests for this ad.
     */
    public function brokerInterests(): HasMany
    {
        return $this->hasMany(BrokerInterest::class);
    }

    /**
     * Get active broker interests for this ad.
     */
    public function activeBrokerInterests(): HasMany
    {
        return $this->brokerInterests()->where('status', 'active');
    }

    /**
     * Get the average rating for this ad.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->reviews()->avg('rating') ?? 0.0;
    }

    /**
     * Get the total number of reviews for this ad.
     */
    public function getTotalReviewsAttribute(): int
    {
        return $this->reviews()->count();
    }

    /**
     * Get formatted rating with stars.
     */
    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->average_rating, 1);
    }

    /**
     * Get rating as stars (1-5).
     */
    public function getStarsAttribute(): int
    {
        return round($this->average_rating);
    }

    /**
     * Get formatted rating with stars display.
     */
    public function getFormattedRatingWithStarsAttribute(): string
    {
        return number_format($this->average_rating, 1) . " ★";
    }

    /**
     * Check if a user can review this ad.
     */
    public function canBeReviewedBy($userId): bool
    {
        // البائع لا يمكنه تقييم منتجه
        if ($this->user_id === $userId) {
            return false;
        }

        // التحقق من عدم وجود تقييم سابق
        return !$this->reviews()->where('reviewer_id', $userId)->exists();
    }

    /**
     * The attributes that should be cast.
     * 
     * @var array
     */
    protected $casts = [
        'is_negotiable' => 'boolean',
        'mark_as_urgent' => 'boolean',
        'expired_at' => 'datetime',
        'created_at' => 'datetime',
        'sponsored_end_at' => 'datetime',
        'status' => AdStatus::class,
    ];

    /**
     * The attributes that should be mutated to dates.
     * 
     * @var array
     */
    protected $dates = [
        'expired_at',
        'created_at',
    ];

    /**
     * Ad Model Methods
     * 
     */

    public function expired(): bool
    {
        return $this->expired_at->isPast() && $this->status === AdStatus::PUBLISHED;
    }

    public function rejected(): bool
    {
        return $this->status === AdStatus::REJECTED;
    }

    public function pending(): bool
    {
        return $this->status === AdStatus::PENDING;
    }

    /**
     * Scope a query to only include active ads.
     */
    public function scopeActive(Builder $query)
    {
        return $query->where('status', AdStatus::PUBLISHED);
    }

    /**
     * Scope a query to include upcoming ads (published ads that will start soon).
     * For now, we'll return published ads since there's no start date concept.
     */
    public function scopeUpcoming(Builder $query)
    {
        // بما إن مفيش مفهوم تاريخ بداية، هنرجع الإعلانات المقبولة
        return $query->where('status', AdStatus::PUBLISHED);
    }

    /**
     * Scope a query to only include pending ads (waiting for admin approval).
     */
    public function scopePending(Builder $query)
    {
        return $query->where('status', AdStatus::PENDING);
    }

    /**
     * Scope a query to only include rejected ads.
     */
    public function scopeRejected(Builder $query)
    {
        return $query->where('status', AdStatus::REJECTED);
    }
    /**
     * Scope a query to only include expired ads.
     */
    public function scopeExpired(Builder $query)
    {
        return $query->where('status', AdStatus::EXPIRED);
    }


    
    /**
     * Check if ad has an accepted bid
     */
    public function hasAcceptedBid(): bool
    {
        return $this->bids()->where('is_accepted', true)->exists();
    }
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Check if ad can be boosted (48 hours since last boost)
     */
    public function canBeBosted(): bool
    {
        if (!$this->created_at) {
            return true; // Never boosted before
        }

        return now()->diffInHours($this->created_at) >= 48;
    }

    /**
     * Get the first image URL for the ad
     */
    public function getFirstImageUrlAttribute(): ?string
    {
        $firstMedia = $this->media()->first();
        return $firstMedia ? $firstMedia->url : null;
    }

    /**
     * Get all image URLs for the ad
     */
    public function getImageUrlsAttribute(): array
    {
        return $this->media->pluck('url')->toArray();
    }

    /**
     * Check if ad has images
     */
    public function hasImages(): bool
    {
        return $this->media()->count() > 0;
    }

    /**
     * Get featured image (first image)
     */
    public function getFeaturedImageAttribute(): ?Media
    {
        return $this->media()->first();
    }

    /**
     * Get time remaining until next boost is available
     */
    public function getBoostAvailableInAttribute(): ?string
    {
        if ($this->canBeBosted()) {
            return null;
        }

        $hoursElapsed = now()->diffInHours($this->created_at);
        $hoursRemaining = 48 - $hoursElapsed;
        $minutesRemaining = (48 * 60) - now()->diffInMinutes($this->created_at);
        $minutesOnly = $minutesRemaining % 60;

        if ($hoursRemaining > 0) {
            return $hoursRemaining . 'h ' . $minutesOnly . 'm';
        } else {
            return $minutesOnly . 'm';
        }
    }

    /**
     * Boost the ad (update created_at and created_at)
     */
    public function boost(): bool
    {
        if (!$this->canBeBosted()) {
            return false;
        }

        $this->update([
            'created_at' => now(),
        ]);

        return true;
    }

    /**
     * Start sponsorship when admin approves the ad
     *
     * @return void
     */
    public function startSponsorship(): void
    {
        if ($this->sponsoredAd && $this->sponsoredAd->status === 'pending') {
            $this->sponsoredAd->startSponsorship();
        }
    }

    /**
     * Check if sponsored ad has expired
     *
     * @return bool
     */
    public function isSponsoredExpired(): bool
    {
        $sponsoredAd = $this->sponsoredAd;
        return $sponsoredAd ? $sponsoredAd->isExpired() : false;
    }

    /**
     * Get remaining sponsored minutes
     *
     * @return int
     */
    public function getRemainingMinutes(): int
    {
        $sponsoredAd = $this->sponsoredAd;
        return $sponsoredAd ? $sponsoredAd->calculateRemainingMinutes() : 0;
    }

    /**
     * Get sponsored ad status info
     *
     * @return array
     */
    public function getSponsoredInfo(): array
    {
        $sponsoredAd = $this->sponsoredAd;

        if (!$sponsoredAd) {
            return [
                'is_sponsored' => false,
                'is_active' => false,
                'remaining_minutes' => 0,
                'expires_at' => null,
            ];
        }

        return [
            'is_sponsored' => $this->is_sponsored,
            'is_active' => $sponsoredAd->is_active,
            'remaining_minutes' => $sponsoredAd->calculateRemainingMinutes(),
            'expires_at' => $sponsoredAd->expires_at,
            'started_at' => $sponsoredAd->started_at,
        ];
    }

}


