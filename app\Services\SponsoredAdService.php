<?php

namespace App\Services;

use App\Models\Ad;
use App\Models\SponsoredAd;
use App\Models\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

class SponsoredAdService
{
    /**
     * دمج الإعلانات الممولة مع الإعلانات العادية
     * كل 4 إعلانات عادية يظهر إعلان ممول
     */
    public function mergeWithRegularAds(Collection $regularAds, array $filters = []): Collection
    {
        // جلب الإعلانات الممولة المناسبة للفلاتر
        $sponsoredAds = $this->getSponsoredAdsForFilters($filters);
        
        if ($sponsoredAds->isEmpty()) {
            return $regularAds;
        }

        $mergedAds = new Collection();
        $sponsoredIndex = 0;
        $regularChunks = $regularAds->chunk(4);

        foreach ($regularChunks as $chunk) {
            // إضافة الـ 4 إعلانات العادية
            $mergedAds = $mergedAds->merge($chunk);
            
            // إضافة إعلان ممول إذا كان متاح
            if (isset($sponsoredAds[$sponsoredIndex])) {
                $mergedAds->push($sponsoredAds[$sponsoredIndex]);
                $sponsoredIndex++;
            }
        }

        return $mergedAds;
    }

    /**
     * جلب الإعلانات الممولة حسب الفلاتر
     */
    public function getSponsoredAdsForFilters(array $filters = []): Collection
    {
        $cacheKey = 'sponsored_ads_' . md5(serialize($filters));
        
        return Cache::remember($cacheKey, now()->addMinutes(10), function () use ($filters) {
            $query = Ad::with([
                'user:id,name,avatar,username,rank,is_trusted',
                'media',
                'category:id,name,slug',
                'sponsoredAd'
            ])
            ->whereHas('sponsoredAd', function ($q) {
                $q->where('status', 'active')
                  ->where('is_active', true)
                  ->where('expires_at', '>', now()); // الاعتماد على expires_at للأمان
            });

            // فلتر الكاتوجري - البحث في الفئة الرئيسية فقط
            if (!empty($filters['category'])) {
                $category = Category::where('slug', $filters['category'])->first();
                if ($category) {
                    // إذا كانت فئة فرعية، نبحث في الفئة الرئيسية
                    $rootCategory = $category->isRoot() ? $category : $category->ancestors()->first();
                    if ($rootCategory) {
                        $categoryIds = $rootCategory->descendants()->pluck('id')->push($rootCategory->id);
                        $query->whereIn('category_id', $categoryIds);
                    }
                }
            }

            // فلتر البحث النصي
            if (!empty($filters['search'])) {
                $searchTerm = $filters['search'];
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('title', 'like', "%{$searchTerm}%")
                      ->orWhere('description', 'like', "%{$searchTerm}%");
                });
            }

            // ترتيب حسب أعلى سعر مدفوع أو عشوائي
            return $query->orderByDesc(function ($q) {
                $q->select('cost')
                  ->from('sponsored_ads')
                  ->whereColumn('sponsored_ads.ad_id', 'ads.id')
                  ->limit(1);
            })
            ->inRandomOrder() // عشوائية إضافية للإعلانات بنفس السعر
            ->limit(20) // حد أقصى للإعلانات الممولة
            ->get();
        });
    }

    /**
     * جلب الإعلانات الممولة المرتبطة بإعلان معين (للصفحة الفردية) مع الفلترة المتقدمة
     */
    public function getRelatedSponsoredAds(Ad $ad, int $limit = 3): Collection
    {
        $cacheKey = "related_sponsored_ads_{$ad->id}_{$limit}";

        return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($ad, $limit) {
            // الحصول على الفئة الرئيسية
            $rootCategory = $ad->category->isRoot() ? $ad->category : $ad->category->ancestors()->first();

            if (!$rootCategory) {
                return collect();
            }

            // جلب جميع الفئات التابعة للفئة الرئيسية
            $categoryIds = $rootCategory->descendants()->pluck('id')->push($rootCategory->id);

            return Ad::with([
                'user:id,name,avatar,username,rank,is_trusted',
                'media',
                'category:id,name,slug',
                'sponsoredAd'
            ])
            ->where('id', '!=', $ad->id) // استبعاد الإعلان الحالي
            ->whereIn('category_id', $categoryIds) // نفس الفئة الرئيسية
            ->whereHas('sponsoredAd', function ($q) use ($ad) {
                $q->where('status', 'active')
                  ->where('is_active', true)
                  ->where('expires_at', '>', now())
                  ->where('show_in_details', true) // يمكن عرضه في صفحة التفاصيل
                  // فلترة جغرافية متقدمة
                  ->where(function ($locationQuery) use ($ad) {
                      $locationQuery->whereNull('country_id')
                                  ->orWhere('country_id', $ad->country_id);
                  })
                  ->where(function ($locationQuery) use ($ad) {
                      $locationQuery->whereNull('state_id')
                                  ->orWhere('state_id', $ad->state_id);
                  })
                  ->where(function ($locationQuery) use ($ad) {
                      $locationQuery->whereNull('city_id')
                                  ->orWhere('city_id', $ad->city_id);
                  })
                  // فلترة حسب الفئات المستهدفة
                  ->where(function ($categoryQuery) use ($rootCategory) {
                      $categoryQuery->whereNull('target_categories')
                                  ->orWhereJsonContains('target_categories', $rootCategory->id);
                  });
            })
            ->orderByDesc(function ($q) {
                // ترتيب حسب الأولوية أولاً (1 = أعلى أولوية)
                $q->select('priority')
                  ->from('sponsored_ads')
                  ->whereColumn('sponsored_ads.ad_id', 'ads.id')
                  ->limit(1);
            })
            ->orderByDesc(function ($q) {
                // ثم حسب التكلفة
                $q->select('cost')
                  ->from('sponsored_ads')
                  ->whereColumn('sponsored_ads.ad_id', 'ads.id')
                  ->limit(1);
            })
            ->inRandomOrder() // عشوائية للتنويع
            ->limit($limit)
            ->get();
        });
    }

    /**
     * تحديث إحصائيات الظهور للإعلان الممول
     */
    public function incrementViews(Ad $ad): void
    {
        if ($ad->sponsoredAd && $ad->sponsoredAd->isActive()) {
            $ad->sponsoredAd->increment('views_count');
        }
    }

    /**
     * تحديث إحصائيات النقرات للإعلان الممول
     */
    public function incrementClicks(Ad $ad): void
    {
        if ($ad->sponsoredAd && $ad->sponsoredAd->isActive()) {
            $ad->sponsoredAd->increment('clicks_count');
        }
    }

    /**
     * حساب معدل النقر للإعلان الممول (CTR)
     */
    public function calculateCTR(SponsoredAd $sponsoredAd): float
    {
        if ($sponsoredAd->views_count == 0) {
            return 0.0;
        }

        return round(($sponsoredAd->clicks_count / $sponsoredAd->views_count) * 100, 2);
    }

    /**
     * جلب أفضل الإعلانات الممولة أداءً
     */
    public function getTopPerformingSponsoredAds(int $limit = 10): Collection
    {
        return SponsoredAd::with(['ad.user', 'ad.media', 'ad.category'])
            ->where('status', 'active')
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->where('views_count', '>', 0)
            ->orderByRaw('(clicks_count / views_count) DESC')
            ->orderByDesc('cost')
            ->limit($limit)
            ->get();
    }

    /**
     * مسح الكاش المرتبط بالإعلانات الممولة
     */
    public function clearCache(): void
    {
        Cache::forget('sponsored_ads_*');
        Cache::forget('related_sponsored_ads_*');
    }
}
