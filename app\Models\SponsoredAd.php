<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class SponsoredAd extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'cost',
        'total_minutes',
        'started_at',
        'expires_at',
        'is_active',
        'status',
        'views_count',
        'clicks_count',
        'ctr',
        'priority',
        'country_id',
        'state_id',
        'city_id',
        'show_in_search',
        'show_in_details',
        'target_categories'
    ];

    protected $casts = [
        'cost' => 'decimal:2',
        'total_minutes' => 'integer',
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'views_count' => 'integer',
        'clicks_count' => 'integer',
        'ctr' => 'decimal:2',
        'priority' => 'integer',
        'show_in_search' => 'boolean',
        'show_in_details' => 'boolean',
        'target_categories' => 'array'
    ];

    /**
     * العلاقة مع الإعلان
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * العلاقة مع الدولة
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * العلاقة مع الولاية/المحافظة
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * العلاقة مع المدينة
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * حساب الدقائق من التكلفة
     * كل 100 جنيه = 60 دقيقة
     */
    public static function calculateMinutes(float $cost): int
    {
        return (int) (($cost / 100) * 60);
    }

    /**
     * بدء الرعاية (عند موافقة الأدمن)
     */
    public function startSponsorship(): void
    {
        $this->update([
            'started_at' => now(),
            'expires_at' => now()->addMinutes($this->total_minutes),
            'is_active' => true,
            'status' => 'active'
        ]);

        // مسح الكاش المتعلق بالإعلانات الممولة
        $this->clearSponsoredAdsCache();
    }



    /**
     * رفض الرعاية
     */
    public function rejectSponsorship(): void
    {
        $this->update([
            'is_active' => false,
            'status' => 'rejected'
        ]);

        // مسح الكاش المتعلق بالإعلانات الممولة
        $this->clearSponsoredAdsCache();
    }

    /**
     * التحقق من انتهاء الرعاية بناءً على expires_at
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * التحقق من كون الرعاية نشطة فعلياً (أمان مضاعف)
     */
    public function isActive(): bool
    {
        return $this->is_active &&
               $this->status === 'active' &&
               !$this->isExpired();
    }

    /**
     * مسح الكاش المتعلق بالإعلانات الممولة
     */
private function clearSponsoredAdsCache(): void
{
    if (Cache::getStore() instanceof \Illuminate\Cache\TaggableStore) {
        Cache::tags(['sponsored_ads', 'ads'])->flush();
    } else {
        // fallback simple clear
        Cache::forget('sponsored_ads');
        Cache::forget('ads');
    }
}


    /**
     * إنهاء الرعاية (تحديث محسن مع مسح الكاش)
     */
    public function expireSponsorship(): void
    {
        $this->update([
            'is_active' => false,
            'status' => 'expired'
        ]);

        // مسح الكاش المتعلق بالإعلانات الممولة
        $this->clearSponsoredAdsCache();
    }





    /**
     * الحصول على الوقت المتبقي بالدقائق
     */
    public function getRemainingMinutes(): int
    {
        if (!$this->expires_at || $this->isExpired()) {
            return 0;
        }

        return (int) now()->diffInMinutes($this->expires_at);
    }

    /**
     * الحصول على الوقت المتبقي كنص
     */
    public function getRemainingTimeText(): string
    {
        $minutes = $this->getRemainingMinutes();

        if ($minutes <= 0) {
            return 'انتهت';
        }

        $hours = intval($minutes / 60);
        $remainingMinutes = $minutes % 60;

        if ($hours > 0) {
            return "{$hours} ساعة و {$remainingMinutes} دقيقة";
        }

        return "{$remainingMinutes} دقيقة";
    }

    /**
     * Scope للإعلانات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('status', 'active')
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope للإعلانات المنتهية
     */
    public function scopeExpired($query)
    {
        return $query->where(function($q) {
            $q->where('status', 'expired')
              ->orWhere('expires_at', '<=', now());
        });
    }

    /**
     * Scope للإعلانات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * تحديث معدل النقر (CTR)
     */
    public function updateCTR(): void
    {
        if ($this->views_count > 0) {
            $this->ctr = round(($this->clicks_count / $this->views_count) * 100, 2);
            $this->save();
        }
    }

    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews(): void
    {
        $this->increment('views_count');
        $this->updateCTR();
    }

    /**
     * زيادة عدد النقرات
     */
    public function incrementClicks(): void
    {
        $this->increment('clicks_count');
        $this->updateCTR();
    }

    /**
     * الحصول على معدل النقر كنص
     */
    public function getCTRText(): string
    {
        return $this->ctr . '%';
    }

    /**
     * الحصول على نص الأولوية
     */
    public function getPriorityText(): string
    {
        return match($this->priority) {
            1 => 'عالية جداً',
            2 => 'عالية',
            3 => 'متوسطة',
            4 => 'منخفضة',
            5 => 'منخفضة جداً',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على لون الأولوية
     */
    public function getPriorityColor(): string
    {
        return match($this->priority) {
            1 => '#dc3545', // أحمر
            2 => '#fd7e14', // برتقالي
            3 => '#ffc107', // أصفر
            4 => '#20c997', // أخضر فاتح
            5 => '#6c757d', // رمادي
            default => '#6c757d'
        };
    }

    /**
     * التحقق من إمكانية العرض في البحث
     */
    public function canShowInSearch(): bool
    {
        return $this->show_in_search && $this->isActive();
    }

    /**
     * التحقق من إمكانية العرض في صفحة التفاصيل
     */
    public function canShowInDetails(): bool
    {
        return $this->show_in_details && $this->isActive();
    }

    /**
     * التحقق من تطابق الموقع
     */
    public function matchesLocation(?int $countryId, ?int $stateId, ?string $cityId): bool
    {
        // إذا لم يتم تحديد موقع للإعلان الممول، فهو يظهر في كل مكان
        if (!$this->country_id && !$this->state_id && !$this->city_id) {
            return true;
        }

        // التحقق من تطابق الدولة
        if ($this->country_id && $this->country_id != $countryId) {
            return false;
        }

        // التحقق من تطابق الولاية/المحافظة
        if ($this->state_id && $this->state_id != $stateId) {
            return false;
        }

        // التحقق من تطابق المدينة
        if ($this->city_id && $this->city_id != $cityId) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من تطابق الفئات المستهدفة
     */
    public function matchesTargetCategories(?int $categoryId): bool
    {
        // إذا لم يتم تحديد فئات مستهدفة، فالإعلان يظهر في كل الفئات
        if (!$this->target_categories || empty($this->target_categories)) {
            return true;
        }

        return in_array($categoryId, $this->target_categories);
    }
}
