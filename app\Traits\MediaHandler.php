<?php

namespace App\Traits;

use App\Enums\StorageDiskType;
use Illuminate\Http\UploadedFile;
use App\Models\Media;
use App\Models\User;
use App\Repositories\Media\MediaRepository;
use App\Services\Media\MediaStorageService;
use App\Services\SimpleFileCompressionService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

trait MediaHandler
{
    /**
     * Upload media files.
     * 
     * @param Model $model
     * @param array $files
     * @param StorageDiskType $disk
     * @param string $directory
     * @param User $user
     * @param int $width
     * @param int $height
     * @return void
     */
    public function uploadMedia(Model $model, array $files, StorageDiskType $disk, string $directory, int $width = 1024, int $height = 768, ?User $user = null): void
    {
        $compressionService = new SimpleFileCompressionService();
        $mediaRepository = app(MediaRepository::class);

        foreach ($files as $file) {
            // Validate image using compression service
            $validation = $compressionService->validateImage($file);
            if (!$validation['valid']) {
                Log::warning('Invalid image file rejected', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $validation['error']
                ]);
                continue;
            }

            // Determine image type based on directory
            $imageType = match($directory) {
                'ad' => 'ad_images',
                'avatars' => 'profile_avatar',
                'identity-verification' => 'identity_documents',
                'broker-documents' => 'broker_documents',
                default => 'ad_images'
            };

            // Get optimized settings
            $settings = $compressionService->getOptimizedSettings($imageType);

            // Override dimensions if provided
            if ($width !== 1024 || $height !== 768) {
                $settings['max_width'] = $width;
                $settings['max_height'] = $height;
            }

            // Compress and store image
            $result = $compressionService->compressAndStore($file, $directory, $settings);

            if ($result['success']) {
                // Create media record
                $media = $mediaRepository->create($user, [
                    'name' => $file->getClientOriginalName(),
                    'path' => $result['path'],
                    'url' => $result['path'], // Store relative path
                    'extension' => $result['format'],
                    'mime_type' => 'image/' . $result['format'],
                    'size' => $result['compressed_size'],
                    'storage' => $disk,
                ]);

                // Associate with model
                $model->media()->save($media);

                Log::info('Compressed media uploaded successfully', [
                    'model' => get_class($model),
                    'model_id' => $model->id,
                    'media_id' => $media->id,
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%',
                    'format' => $result['format']
                ]);
            } else {
                Log::error('Failed to compress and upload media', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $result['error']
                ]);
            }
        }
    }

    /**
     * Store media file.
     * 
     * @param UploadedFile $file
     * @param string $directory
     * @param StorageDiskType $disk
     * @param ?User $user|null
     * @param int $width
     * @param int $height
     * @return Media
     */
    protected function storeMedia(UploadedFile $file, string $directory, StorageDiskType $disk, int $width, int $height, ?User $user = null): Media
    {
        $mediaStorage = new MediaStorageService($disk);
        $uploadedMedia = $mediaStorage->upload($file, $directory, $width, $height);
        $media = app(MediaRepository::class)->create($user, [
            'name' => $file->getClientOriginalName(),
            // 'type' => $file->getMimeType(),
            'path' => $uploadedMedia['path'],
            'url' => $uploadedMedia['url'],
            'extension' => $file->getClientOriginalExtension(),
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'storage' => $disk
        ]);
        return $media;
    }

    /**
     * Delete media file.
     * 
     * @param Media $media
     * @return void
     */
    protected function deleteMediaFile(Media $media): void
    {
        $mediaStorage = new MediaStorageService($media->storage);
        $mediaStorage->delete($media->path);
        $media->delete();
    }

    /**
     * Basic validation for user-friendly experience
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function isBasicValidImageFile(UploadedFile $file): bool
    {
        // Check file extension - أكثر مرونة
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp'];
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return false;
        }

        // Check file size (max 10MB) - أكثر مرونة
        if ($file->getSize() > 10 * 1024 * 1024) {
            return false;
        }

        // فقط تحقق بسيط من أنه صورة
        $imageInfo = @getimagesize($file->getRealPath());
        if ($imageInfo === false) {
            // حتى لو فشل getimagesize، نقبل الملف إذا كان extension صحيح
            return true;
        }

        return true;
    }

    /**
     * Strict validation for security (للاستخدام المستقبلي)
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function isValidImageFile(UploadedFile $file): bool
    {
        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return false;
        }

        // Check MIME type
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            return false;
        }

        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return false;
        }

        // Check if it's actually an image using getimagesize
        $imageInfo = @getimagesize($file->getRealPath());
        if ($imageInfo === false) {
            return false;
        }

        // Check image dimensions
        [$width, $height] = $imageInfo;
        if ($width < 200 || $height < 200 || $width > 4000 || $height > 4000) {
            return false;
        }

        // Check for malicious content in EXIF data
        if (function_exists('exif_read_data')) {
            $exif = @exif_read_data($file->getRealPath());
            if ($exif && isset($exif['UserComment'])) {
                // Check for suspicious content in EXIF
                $suspiciousPatterns = ['<script', '<?php', 'javascript:', 'data:'];
                foreach ($suspiciousPatterns as $pattern) {
                    if (stripos($exif['UserComment'], $pattern) !== false) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Upload and compress media files with optimized settings.
     *
     * @param Model $model
     * @param array $files
     * @param string $imageType
     * @param string $directory
     * @param User $user
     * @return void
     */
    public function uploadCompressedMedia(Model $model, array $files, string $imageType, string $directory, ?User $user = null): void
    {
        $compressionService = new SimpleFileCompressionService();
        $mediaRepository = app(MediaRepository::class);

        foreach ($files as $file) {
            // Validate image
            $validation = $compressionService->validateImage($file);
            if (!$validation['valid']) {
                Log::warning('Invalid image file rejected', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $validation['error']
                ]);
                continue;
            }

            // Get optimized settings for image type
            $settings = $compressionService->getOptimizedSettings($imageType);

            // Compress and store image
            $result = $compressionService->compressAndStore($file, $directory, $settings);

            if ($result['success']) {
                // Create media record
                $media = $mediaRepository->create($user, [
                    'name' => $file->getClientOriginalName(),
                    'path' => $result['path'],
                    'url' => $result['path'], // Store relative path
                    'extension' => $result['format'],
                    'mime_type' => 'image/' . $result['format'],
                    'size' => $result['compressed_size'],
                    'storage' => StorageDiskType::LOCAL,
                ]);

                // Associate with model
                $model->media()->save($media);

                Log::info('Compressed media uploaded successfully', [
                    'model' => get_class($model),
                    'model_id' => $model->id,
                    'media_id' => $media->id,
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%',
                    'format' => $result['format']
                ]);
            } else {
                Log::error('Failed to compress and upload media', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $result['error']
                ]);
            }
        }
    }
}

