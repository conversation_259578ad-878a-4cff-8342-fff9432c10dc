<?php

namespace App\Traits;

use App\Enums\StorageDiskType;
use Illuminate\Http\UploadedFile;
use App\Models\Media;
use App\Models\User;
use App\Repositories\Media\MediaRepository;
use App\Services\Media\MediaStorageService;
use App\Services\SimpleImageCompressionService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

trait MediaHandler
{
    /**
     * Upload media files.
     * 
     * @param Model $model
     * @param array $files
     * @param StorageDiskType $disk
     * @param string $directory
     * @param User $user
     * @param int $width
     * @param int $height
     * @return void
     */
    public function uploadMedia(Model $model, array $files, StorageDiskType $disk, string $directory, int $width = 1024, int $height = 768, ?User $user = null): void
    {
        $compressionService = new SimpleImageCompressionService();
        $mediaRepository = app(MediaRepository::class);

        foreach ($files as $file) {
            try {
                // Validate image using compression service
                $validation = $compressionService->validateImage($file, 25); // Increase limit to 25MB
                if (!$validation['valid']) {
                    Log::warning('Invalid image file rejected', [
                        'filename' => $file->getClientOriginalName(),
                        'error' => $validation['error']
                    ]);
                    continue;
                }

                // Determine image type based on directory
                $imageType = match($directory) {
                    'ad' => 'ad_images',
                    'avatars' => 'profile_avatar',
                    'identity-verification' => 'identity_documents',
                    'broker-documents' => 'broker_documents',
                    default => 'ad_images'
                };

                // Get optimized settings
                $settings = $compressionService->getOptimizedSettings($imageType);

                // Override dimensions if provided
                if ($width !== 1024 || $height !== 768) {
                    $settings['max_width'] = $width;
                    $settings['max_height'] = $height;
                }

                // Compress and store image
                $result = $compressionService->compressAndStore($file, $directory, $settings);

                if ($result['success']) {
                    // Create media record
                    $media = $mediaRepository->create($user, [
                        'name' => $file->getClientOriginalName(),
                        'path' => $result['path'],
                        'url' => $result['path'], // Store relative path
                        'extension' => $result['format'],
                        'mime_type' => 'image/' . $result['format'],
                        'size' => $result['compressed_size'],
                        'storage' => $disk,
                    ]);

                    // Associate with model
                    $model->media()->save($media);

                    Log::info('Compressed media uploaded successfully', [
                        'model' => get_class($model),
                        'model_id' => $model->id,
                        'media_id' => $media->id,
                        'original_size' => $compressionService->formatFileSize($result['original_size']),
                        'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                        'compression_ratio' => $result['compression_ratio'] . '%',
                        'format' => $result['format']
                    ]);
                } else {
                    Log::error('Failed to compress and upload media', [
                        'filename' => $file->getClientOriginalName(),
                        'error' => $result['error']
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Exception during media upload', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
    }

    /**
     * Store media file.
     * 
     * @param UploadedFile $file
     * @param string $directory
     * @param StorageDiskType $disk
     * @param ?User $user|null
     * @param int $width
     * @param int $height
     * @return Media
     */
    protected function storeMedia(UploadedFile $file, string $directory, StorageDiskType $disk, int $width, int $height, ?User $user = null): Media
    {
        $mediaStorage = new MediaStorageService($disk);
        $uploadedMedia = $mediaStorage->upload($file, $directory, $width, $height);
        $media = app(MediaRepository::class)->create($user, [
            'name' => $file->getClientOriginalName(),
            // 'type' => $file->getMimeType(),
            'path' => $uploadedMedia['path'],
            'url' => $uploadedMedia['url'],
            'extension' => $file->getClientOriginalExtension(),
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'storage' => $disk
        ]);
        return $media;
    }

    /**
     * Delete media file.
     * 
     * @param Media $media
     * @return void
     */
    protected function deleteMediaFile(Media $media): void
    {
        $mediaStorage = new MediaStorageService($media->storage);
        $mediaStorage->delete($media->path);
        $media->delete();
    }

    /**
     * Basic validation for user-friendly experience
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function isBasicValidImageFile(UploadedFile $file): bool
    {
        // Check file extension - أكثر مرونة
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp'];
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return false;
        }

        // Check file size (max 10MB) - أكثر مرونة
        if ($file->getSize() > 10 * 1024 * 1024) {
            return false;
        }

        // فقط تحقق بسيط من أنه صورة
        $imageInfo = @getimagesize($file->getRealPath());
        if ($imageInfo === false) {
            // حتى لو فشل getimagesize، نقبل الملف إذا كان extension صحيح
            return true;
        }

        return true;
    }

    /**
     * Strict validation for security (للاستخدام المستقبلي)
     *
     * @param UploadedFile $file
     * @return bool
     */
    private function isValidImageFile(UploadedFile $file): bool
    {
        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return false;
        }

        // Check MIME type
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            return false;
        }

        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return false;
        }

        // Check if it's actually an image using getimagesize
        $imageInfo = @getimagesize($file->getRealPath());
        if ($imageInfo === false) {
            return false;
        }

        // Check image dimensions
        [$width, $height] = $imageInfo;
        if ($width < 200 || $height < 200 || $width > 4000 || $height > 4000) {
            return false;
        }

        // Check for malicious content in EXIF data
        if (function_exists('exif_read_data')) {
            $exif = @exif_read_data($file->getRealPath());
            if ($exif && isset($exif['UserComment'])) {
                // Check for suspicious content in EXIF
                $suspiciousPatterns = ['<script', '<?php', 'javascript:', 'data:'];
                foreach ($suspiciousPatterns as $pattern) {
                    if (stripos($exif['UserComment'], $pattern) !== false) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Upload and compress media files with optimized settings.
     *
     * @param Model $model
     * @param array $files
     * @param string $imageType
     * @param string $directory
     * @param User $user
     * @return void
     */
    public function uploadCompressedMedia(Model $model, array $files, string $imageType, string $directory, ?User $user = null): void
    {
        $compressionService = new SimpleFileCompressionService();
        $mediaRepository = app(MediaRepository::class);

        foreach ($files as $file) {
            // Validate image
            $validation = $compressionService->validateImage($file);
            if (!$validation['valid']) {
                Log::warning('Invalid image file rejected', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $validation['error']
                ]);
                continue;
            }

            // Get optimized settings for image type
            $settings = $compressionService->getOptimizedSettings($imageType);

            // Compress and store image
            $result = $compressionService->compressAndStore($file, $directory, $settings);

            if ($result['success']) {
                // Create media record
                $media = $mediaRepository->create($user, [
                    'name' => $file->getClientOriginalName(),
                    'path' => $result['path'],
                    'url' => $result['path'], // Store relative path
                    'extension' => $result['format'],
                    'mime_type' => 'image/' . $result['format'],
                    'size' => $result['compressed_size'],
                    'storage' => StorageDiskType::LOCAL,
                ]);

                // Associate with model
                $model->media()->save($media);

                Log::info('Compressed media uploaded successfully', [
                    'model' => get_class($model),
                    'model_id' => $model->id,
                    'media_id' => $media->id,
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%',
                    'format' => $result['format']
                ]);
            } else {
                Log::error('Failed to compress and upload media', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $result['error']
                ]);
            }
        }
    }

    /**
     * Ultra compress media files for maximum size reduction
     *
     * @param Model $model
     * @param array $files
     * @param string $imageType
     * @param string $directory
     * @param User $user
     * @return void
     */
    public function uploadUltraCompressedMedia(Model $model, array $files, string $imageType, string $directory, ?User $user = null): void
    {
        $compressionService = new SimpleFileCompressionService();
        $advancedOptimizer = new AdvancedImageOptimizer();
        $mediaRepository = app(MediaRepository::class);

        foreach ($files as $file) {
            // Validate image
            $validation = $compressionService->validateImage($file);
            if (!$validation['valid']) {
                Log::warning('Invalid image file rejected', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $validation['error']
                ]);
                continue;
            }

            // Get ultra compression settings based on image type
            $ultraSettings = $this->getUltraCompressionSettings($imageType);

            // Apply ultra compression
            $result = $advancedOptimizer->ultraCompress($file, $ultraSettings);

            if ($result['success']) {
                // Generate filename and store
                $uuid = \Illuminate\Support\Str::uuid();
                $extension = $ultraSettings['format'] === 'webp' ? 'webp' : 'jpg';
                $filename = $uuid . '.' . $extension;
                $path = $directory . '/' . $filename;

                Storage::disk('public')->put($path, $result['compressed_data']);

                // Create media record
                $media = $mediaRepository->create($user, [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'url' => $path,
                    'extension' => $extension,
                    'mime_type' => 'image/' . $extension,
                    'size' => $result['compressed_size'],
                    'storage' => StorageDiskType::LOCAL,
                ]);

                // Associate with model
                $model->media()->save($media);

                Log::info('Ultra compressed media uploaded successfully', [
                    'filename' => $file->getClientOriginalName(),
                    'compressed_filename' => $filename,
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%',
                    'final_dimensions' => $result['final_dimensions'],
                    'techniques_applied' => $result['techniques_applied'],
                    'path' => $path
                ]);
            } else {
                Log::error('Failed to ultra compress image', [
                    'filename' => $file->getClientOriginalName(),
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }
        }
    }

    /**
     * Get ultra compression settings for different image types
     */
    private function getUltraCompressionSettings(string $imageType): array
    {
        return match ($imageType) {
            'ad_images' => [
                'target_size_kb' => 150,  // 150KB max for ad images
                'min_quality' => 30,
                'max_width' => 900,
                'max_height' => 900,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => true
            ],
            'profile_avatar' => [
                'target_size_kb' => 50,   // 50KB max for avatars
                'min_quality' => 35,
                'max_width' => 300,
                'max_height' => 300,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => true
            ],
            'identity_documents' => [
                'target_size_kb' => 300,  // 300KB max for documents (need readability)
                'min_quality' => 40,
                'max_width' => 1200,
                'max_height' => 900,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => false // Keep colors for document clarity
            ],
            'broker_documents' => [
                'target_size_kb' => 300,  // 300KB max for broker documents
                'min_quality' => 40,
                'max_width' => 1200,
                'max_height' => 900,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => false
            ],
            default => [
                'target_size_kb' => 200,
                'min_quality' => 30,
                'max_width' => 800,
                'max_height' => 800,
                'format' => 'webp',
                'progressive' => true,
                'strip_all_metadata' => true,
                'color_reduction' => true
            ]
        };
    }
}

