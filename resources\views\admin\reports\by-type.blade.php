@extends('partials.admin')

@section('title', $typeLabel . ' Reports')

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'reports'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => $typeLabel . ' Reports', 'hasBack' => true, 'backTitle' => 'Reports', 'backUrl' => route('admin.reports.index')])

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">{{ $typeLabel }} Reports ({{ $reports->total() }})</h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    Filter by Status
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="?status=pending">Pending</a></li>
                                    <li><a class="dropdown-item" href="?status=reviewed">Reviewed</a></li>
                                    <li><a class="dropdown-item" href="?status=resolved">Resolved</a></li>
                                    <li><a class="dropdown-item" href="?status=dismissed">Dismissed</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('admin.reports.by-type', $type) }}">All</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($reports->count() > 0)
                                <!-- Bulk Actions -->
                                <form method="POST" action="{{ route('admin.reports.bulk-update') }}" id="bulkForm">
                                    @csrf
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                            <label class="form-check-label" for="selectAll">
                                                Select All
                                            </label>
                                        </div>
                                        <div class="btn-group">
                                            <button type="submit" name="action" value="reviewed" class="btn btn-sm btn-outline-info" disabled id="bulkReviewed">
                                                <i class="fas fa-eye me-1"></i>Mark as Reviewed
                                            </button>
                                            <button type="submit" name="action" value="resolved" class="btn btn-sm btn-outline-success" disabled id="bulkResolved">
                                                <i class="fas fa-check me-1"></i>Mark as Resolved
                                            </button>
                                            <button type="submit" name="action" value="dismissed" class="btn btn-sm btn-outline-danger" disabled id="bulkDismissed">
                                                <i class="fas fa-times me-1"></i>Dismiss
                                            </button>
                                        </div>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th width="50">
                                                        <input type="checkbox" id="selectAllHeader" class="form-check-input">
                                                    </th>
                                                    <th>ID</th>
                                                    <th>Reporter</th>
                                                    <th>Content</th>
                                                    <th>Reason</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($reports as $report)
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" name="report_ids[]" value="{{ $report->id }}" class="form-check-input report-checkbox">
                                                        </td>
                                                        <td><code>{{ substr($report->id, 0, 8) }}</code></td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <img src="{{ $report->reporter->avatar ?? '/assets/images/default-avatar.png' }}" 
                                                                     alt="{{ $report->reporter->name }}" 
                                                                     class="avatar avatar-sm rounded-circle me-2">
                                                                <div>
                                                                    <div class="fw-semibold">{{ $report->reporter->name }}</div>
                                                                    <small class="text-muted">{{ $report->reporter->email }}</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            @if($report->reportable)
                                                                @if($report->reportable_type === 'App\Models\Ad')
                                                                    <div class="d-flex align-items-center">
                                                                        @if($report->reportable->media->first())
                                                                            <img src="{{ $report->reportable->media->first()->url }}" 
                                                                                 alt="{{ $report->reportable->title }}" 
                                                                                 class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                                        @endif
                                                                        <div>
                                                                            <div class="fw-semibold">{{ \Illuminate\Support\Str::limit($report->reportable->title, 30) }}</div>
                                                                            <small class="text-muted">${{ number_format($report->reportable->price) }}</small>
                                                                        </div>
                                                                    </div>
                                                                @elseif($report->reportable_type === 'App\Models\User')
                                                                    <div class="d-flex align-items-center">
                                                                        <img src="{{ $report->reportable->avatar ?? '/assets/images/default-avatar.png' }}" 
                                                                             alt="{{ $report->reportable->name }}" 
                                                                             class="avatar avatar-sm rounded-circle me-2">
                                                                        <div>
                                                                            <div class="fw-semibold">{{ $report->reportable->name }}</div>
                                                                            <small class="text-muted">{{ $report->reportable->email }}</small>
                                                                        </div>
                                                                    </div>
                                                                @elseif($report->reportable_type === 'App\Models\UserRating')
                                                                    <div>
                                                                        <div class="fw-semibold">Rating: {{ $report->reportable->rating }}/5</div>
                                                                        <small class="text-muted">{{ \Illuminate\Support\Str::limit($report->reportable->comment ?? 'No comment', 30) }}</small>
                                                                    </div>
                                                                @elseif($report->reportable_type === 'App\Models\ChMessage')
                                                                    <div>
                                                                        <div class="fw-semibold">Chat Message</div>
                                                                        <small class="text-muted">{{ \Illuminate\Support\Str::limit($report->reportable->body ?? 'No message', 30) }}</small>
                                                                    </div>
                                                                @endif
                                                            @else
                                                                <span class="text-muted">Content deleted</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-warning">{{ $report->reason_label }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-{{ $report->status === 'pending' ? 'warning' : ($report->status === 'resolved' ? 'success' : 'secondary') }}">
                                                                {{ $report->status_label }}
                                                            </span>
                                                        </td>
                                                        <td>{{ $report->created_at->diffForHumans() }}</td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="{{ route('admin.reports.show', $report) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>

                                                                <!-- Quick Warning Button -->
                                                                <button type="button" class="btn btn-sm btn-outline-warning"
                                                                        onclick="quickWarning('{{ $report->id }}', '{{ $report->reporter->id }}', '{{ $report->reporter->name }}')"
                                                                        title="Issue Warning">
                                                                    <i class="fas fa-exclamation-triangle"></i>
                                                                </button>

                                                                @if($report->status === 'pending')
                                                                    <form method="POST" action="{{ route('admin.reports.update-status', $report) }}" class="d-inline">
                                                                        @csrf
                                                                        @method('PATCH')
                                                                        <input type="hidden" name="status" value="resolved">
                                                                        <button type="submit" class="btn btn-sm btn-outline-success" title="Mark as Resolved">
                                                                            <i class="fas fa-check"></i>
                                                                        </button>
                                                                    </form>
                                                                    <form method="POST" action="{{ route('admin.reports.update-status', $report) }}" class="d-inline">
                                                                        @csrf
                                                                        @method('PATCH')
                                                                        <input type="hidden" name="status" value="dismissed">
                                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Dismiss Report">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                    </form>
                                                                @endif
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </form>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-center">
                                    {{ $reports->links() }}
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                                    <h5>No {{ $typeLabel }} Reports Found</h5>
                                    <p class="text-muted">There are no {{ strtolower($typeLabel) }} reports to display at the moment.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const selectAllHeader = document.getElementById('selectAllHeader');
    const checkboxes = document.querySelectorAll('.report-checkbox');
    const bulkButtons = document.querySelectorAll('#bulkReviewed, #bulkResolved, #bulkDismissed');

    // Handle select all functionality
    [selectAll, selectAllHeader].forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateBulkButtons();
            // Sync both select all checkboxes
            selectAll.checked = this.checked;
            selectAllHeader.checked = this.checked;
        });
    });

    // Handle individual checkbox changes
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkButtons);
    });

    function updateBulkButtons() {
        const checkedCount = document.querySelectorAll('.report-checkbox:checked').length;
        bulkButtons.forEach(btn => {
            btn.disabled = checkedCount === 0;
        });
        
        // Update select all checkbox state
        const allChecked = checkedCount === checkboxes.length;
        const someChecked = checkedCount > 0;
        
        selectAll.checked = allChecked;
        selectAll.indeterminate = someChecked && !allChecked;
        selectAllHeader.checked = allChecked;
        selectAllHeader.indeterminate = someChecked && !allChecked;
    }

    // Handle bulk form submission
    document.getElementById('bulkForm').addEventListener('submit', function(e) {
        const checkedCount = document.querySelectorAll('.report-checkbox:checked').length;
        if (checkedCount === 0) {
            e.preventDefault();
            alert('Please select at least one report.');
            return;
        }
        
        const action = e.submitter.value;
        const actionText = e.submitter.textContent.trim();
        
        if (!confirm(`Are you sure you want to ${actionText.toLowerCase()} ${checkedCount} report(s)?`)) {
            e.preventDefault();
        }
    });
});

function quickWarning(reportId, userId, userName) {
    // You can implement a quick warning modal here
    // For now, redirect to the report details page
    window.location.href = `/admin/reports/show/${reportId}`;
}

function quickDeleteContent(reportId, action) {
    if (!confirm('Are you sure you want to delete this content? This action cannot be undone.')) {
        return;
    }

    fetch('{{ route("admin.warnings.delete-content") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            report_id: reportId,
            action: action
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Refresh page after 2 seconds
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while deleting content.');
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.main-container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);

        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
@endpush

@endsection
