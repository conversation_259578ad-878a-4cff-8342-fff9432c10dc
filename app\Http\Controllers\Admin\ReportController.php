<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    /**
     * Display reports dashboard.
     */
    public function index(): View
    {
        $stats = [
            'total' => Report::count(),
            'pending' => Report::pending()->count(),
            'ads' => Report::byType(Report::TYPE_AD)->count(),
            'comments' => Report::byType(Report::TYPE_COMMENT)->count(),
            'users' => Report::byType(Report::TYPE_USER)->count(),
            'reviews' => Report::byType(Report::TYPE_REVIEW)->count(),
            'user_ratings' => Report::byType(Report::TYPE_USER_RATING)->count(),
            'messages' => Report::byType(Report::TYPE_MESSAGE)->count(),
        ];

        $recentReports = Report::with(['reporter', 'reportable'])
            ->when(request('status'), function($query, $status) {
                return $query->where('status', $status);
            })
            ->latest()
            ->take(10)
            ->get();

        return view('admin.reports.index', compact('stats', 'recentReports'));
    }

    /**
     * Display reports by type.
     */
    public function byType(Request $request, string $type): View
    {
        $typeMap = [
            'ads' => Report::TYPE_AD,
            'comments' => Report::TYPE_COMMENT,
            'users' => Report::TYPE_USER,
            'reviews' => Report::TYPE_REVIEW,
            'user-ratings' => Report::TYPE_USER_RATING,
            'messages' => Report::TYPE_MESSAGE,
        ];

        if (!isset($typeMap[$type])) {
            abort(404);
        }

        $reports = Report::with(['reporter', 'reportable'])
            ->byType($typeMap[$type])
            ->when($request->status, function($query, $status) {
                return $query->where('status', $status);
            })
            ->latest()
            ->paginate(20);

        $typeLabel = match($type) {
            'ads' => 'الإعلانات',
            'comments' => 'التعليقات',
            'users' => 'المستخدمين',
            'reviews' => 'التقييمات',
            'user-ratings' => 'تقييمات المستخدمين',
            'messages' => 'الرسائل',
        };

        return view('admin.reports.by-type', compact('reports', 'type', 'typeLabel'));
    }

    /**
     * Show report details.
     */
    public function show(Report $report): View
    {
        $report->load(['reporter', 'reportable', 'reviewer']);

        return view('admin.reports.show', compact('report'));
    }

    /**
     * Update report status.
     */
    public function updateStatus(Request $request, Report $report): RedirectResponse
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,resolved,dismissed',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        $report->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
            'reviewed_by' => Auth::guard('admin_web')->id(),
            'reviewed_at' => now()
        ]);

        return redirect()->back()->with('success', 'تم تحديث حالة الإبلاغ بنجاح');
    }

    /**
     * Bulk update reports.
     */
    public function bulkUpdate(Request $request): RedirectResponse
    {
        $request->validate([
            'report_ids' => 'required|array',
            'report_ids.*' => 'exists:reports,id',
            'action' => 'required|in:reviewed,resolved,dismissed'
        ]);

        Report::whereIn('id', $request->report_ids)
            ->update([
                'status' => $request->action,
                'reviewed_by' => Auth::guard('admin')->id(),
                'reviewed_at' => now()
            ]);

        $count = count($request->report_ids);
        return redirect()->back()->with('success', "تم تحديث {$count} إبلاغ بنجاح");
    }
}
