@extends('partials.admin')

@section('title', 'إدارة طلبات المندوبين')

@include('layouts.header', ['admin' => true])
@section('content')

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-tie me-2"></i>
            إدارة طلبات المندوبين
        </h1>
        <button class="btn btn-danger btn-sm" onclick="deleteOldRejected()">
            <i class="fas fa-trash me-2"></i>
            حذف المرفوضة القديمة
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                في انتظار المراجعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المرحلة الأولى
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['first_stage'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                المرحلة الثانية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['second_stage'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-upload fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                مرفوضة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['rejected'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائية المقبولين نهائياً -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                مقبولين نهائياً
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['accepted_brokers'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-tabs" id="applicationTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                <i class="fas fa-clock me-2"></i>
                في انتظار المراجعة ({{ $stats['pending'] }})
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="first-stage-tab" data-bs-toggle="tab" data-bs-target="#first-stage" type="button" role="tab">
                <i class="fas fa-check-circle me-2"></i>
                المرحلة الأولى ({{ $stats['first_stage'] }})
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="second-stage-tab" data-bs-toggle="tab" data-bs-target="#second-stage" type="button" role="tab">
                <i class="fas fa-upload me-2"></i>
                المرحلة الثانية ({{ $stats['second_stage'] }})
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="accepted-brokers-tab" data-bs-toggle="tab" data-bs-target="#accepted-brokers" type="button" role="tab">
                <i class="fas fa-user-check me-2"></i>
                المقبولين نهائياً ({{ $stats['accepted_brokers'] }})
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab">
                <i class="fas fa-times-circle me-2"></i>
                مرفوضة ({{ $stats['rejected'] }})
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="applicationTabsContent">
        <!-- Pending Applications -->
        <div class="tab-pane fade show active" id="pending" role="tabpanel">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock me-2"></i>
                        الطلبات في انتظار المراجعة
                    </h6>
                </div>
                <div class="card-body">
                    @if($pendingApplications->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المتقدم</th>
                                        <th>تاريخ التقديم</th>
                                        <th>المؤهل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingApplications as $application)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $application->user->avatar_url }}" 
                                                         class="rounded-circle me-3" 
                                                         width="40" height="40">
                                                    <div>
                                                        <div class="fw-bold">{{ $application->full_name }}</div>
                                                        <div class="text-muted small">{{ $application->user->email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $application->created_at->format('Y-m-d H:i') }}</td>
                                            <td>{{ \Illuminate\Support\Str::limit($application->education, 50) }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.broker-applications.show', $application) }}" 
                                                       class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-success btn-sm" 
                                                            onclick="acceptFirstStage({{ $application->id }})">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-danger btn-sm" 
                                                            onclick="rejectApplication({{ $application->id }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد طلبات في انتظار المراجعة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- First Stage Accepted -->
        <div class="tab-pane fade" id="first-stage" role="tabpanel">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-check-circle me-2"></i>
                        المقبولين في المرحلة الأولى
                    </h6>
                </div>
                <div class="card-body">
                    @if($firstStageAccepted->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المتقدم</th>
                                        <th>تاريخ القبول</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($firstStageAccepted as $application)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $application->user->avatar_url }}" 
                                                         class="rounded-circle me-3" 
                                                         width="40" height="40">
                                                    <div>
                                                        <div class="fw-bold">{{ $application->full_name }}</div>
                                                        <div class="text-muted small">{{ $application->user->email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $application->updated_at->format('Y-m-d H:i') }}</td>
                                            <td>
                                                <span class="badge bg-info">في انتظار رفع المستندات</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.broker-applications.show', $application) }}" 
                                                       class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-danger btn-sm" 
                                                            onclick="rejectApplication({{ $application->id }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد طلبات مقبولة في المرحلة الأولى</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Second Stage Accepted -->
        <div class="tab-pane fade" id="second-stage" role="tabpanel">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-upload me-2"></i>
                        المقبولين في المرحلة الثانية (رفعوا المستندات)
                    </h6>
                </div>
                <div class="card-body">
                    @if($secondStageAccepted->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المتقدم</th>
                                        <th>تاريخ رفع المستندات</th>
                                        <th>المستندات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($secondStageAccepted as $application)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $application->user->avatar_url }}"
                                                         class="rounded-circle me-3"
                                                         width="40" height="40">
                                                    <div>
                                                        <div class="fw-bold">{{ $application->full_name }}</div>
                                                        <div class="text-muted small">{{ $application->user->email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $application->updated_at->format('Y-m-d H:i') }}</td>
                                            <td>
                                                @if($application->documents)
                                                    <div class="d-flex flex-wrap gap-1">
                                                        @if(isset($application->documents['education_certificate']))
                                                            <span class="badge bg-success">شهادة التعليم</span>
                                                        @endif
                                                        @if(isset($application->documents['id_card']))
                                                            <span class="badge bg-primary">البطاقة</span>
                                                        @endif
                                                        @if(isset($application->documents['experience_proof']))
                                                            <span class="badge bg-info">إثبات خبرة</span>
                                                        @endif
                                                        @if(isset($application->documents['additional_documents']))
                                                            <span class="badge bg-secondary">مستندات إضافية</span>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">لا توجد مستندات</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.broker-applications.show', $application) }}"
                                                       class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-success btn-sm"
                                                            onclick="acceptFinal({{ $application->id }})">
                                                        <i class="fas fa-check-double"></i>
                                                    </button>
                                                    <button type="button"
                                                            class="btn btn-danger btn-sm"
                                                            onclick="rejectApplication({{ $application->id }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد طلبات في المرحلة الثانية</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- المقبولين نهائياً -->
        <div class="tab-pane fade" id="accepted-brokers" role="tabpanel">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-user-check me-2"></i>
                        المندوبين المقبولين نهائياً
                    </h6>
                </div>
                <div class="card-body">
                    @if($acceptedBrokers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المندوب</th>
                                        <th>تاريخ القبول</th>
                                        <th>النقاط الحالية</th>
                                        <th>حالة المندوب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($acceptedBrokers as $application)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $application->user->avatar_url }}"
                                                         class="rounded-circle me-3"
                                                         width="40" height="40">
                                                    <div>
                                                        <div class="fw-bold">{{ $application->full_name }}</div>
                                                        <div class="text-muted small">{{ $application->user->email }}</div>
                                                        <span class="badge bg-success">مندوب نشط</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-success">
                                                    <i class="fas fa-calendar-check me-1"></i>
                                                    {{ $application->updated_at->format('Y-m-d') }}
                                                    <br>
                                                    <small class="text-muted">{{ $application->updated_at->format('H:i') }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary fs-6">
                                                    {{ number_format($application->user->points ?? 0) }} نقطة
                                                </span>
                                            </td>
                                            <td>
                                                @if($application->user->is_broker)
                                                    <span class="badge bg-success">مندوب نشط</span>
                                                @else
                                                    <span class="badge bg-warning">تم إلغاء المندوبية</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.broker-applications.show', $application) }}"
                                                       class="btn btn-info btn-sm"
                                                       title="عرض التفاصيل والمستندات">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.users.edit', $application->user->id) }}"
                                                       class="btn btn-primary btn-sm"
                                                       title="تعديل بيانات المستخدم">
                                                        <i class="fas fa-user-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد مندوبين مقبولين حتى الآن</h5>
                            <p class="text-muted">سيظهر هنا المندوبين الذين تم قبولهم نهائياً</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Rejected Applications -->
        <div class="tab-pane fade" id="rejected" role="tabpanel">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        الطلبات المرفوضة
                    </h6>
                </div>
                <div class="card-body">
                    @if($rejectedApplications->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>المتقدم</th>
                                        <th>تاريخ الرفض</th>
                                        <th>سبب الرفض</th>
                                        <th>إعادة التقديم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($rejectedApplications as $application)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ $application->user->avatar_url }}"
                                                         class="rounded-circle me-3"
                                                         width="40" height="40">
                                                    <div>
                                                        <div class="fw-bold">{{ $application->full_name }}</div>
                                                        <div class="text-muted small">{{ $application->user->email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $application->rejected_at->format('Y-m-d H:i') }}</td>
                                            <td>{{ \Illuminate\Support\Str::limit($application->rejection_reason, 50) }}</td>
                                            <td>
                                                @if($application->canReapply())
                                                    <span class="badge bg-success">يمكن التقديم</span>
                                                @else
                                                    <span class="badge bg-warning">بعد {{ $application->daysUntilReapply() }} يوم</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.broker-applications.show', $application) }}"
                                                       class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-danger btn-sm"
                                                            onclick="deleteRejectedApplication({{ $application->id }})"
                                                            title="حذف الطلب">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد طلبات مرفوضة</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Reject Application Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض *</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function acceptFirstStage(applicationId) {
    if (confirm('هل أنت متأكد من قبول هذا الطلب في المرحلة الأولى؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/broker-applications/${applicationId}/accept-first-stage`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function acceptFinal(applicationId) {
    if (confirm('هل أنت متأكد من قبول هذا الطلب نهائياً وتحويل المستخدم إلى مندوب؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/broker-applications/${applicationId}/accept-final`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectApplication(applicationId) {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    const form = document.getElementById('rejectForm');
    form.action = `/admin/broker-applications/${applicationId}/reject`;
    modal.show();
}

function deleteRejectedApplication(applicationId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب المرفوض؟ سيتمكن المستخدم من التقديم مرة أخرى فوراً.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/broker-applications/${applicationId}/delete`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteOldRejected() {
    if (confirm('هل أنت متأكد من حذف جميع الطلبات المرفوضة القديمة (أكثر من أسبوعين)؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/broker-applications/delete-old-rejected';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';

        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

@endsection
