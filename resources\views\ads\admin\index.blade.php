@extends('partials.admin')
@section('title', 'Admin Ads')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'ads.all'])

<div class="main-content app-content mt-0">
    <div class="side-app">

        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Ads Management', 'hasBack' => true, 'backTitle' => 'Dashboard', 'backUrl' => route('admin.dashboard')])

            <!-- Ads Management Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title mb-0">إدارة الإعلانات</h3>
                        </div>
                        <div class="card-body">
                            <!-- Navigation Tabs -->
                            <ul class="nav nav-tabs" id="adsManagementTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="regular-with-broker-tab" data-bs-toggle="tab"
                                            data-bs-target="#regular-with-broker" type="button" role="tab">
                                        <i class="fas fa-handshake"></i>
                                        يحتاج مندوب
                                        <span class="badge bg-success ms-1">{{ ($regularWithBrokerPendingCount ?? 0) + ($regularWithBrokerAllCount ?? 0) }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="regular-without-broker-tab" data-bs-toggle="tab"
                                            data-bs-target="#regular-without-broker" type="button" role="tab">
                                        <i class="fas fa-user"></i>
                                        لا يحتاج مندوب
                                        <span class="badge bg-info ms-1">{{ ($regularWithoutBrokerPendingCount ?? 0) + ($regularWithoutBrokerAllCount ?? 0) }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="vip-users-tab" data-bs-toggle="tab"
                                            data-bs-target="#vip-users" type="button" role="tab">
                                        <i class="fas fa-crown"></i>
                                        المستخدمون VIP (Rank > 0)
                                        <span class="badge bg-warning ms-1">{{ ($vipPendingCount ?? 0) + ($vipAllCount ?? 0) }}</span>
                                    </button>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content mt-3" id="adsManagementTabsContent">
                                <!-- Regular Users With Broker Tab -->
                                <div class="tab-pane fade show active" id="regular-with-broker" role="tabpanel">
                                    <div class="row">
                                        <div class="col-12">
                                            <!-- Sub-tabs for Regular Users With Broker -->
                                            <ul class="nav nav-pills mb-3" id="regularWithBrokerTabs" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="regular-with-broker-pending-tab" data-bs-toggle="pill"
                                                            data-bs-target="#regular-with-broker-pending" type="button" role="tab">
                                                        <i class="fas fa-clock"></i>
                                                        الإعلانات المعلقة
                                                        <span class="badge bg-warning ms-1">{{ $regularWithBrokerPendingCount ?? 0 }}</span>
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="regular-with-broker-all-tab" data-bs-toggle="pill"
                                                            data-bs-target="#regular-with-broker-all" type="button" role="tab">
                                                        <i class="fas fa-list"></i>
                                                        جميع الإعلانات
                                                        <span class="badge bg-info ms-1">{{ $regularWithBrokerAllCount ?? 0 }}</span>
                                                    </button>
                                                </li>
                                            </ul>

                                            <!-- Regular Users With Broker Content -->
                                            <div class="tab-content" id="regularWithBrokerTabsContent">
                                                <div class="tab-pane fade show active" id="regular-with-broker-pending" role="tabpanel">
                                                    <x-admin-ad-table :collection="$regularWithBrokerPendingAds ?? collect()" />
                                                </div>
                                                <div class="tab-pane fade" id="regular-with-broker-all" role="tabpanel">
                                                    <x-admin-ad-table :collection="$regularWithBrokerAllAds ?? collect()" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Regular Users Without Broker Tab -->
                                <div class="tab-pane fade" id="regular-without-broker" role="tabpanel">
                                    <div class="row">
                                        <div class="col-12">
                                            <!-- Sub-tabs for Regular Users Without Broker -->
                                            <ul class="nav nav-pills mb-3" id="regularWithoutBrokerTabs" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="regular-without-broker-pending-tab" data-bs-toggle="pill"
                                                            data-bs-target="#regular-without-broker-pending" type="button" role="tab">
                                                        <i class="fas fa-clock"></i>
                                                        الإعلانات المعلقة
                                                        <span class="badge bg-warning ms-1">{{ $regularWithoutBrokerPendingCount ?? 0 }}</span>
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="regular-without-broker-all-tab" data-bs-toggle="pill"
                                                            data-bs-target="#regular-without-broker-all" type="button" role="tab">
                                                        <i class="fas fa-list"></i>
                                                        جميع الإعلانات
                                                        <span class="badge bg-info ms-1">{{ $regularWithoutBrokerAllCount ?? 0 }}</span>
                                                    </button>
                                                </li>
                                            </ul>

                                            <!-- Regular Users Without Broker Content -->
                                            <div class="tab-content" id="regularWithoutBrokerTabsContent">
                                                <div class="tab-pane fade show active" id="regular-without-broker-pending" role="tabpanel">
                                                    <x-admin-ad-table :collection="$regularWithoutBrokerPendingAds ?? collect()" />
                                                </div>
                                                <div class="tab-pane fade" id="regular-without-broker-all" role="tabpanel">
                                                    <x-admin-ad-table :collection="$regularWithoutBrokerAllAds ?? collect()" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- VIP Users Tab -->
                                <div class="tab-pane fade" id="vip-users" role="tabpanel">
                                    <div class="row">
                                        <div class="col-12">
                                            <!-- Sub-tabs for VIP Users -->
                                            <ul class="nav nav-pills mb-3" id="vipUsersTabs" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="vip-pending-tab" data-bs-toggle="pill"
                                                            data-bs-target="#vip-pending" type="button" role="tab">
                                                        <i class="fas fa-clock"></i>
                                                        الإعلانات المعلقة
                                                        <span class="badge bg-warning ms-1">{{ $vipPendingCount ?? 0 }}</span>
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="vip-all-tab" data-bs-toggle="pill"
                                                            data-bs-target="#vip-all" type="button" role="tab">
                                                        <i class="fas fa-list"></i>
                                                        جميع الإعلانات
                                                        <span class="badge bg-info ms-1">{{ $vipAllCount ?? 0 }}</span>
                                                    </button>
                                                </li>
                                            </ul>

                                            <!-- VIP Users Content -->
                                            <div class="tab-content" id="vipUsersTabsContent">
                                                <div class="tab-pane fade show active" id="vip-pending" role="tabpanel">
                                                    <x-admin-ad-table :collection="$vipPendingAds ?? collect()" />
                                                </div>
                                                <div class="tab-pane fade" id="vip-all" role="tabpanel">
                                                    <x-admin-ad-table :collection="$vipAllAds ?? collect()" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- CONTAINER END -->
    </div>
</div>

<style>
.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-pills .nav-link {
    border-radius: 0.375rem;
    color: #6c757d;
    font-weight: 500;
    margin-right: 0.5rem;
}

.nav-pills .nav-link:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.nav-pills .nav-link.active {
    color: #fff;
    background-color: #007bff;
}

.badge {
    font-size: 0.75em;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1.5rem;
    background-color: #fff;
    border-radius: 0 0 0.375rem 0.375rem;
}

.nav-pills + .tab-content {
    border: none;
    padding: 1rem 0;
}

.card-header h3 {
    color: #495057;
    font-weight: 600;
}

.nav-link i {
    margin-right: 0.5rem;
}
</style>

@endsection