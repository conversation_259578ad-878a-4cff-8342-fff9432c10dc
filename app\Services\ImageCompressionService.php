<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Log;

class ImageCompressionService
{
    /**
     * Compress and store image with optimized settings
     */
    public function compressAndStore(UploadedFile $file, string $directory, array $options = []): array
    {
        // Default options
        $defaultOptions = [
            'quality' => 75,           // WebP quality (75% gives good balance)
            'max_width' => 1200,       // Maximum width
            'max_height' => 1200,      // Maximum height
            'format' => 'webp',        // Output format (webp, jpg, png)
            'progressive' => true,     // Progressive JPEG
            'strip_metadata' => true,  // Remove EXIF data to save space
        ];

        $options = array_merge($defaultOptions, $options);

        // Generate unique filename
        $uuid = Str::uuid();
        $extension = $options['format'] === 'webp' ? 'webp' : 
                    ($options['format'] === 'jpg' ? 'jpg' : 'png');
        $filename = $uuid . '.' . $extension;
        $path = $directory . '/' . $filename;

        // Get original file size
        $originalSize = $file->getSize();

        try {
            // Create image instance
            $image = Image::make($file);

            // Strip metadata if requested
            if ($options['strip_metadata']) {
                $image->exif(null);
            }

            // Resize if image is larger than max dimensions
            if ($image->width() > $options['max_width'] || $image->height() > $options['max_height']) {
                $image->resize($options['max_width'], $options['max_height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize(); // Prevent upsizing
                });
            }

            // Apply compression based on format
            switch ($options['format']) {
                case 'webp':
                    $compressedImage = $image->encode('webp', $options['quality']);
                    break;
                case 'jpg':
                    $compressedImage = $image->encode('jpg', $options['quality']);
                    if ($options['progressive']) {
                        $compressedImage->interlace();
                    }
                    break;
                case 'png':
                    // PNG compression (0-9, where 9 is maximum compression)
                    $pngCompression = (int) ((100 - $options['quality']) / 10);
                    $compressedImage = $image->encode('png', $pngCompression);
                    break;
                default:
                    $compressedImage = $image->encode('webp', $options['quality']);
            }

            // Store the compressed image
            Storage::disk('public')->put($path, (string) $compressedImage);

            // Get compressed file size
            $compressedSize = Storage::disk('public')->size($path);
            $compressionRatio = round((($originalSize - $compressedSize) / $originalSize) * 100, 2);

            // Log compression results
            Log::info('Image compressed successfully', [
                'original_filename' => $file->getClientOriginalName(),
                'new_filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio . '%',
                'format' => $options['format'],
                'quality' => $options['quality'],
                'dimensions' => $image->width() . 'x' . $image->height()
            ]);

            return [
                'success' => true,
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'format' => $options['format'],
                'dimensions' => [
                    'width' => $image->width(),
                    'height' => $image->height()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Image compression failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get optimized settings for different image types
     */
    public function getOptimizedSettings(string $type): array
    {
        return match ($type) {
            'ad_images' => [
                'quality' => 80,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'webp'
            ],
            'profile_avatar' => [
                'quality' => 85,
                'max_width' => 400,
                'max_height' => 400,
                'format' => 'webp'
            ],
            'identity_documents' => [
                'quality' => 90,  // Higher quality for documents
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg'  // JPG better for documents
            ],
            'broker_documents' => [
                'quality' => 90,  // Higher quality for documents
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg'  // JPG better for documents
            ],
            default => [
                'quality' => 75,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'webp'
            ]
        };
    }

    /**
     * Delete image file from storage
     */
    public function deleteImage(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                Log::info('Image deleted successfully', ['path' => $path]);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to delete image', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, int $maxSizeMB = 10): array
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
            ];
        }

        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'error' => "File size exceeds {$maxSizeMB}MB limit."
            ];
        }

        return ['valid' => true];
    }
}
