@props(['ad', 'type' => 'classic'])

@php
    $userRank = $ad->rank ?? ($ad->user->rank ?? 0);
    $isTrusted = $ad->is_trusted || ($ad->user && $ad->user->is_trusted);
    $isSponsored = $ad->sponsoredAd && $ad->sponsoredAd->isActive();
@endphp

@if($type == 'classic')
<div class="col-lg-4 col-md-6 col-sm-10">
    <div data-wow-duration="1.5s" data-wow-delay="0.2s" class="eg-card auction-card2 wow fadeInDown sponsored-card">
        <!-- Sponsored Badge -->
        <div class="sponsored-badge">
            <i class="fas fa-star"></i>
            <span>إعلان ممول</span>
        </div>
        
        <div class="auction-img">
            <img alt="image" src="{{ optional($ad->media->first())->url }}">
            <div class="auction-timer">
                <div class="countdown">
                    <h5 class="countdown-classic">
                        {{ $ad->expired_at }}
                    </h5>
                </div>
            </div>
        </div>
        <div class="auction-content">
            <h4><a href="{{ route('auction-details', $ad->slug) }}" onclick="trackSponsoredClick({{ $ad->id }})">{{ shorten_chars($ad->title)}}</a></h4>
            <div class="author-price-area">
                <div class="author">
                    <img alt="image" src="{{ $ad->user?->avatar ?? get_random_avatar() }}">
                    <span class="name">By {{ $ad->user->name }}</span>
                    @if($isTrusted)
                        <i class="fas fa-check-circle text-success ms-1" title="موثق"></i>
                    @endif
                </div>
                <p>{{ money($ad->price) }}</p>
            </div>
            <div class="auction-card-bttm">
                <a href="{{ route('auction-details', $ad->slug) }}" class="eg-btn btn--primary2 btn--sm" onclick="trackSponsoredClick({{ $ad->id }})">Place a Bid</a>
                <div class="share-area">
                    <a href="#" class="share-btn"><i class="bx bxs-share-alt"></i></a>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

@if($type == 'horizontal')
<div class="sponsored-ad-horizontal">
    <div class="sponsored-badge-horizontal">
        <i class="fas fa-star"></i>
        <span>إعلان ممول</span>
    </div>
    
    <div class="row align-items-center">
        <div class="col-md-4">
            <div class="sponsored-image">
                <img src="{{ optional($ad->media->first())->url }}" alt="{{ $ad->title }}" class="img-fluid rounded">
            </div>
        </div>
        <div class="col-md-8">
            <div class="sponsored-content">
                <h5><a href="{{ route('auction-details', $ad->slug) }}" onclick="trackSponsoredClick({{ $ad->id }})">{{ $ad->title }}</a></h5>
                <p class="text-muted mb-2">{{  \Illuminate\Support\Str::limit($ad->description, 100) }}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="price-info">
                        <span class="price">{{ money($ad->price) }}</span>
                        <small class="text-muted">{{ $ad->category->name ?? '' }}</small>
                    </div>
                    <div class="author-info">
                        <img src="{{ $ad->user?->avatar ?? get_random_avatar() }}" alt="{{ $ad->user->name }}" class="rounded-circle me-2" width="30" height="30">
                        <span class="author-name">{{ $ad->user->name }}</span>
                        @if($isTrusted)
                            <i class="fas fa-check-circle text-success ms-1" title="موثق"></i>
                        @endif
                    </div>
                </div>
                <div class="mt-2">
                    <a href="{{ route('auction-details', $ad->slug) }}" class="btn btn-primary btn-sm" onclick="trackSponsoredClick({{ $ad->id }})">
                        <i class="fas fa-eye me-1"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

@if($type == 'sidebar')
<div class="sponsored-ad-sidebar mb-3">
    <div class="sponsored-badge-small">
        <i class="fas fa-star"></i>
        <span>ممول</span>
    </div>
    
    <div class="card">
        <div class="card-body p-3">
            <div class="row">
                <div class="col-4">
                    <img src="{{ optional($ad->media->first())->url }}" alt="{{ $ad->title }}" class="img-fluid rounded">
                </div>
                <div class="col-8">
                    <h6 class="card-title mb-1">
                        <a href="{{ route('auction-details', $ad->slug) }}" onclick="trackSponsoredClick({{ $ad->id }})" class="text-decoration-none">
                            {{ \Illuminate\Support\Str::limit($ad->title, 40) }}
                        </a>
                    </h6>
                    <p class="card-text small text-muted mb-1">{{ money($ad->price) }}</p>
                    <div class="d-flex align-items-center">
                        <img src="{{ $ad->user?->avatar ?? get_random_avatar() }}" alt="{{ $ad->user->name }}" class="rounded-circle me-1" width="20" height="20">
                        <small class="text-muted">{{ $ad->user->name }}</small>
                        @if($isTrusted)
                            <i class="fas fa-check-circle text-success ms-1" style="font-size: 10px;" title="موثق"></i>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<style>
.sponsored-card {
    position: relative;
    border: 2px solid #ffd700 !important;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3) !important;
}

.sponsored-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sponsored-badge i {
    margin-right: 3px;
}

.sponsored-ad-horizontal {
    background: #fff;
    border: 2px solid #ffd700;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    position: relative;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.sponsored-badge-horizontal {
    position: absolute;
    top: -10px;
    left: 20px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sponsored-ad-sidebar {
    position: relative;
}

.sponsored-badge-small {
    position: absolute;
    top: -5px;
    right: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.sponsored-ad-sidebar .card {
    border: 1px solid #ffd700;
}
</style>

<script>
function trackSponsoredClick(adId) {
    // إرسال طلب AJAX لتسجيل النقرة
    fetch(`/api/sponsored-ads/${adId}/click`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    }).catch(error => console.log('Click tracking failed:', error));
}

// تسجيل المشاهدة عند ظهور الإعلان
document.addEventListener('DOMContentLoaded', function() {
    const sponsoredAds = document.querySelectorAll('[data-sponsored-id]');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const adId = entry.target.getAttribute('data-sponsored-id');
                fetch(`/api/sponsored-ads/${adId}/view`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                }).catch(error => console.log('View tracking failed:', error));
                
                observer.unobserve(entry.target);
            }
        });
    });
    
    sponsoredAds.forEach(ad => observer.observe(ad));
});
</script>
