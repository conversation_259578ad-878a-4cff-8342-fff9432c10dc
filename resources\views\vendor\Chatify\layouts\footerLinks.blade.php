{{-- ملفات JS للشات فقط - تم إزالة JS الموقع الأساسي --}}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

{{-- Chatify scripts --}}
<script src="{{ asset('js/chatify/code.js') }}"></script>
<script src="{{ asset('js/chatify/utils.js') }}"></script>

{{-- Pusher --}}
<script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>

{{-- Emoji Picker --}}
<script src="https://cdn.jsdelivr.net/npm/emoji-button@latest/dist/index.min.js"></script>

{{-- Chatify Config --}}
<script>
    window.chatify = {
        name: "{{ config('chatify.name') }}",
        sounds: {
            new_message: "{{ config('chatify.sounds.new_message') }}",
            notification: "{{ config('chatify.sounds.notification') }}",
            enabled: "{{ config('chatify.sounds.enabled') }}",
            public_path: "{{ config('chatify.sounds.public_path') }}"
        },
        pusher: {
            key: "{{ config('chatify.pusher.key') }}",
            options: {
                cluster: "{{ config('chatify.pusher.options.cluster') }}",
                encrypted: "{{ config('chatify.pusher.options.encrypted') }}",
                host: "{{ config('chatify.pusher.options.host') }}",
                port: "{{ config('chatify.pusher.options.port') }}",
                useTLS: "{{ config('chatify.pusher.options.useTLS') }}"
            }
        },
        allAllowedExtensions: @json(config('chatify.attachments.allowed_files')),
        maxUploadSize: "{{ config('chatify.attachments.max_upload_size') }}"
    };
    
    window.chatifyAuthEndpoint = "{{ route('pusher.auth') }}";

    // Function to update chat header badges (called from code.js)
    function updateHeaderBadges(data) {
        const badgesContainer = document.getElementById('chat-header-badges');
        if (!badgesContainer || !data) return;

        let badges = '';

        // VIP Badge (only show if user rank > 0)
        if (data.rank && data.rank > 0) {
            badges += '<span class="chat-header-badge vip-badge"><i class="fas fa-crown"></i> VIP</span>';
        }

        // Rank Badge
        if (data.rank == 1) {
            badges += '<span class="chat-header-badge vip-badge"><i class="fas fa-crown"></i> VIP</span>';
        } else if (data.rank == 2) {
            badges += '<span class="chat-header-badge trader-badge"><i class="fas fa-briefcase"></i> Trader</span>';
        } else if (data.rank == 3) {
            badges += '<span class="chat-header-badge company-badge"><i class="fas fa-building"></i> Company</span>';
        } else if (data.rank == 4) {
            badges += '<span class="chat-header-badge admin-badge"><i class="fas fa-user-shield"></i> Admin</span>';
        }

        badgesContainer.innerHTML = badges;
    }
</script>