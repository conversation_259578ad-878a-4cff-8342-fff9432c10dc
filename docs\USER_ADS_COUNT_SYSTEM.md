# نظام عد الإعلانات للمستخدمين (User Ads Count System)

## نظرة عامة

تم تطوير نظام تلقائي لتتبع عدد الإعلانات المنشورة لكل مستخدم في عمود `Number_Ads` في جدول `users`. هذا النظام يضمن دقة العد عند تغيير حالة الإعلانات من قبل الأدمن.

## كيف يعمل النظام

### 1. تحديث تلقائي عند تغيير حالة الإعلان

#### زيادة العدد (+1):
- عندما يوافق الأدمن على إعلان (تغيير الحالة من `PENDING` إلى `PUBLISHED`)
- عندما يتم استعادة إعلان منشور من الحذف

#### تقليل العدد (-1):
- عندما يرفض الأدمن إعلان منشور (تغيير من `PUBLISHED` إلى `REJECTED`)
- عندما يتم أرشفة إعلان منشور (تغيير من `PUBLISHED` إلى `ARCHIVED`)
- عندما يتم حذف إعلان منشور

### 2. الملفات المتأثرة

#### Observer Pattern:
- `app/Observers/AdObserver.php` - يراقب تغييرات الإعلانات تلقائياً

#### Repository Pattern:
- `app/Repositories/Ad/Admin/AdminAdRepository.php` - يحدث العدد عند تحديث الإعلانات من الأدمن

#### User Model:
- `app/Models/User.php` - يحتوي على methods لحساب وإعادة حساب عدد الإعلانات

### 3. Commands المتاحة

#### إعادة حساب عدد الإعلانات لجميع المستخدمين:
```bash
php artisan users:recalculate-ads-count
```

#### إعادة حساب عدد الإعلانات لمستخدم محدد:
```bash
php artisan users:recalculate-ads-count --user-id=123
```

## الاستخدام في الكود

### 1. الحصول على عدد الإعلانات المنشورة:

```php
// من قاعدة البيانات (محفوظ)
$user->Number_Ads

// حساب مباشر من الإعلانات
$user->published_ads_count

// إعادة حساب وتحديث
$user->recalculateAdsCount();
```

### 2. في الـ Views:

```blade
<!-- عرض عدد الإعلانات -->
<span>{{ $user->Number_Ads }} إعلان منشور</span>

<!-- أو -->
<span>{{ $user->published_ads_count }} إعلان منشور</span>
```

## حالات الاستخدام

### 1. Dashboard المستخدم:
- عرض عدد الإعلانات الكلي للمستخدم
- إحصائيات سريعة

### 2. Profile المستخدم:
- عرض معلومات المستخدم العامة
- تقييم نشاط المستخدم

### 3. Admin Panel:
- مراقبة نشاط المستخدمين
- إحصائيات النظام

## الأمان والدقة

### 1. Observer Pattern:
- يضمن التحديث التلقائي عند أي تغيير في حالة الإعلان
- يعمل حتى لو تم التحديث من خارج Admin Panel

### 2. Repository Pattern:
- يضمن التحديث الصحيح عند تحديث الإعلانات من Admin Panel
- يتعامل مع جميع حالات التغيير

### 3. Command للإصلاح:
- يمكن تشغيله دورياً للتأكد من دقة البيانات
- يصلح أي اختلافات في العد

## Testing

تم إنشاء tests شاملة في:
- `tests/Feature/UserAdsCountTest.php`

لتشغيل الـ tests:
```bash
php artisan test --filter UserAdsCountTest
```

## Database Indexes

تم إضافة indexes لتحسين الأداء:
- `ads.status`
- `ads.user_id, ads.status`

## Migration

تم إنشاء migration لإضافة الـ indexes:
- `database/migrations/2025_07_19_010535_add_index_to_ads_status_column.php`

لتشغيل الـ migration:
```bash
php artisan migrate
```

## الصيانة

### 1. مراقبة دورية:
```bash
# تشغيل إعادة الحساب أسبوعياً
php artisan users:recalculate-ads-count
```

### 2. في حالة وجود مشاكل:
```bash
# فحص مستخدم محدد
php artisan users:recalculate-ads-count --user-id=123

# إعادة حساب الكل
php artisan users:recalculate-ads-count
```

## ملاحظات مهمة

1. **العدد يشمل فقط الإعلانات المنشورة** (`PUBLISHED`)
2. **التحديث تلقائي** عند تغيير حالة الإعلان
3. **يمكن إعادة الحساب** في أي وقت باستخدام الـ command
4. **محمي من الأخطاء** - لا يقل العدد تحت الصفر
5. **متوافق مع Observer Pattern** - يعمل مع جميع طرق التحديث
