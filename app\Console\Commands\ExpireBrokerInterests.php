<?php

namespace App\Console\Commands;

use App\Models\BrokerInterest;
use Illuminate\Console\Command;

class ExpireBrokerInterests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'broker:expire-interests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنهاء اهتمامات المندوبين المنتهية الصلاحية (بعد 10 أيام)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('بدء فحص اهتمامات المندوبين المنتهية الصلاحية...');

        // جلب الاهتمامات المنتهية الصلاحية (أكثر من 10 أيام)
        $expiredInterests = BrokerInterest::expired()->get();

        $this->info("تم العثور على {$expiredInterests->count()} اهتمام منتهي الصلاحية.");

        $expiredCount = 0;

        foreach ($expiredInterests as $interest) {
            $daysSinceInterest = $interest->created_at->diffInDays(now());

            $this->info("إنهاء اهتمام المندوب {$interest->broker->name} بالإعلان: {$interest->ad->title} (مضى عليه {$daysSinceInterest} يوم)");

            // حساب النقاط المسترجعة (خصم 20% لأن الانتهاء بعد 10 أيام)
            $refundPoints = (int) floor($interest->points_paid * 0.8);

            // تحديث الحالة إلى منتهي الصلاحية
            $interest->update([
                'status' => 'expired',
                'points_earned' => $refundPoints,
            ]);

            // إضافة النقاط المسترجعة للمندوب
            $interest->broker->increment('points', $refundPoints);

            // حذف الاهتمام من الجدول القديم
            \App\Models\AdBrokerRequest::where('user_id', $interest->broker_id)
                                      ->where('ad_id', $interest->ad_id)
                                      ->delete();

            $this->info("تم استرداد {$refundPoints} نقطة للمندوب (خصم 20%)");
            $expiredCount++;
        }

        $this->info("✅ تم إنهاء {$expiredCount} اهتمام منتهي الصلاحية.");

        return Command::SUCCESS;
    }
}
