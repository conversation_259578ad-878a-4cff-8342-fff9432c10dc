<?php

namespace App\Http\Controllers;

use App\Models\UserBlock;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Chatify\Models\ChMessage;
use Illuminate\Support\Facades\Log;

class ChatController extends Controller
{
    /**
     * Block a user (mutual blocking).
     */
    public function blockUser(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        try {
            $blockerId = Auth::id();
            $blockedId = $request->user_id;

            // Check if already blocked
            if (UserBlock::isBlocked($blockerId, $blockedId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم محظور بالفعل'
                ]);
            }

            // Get user names for messages
            $blocker = Auth::user();
            $blocked = \App\Models\User::find($blockedId);

            // Create single-sided block (only blocker blocks blocked)
            UserBlock::create([
                'blocker_id' => $blockerId,
                'blocked_id' => $blockedId
            ]);

            Log::info("User blocked another user", [
                'blocker_id' => $blockerId,
                'blocker_name' => $blocker->name,
                'blocked_id' => $blockedId,
                'blocked_name' => $blocked->name ?? 'Unknown'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم حظر المستخدم بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حظر المستخدم'
            ], 500);
        }
    }

    /**
     * Unblock a user.
     */
    public function unblockUser(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        try {
            $blockerId = Auth::id();
            $blockedId = $request->user_id;

            UserBlock::where('blocker_id', $blockerId)
                     ->where('blocked_id', $blockedId)
                     ->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء حظر المستخدم بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء حظر المستخدم'
            ], 500);
        }
    }

    /**
     * Report a message.
     */
    public function reportMessage(Request $request): JsonResponse
    {
        Log::info('Report message attempt', [
            'user_id' => Auth::id(),
            'request_data' => $request->all()
        ]);

        try {
            $request->validate([
                'message_id' => 'required|string',
                'reason' => 'required|string',
                'description' => 'nullable|string|max:1000'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Report message validation failed', [
                'user_id' => Auth::id(),
                'errors' => $e->validator->errors()->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        }

        try {
            // Check if user already reported this message
            $existingReport = Report::where([
                'reporter_id' => Auth::id(),
                'reportable_type' => Report::TYPE_MESSAGE,
                'reportable_id' => $request->message_id
            ])->first();

            if ($existingReport) {
                Log::info('Duplicate report attempt', [
                    'user_id' => Auth::id(),
                    'message_id' => $request->message_id,
                    'existing_report_id' => $existingReport->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'لقد قمت بالإبلاغ عن هذه الرسالة من قبل'
                ]);
            }

            // Get message details for metadata
            $message = ChMessage::find($request->message_id);

            if (!$message) {
                Log::warning('Message not found for report', [
                    'user_id' => Auth::id(),
                    'message_id' => $request->message_id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'الرسالة غير موجودة'
                ], 404);
            }

            $metadata = [
                'message_content' => substr($message->body ?? '', 0, 200),
                'sender_id' => $message->from_id,
                'sender_name' => $message->fromUser->name ?? 'Unknown',
                'receiver_id' => $message->to_id,
                'receiver_name' => $message->toUser->name ?? 'Unknown',
                'conversation_id' => $message->conversation_id ?? null
            ];

            // Create the report
            $report = Report::create([
                'reporter_id' => Auth::id(),
                'reportable_type' => Report::TYPE_MESSAGE,
                'reportable_id' => $request->message_id,
                'reason' => $request->reason,
                'description' => $request->description,
                'metadata' => $metadata
            ]);

            Log::info('Report message created successfully', [
                'user_id' => Auth::id(),
                'report_id' => $report->id,
                'message_id' => $request->message_id,
                'reason' => $request->reason
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإبلاغ بنجاح. سيتم مراجعته من قبل فريق الإدارة.'
            ]);

        } catch (\Exception $e) {
            Log::error('Report message failed', [
                'user_id' => Auth::id(),
                'message_id' => $request->message_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإبلاغ: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check block status between users.
     */
    public function checkBlock(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);

        $currentUserId = Auth::id();
        $otherUserId = $request->user_id;

        // Check if current user blocked the other user
        $iBlockedThem = UserBlock::isBlocked($currentUserId, $otherUserId);

        // Check if the other user blocked current user
        $theyBlockedMe = UserBlock::isBlocked($otherUserId, $currentUserId);

        // Check if there's any block between them
        $hasAnyBlock = $iBlockedThem || $theyBlockedMe;

        return response()->json([
            'success' => true,
            'i_blocked_them' => $iBlockedThem,
            'they_blocked_me' => $theyBlockedMe,
            'has_any_block' => $hasAnyBlock,
            'can_send_message' => !$theyBlockedMe, // يمكن إرسال رسالة إذا لم يحظرني الطرف الآخر
            'show_unblock_button' => $iBlockedThem // إظهار زر فك الحظر إذا كنت أحظره
        ]);
    }

    /**
     * Send a system message to a user.
     */
    private function sendSystemMessage(string $fromId, string $toId, string $message): void
    {
        ChMessage::create([
            'from_id' => $fromId,
            'to_id' => $toId,
            'body' => $message,
            'seen' => false
        ]);
    }
}
