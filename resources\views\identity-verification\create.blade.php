@extends('partials.app')

@section('title', 'توثيق الهوية')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>
                        توثيق الهوية
                    </h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                        </div>
                    @endif

                    @if($existingVerification)
                        @if($existingVerification->status === 'pending')
                            <div class="alert alert-info">
                                <i class="fas fa-clock me-2"></i>
                                <strong>طلبك قيد المراجعة</strong><br>
                                تم إرسال طلب توثيق الهوية الخاص بك في {{ $existingVerification->created_at->format('Y-m-d H:i') }}
                                وهو الآن قيد المراجعة من قبل الإدارة.
                            </div>
                        @elseif($existingVerification->status === 'approved')
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>تم توثيق هويتك بنجاح!</strong><br>
                                تم قبول طلب توثيق الهوية الخاص بك في {{ $existingVerification->approved_at->format('Y-m-d H:i') }}.
                                يمكنك الآن الاستفادة من جميع المزايا المتاحة للمستخدمين الموثقين.
                            </div>
                        @elseif($existingVerification->status === 'rejected')
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>تم رفض طلب توثيق الهوية</strong><br>
                                <strong>السبب:</strong> {{ $existingVerification->rejection_reason }}<br>
                                @if($existingVerification->canReapply())
                                    <small class="text-muted">يمكنك التقديم مرة أخرى الآن</small>
                                @else
                                    <small class="text-muted">
                                        يمكنك التقديم مرة أخرى بعد {{ 5 - now()->diffInDays($existingVerification->rejected_at) }} أيام
                                    </small>
                                @endif
                            </div>
                        @endif
                    @endif

                    @if(!$existingVerification || ($existingVerification->status === 'rejected' && $existingVerification->canReapply()))
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
                            <ul class="mb-0">
                                <li>توثيق الهوية مطلوب للاستفادة من خدمات البرايم والمندوبين</li>
                                <li>يجب رفع صورة واضحة للبطاقة الشخصية من الأمام مع سيلفي</li>
                                <li>يجب رفع صورة واضحة للبطاقة الشخصية من الخلف</li>
                                <li>يجب أن تكون الصور واضحة وغير مشوشة</li>
                                <li>سيتم مراجعة طلبك خلال 24-48 ساعة</li>
                            </ul>
                        </div>

                        <form method="POST" action="{{ route('identity-verification.store') }}" enctype="multipart/form-data">
                            @csrf
                            
                            <div class="mb-3">
                                <label class="form-label">الرقم القومي <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                                       name="national_id" value="{{ old('national_id') }}" 
                                       placeholder="أدخل الرقم القومي (14 رقم)" maxlength="14">
                                @error('national_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">صورة البطاقة الأمامية مع السيلفي <span class="text-danger">*</span></label>
                                <input type="file" class="form-control @error('front_image') is-invalid @enderror" 
                                       name="front_image" accept="image/*">
                                <small class="form-text text-muted">
                                    صورة سيلفي وأنت تحمل البطاقة الشخصية بجانب وجهك (الوجه الأمامي للبطاقة)
                                </small>
                                @error('front_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">صورة البطاقة الخلفية <span class="text-danger">*</span></label>
                                <input type="file" class="form-control @error('back_image') is-invalid @enderror" 
                                       name="back_image" accept="image/*">
                                <small class="form-text text-muted">
                                    صورة واضحة للوجه الخلفي للبطاقة الشخصية
                                </small>
                                @error('back_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال طلب التوثيق
                                </button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديد الرقم القومي ليكون أرقام فقط
document.querySelector('input[name="national_id"]').addEventListener('input', function(e) {
    this.value = this.value.replace(/[^0-9]/g, '');
});
</script>
@endsection
