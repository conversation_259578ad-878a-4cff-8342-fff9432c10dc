<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Deals extends Model
{
    public $incrementing = false; // لأن UUID مش auto-increment
    protected $keyType = 'string'; // UUID بيكون string

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid(); // توليد UUID تلقائي
        });
    }

    protected $fillable = ['id', 'email', 'unique_ad_code', 'ad_id'];

    /**
     * Get the ad that belongs to this deal.
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }
}
