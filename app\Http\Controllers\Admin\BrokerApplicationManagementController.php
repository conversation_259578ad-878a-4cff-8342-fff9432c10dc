<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BrokerApplication;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;

class BrokerApplicationManagementController extends Controller
{
    /**
     * عرض جميع طلبات المندوبين
     */
    public function index(): View
    {
        // الطلبات في انتظار المراجعة
        $pendingApplications = BrokerApplication::with('user')
                                              ->where('status', BrokerApplication::STATUS_PENDING)
                                              ->orderBy('created_at', 'asc')
                                              ->get();

        // الطلبات المقبولة في المرحلة الأولى
        $firstStageAccepted = BrokerApplication::with('user')
                                             ->where('status', BrokerApplication::STATUS_FIRST_STAGE_ACCEPTED)
                                             ->orderBy('updated_at', 'desc')
                                             ->get();

        // الطلبات المقبولة في المرحلة الثانية (لم يتم قبولها نهائياً بعد)
        $secondStageAccepted = BrokerApplication::with('user')
                                              ->where('status', BrokerApplication::STATUS_SECOND_STAGE_ACCEPTED)
                                              ->whereHas('user', function($query) {
                                                  $query->where('is_broker', false);
                                              })
                                              ->orderBy('updated_at', 'desc')
                                              ->get();

        // الطلبات المرفوضة
        $rejectedApplications = BrokerApplication::with('user')
                                               ->where('status', BrokerApplication::STATUS_REJECTED)
                                               ->orderBy('rejected_at', 'desc')
                                               ->get();

        // المقبولين نهائياً (المندوبين الحاليين الذين لديهم طلبات)
        $acceptedBrokers = BrokerApplication::with('user')
                                          ->whereHas('user', function($query) {
                                              $query->where('is_broker', true);
                                          })
                                          ->where('status', BrokerApplication::STATUS_SECOND_STAGE_ACCEPTED)
                                          ->orderBy('updated_at', 'desc')
                                          ->get();

        // إحصائيات
        $stats = [
            'total' => BrokerApplication::count(),
            'pending' => $pendingApplications->count(),
            'first_stage' => $firstStageAccepted->count(),
            'second_stage' => $secondStageAccepted->count(),
            'rejected' => $rejectedApplications->count(),
            'accepted_brokers' => $acceptedBrokers->count(),
        ];

        return view('admin.broker-applications.index', compact(
            'pendingApplications',
            'firstStageAccepted',
            'secondStageAccepted',
            'rejectedApplications',
            'acceptedBrokers',
            'stats'
        ))->with('active', 'broker-applications');
    }

    /**
     * عرض تفاصيل طلب معين
     */
    public function show(BrokerApplication $application): View
    {
        $application->load('user');
        return view('admin.broker-applications.show', compact('application'))
            ->with('active', 'broker-applications');
    }

    /**
     * قبول الطلب في المرحلة الأولى
     */
    public function acceptFirstStage(BrokerApplication $application): RedirectResponse
    {
        if ($application->status !== BrokerApplication::STATUS_PENDING) {
            return back()->with('error', 'لا يمكن قبول هذا الطلب في الوقت الحالي');
        }

        $application->update([
            'status' => BrokerApplication::STATUS_FIRST_STAGE_ACCEPTED
        ]);

        Log::info('تم قبول طلب مندوب في المرحلة الأولى', [
            'application_id' => $application->id,
            'user_id' => $application->user_id,
            'user_name' => $application->user->name,
            'admin_id' => auth()->id(),
            'admin_name' => auth()->user()->name,
        ]);

        return redirect()->route('admin.broker-applications.index')
            ->with('success', 'تم قبول الطلب في المرحلة الأولى بنجاح');
    }

    /**
     * قبول الطلب نهائياً وتحويل المستخدم إلى مندوب
     */
    public function acceptFinal(BrokerApplication $application): RedirectResponse
    {
        if ($application->status !== BrokerApplication::STATUS_SECOND_STAGE_ACCEPTED) {
            return back()->with('error', 'لا يمكن قبول هذا الطلب نهائياً في الوقت الحالي');
        }

        // التحقق من الرقم القومي في الطلب
        if (!$application->national_id) {
            return back()->with('error', 'يجب أن يكون للطلب رقم قومي محدد.');
        }

        // التحقق من عدم تكرار الرقم القومي (باستثناء نفس المستخدم)
        $existingUser = \App\Models\User::where('national_id', $application->national_id)
                                       ->where('id', '!=', $application->user->id)
                                       ->first();

        if ($existingUser) {
            return back()->with('error', 'هذا الرقم القومي مسجل مسبقاً للمستخدم: ' . $existingUser->name . ' (ID: ' . $existingUser->id . ')');
        }

        // التحقق من حالة توثيق المستخدم
        $isAlreadyVerified = $application->user->national_id &&
                            $application->user->identityVerification &&
                            $application->user->identityVerification->status === 'approved';

        if ($isAlreadyVerified && $application->user->national_id !== $application->national_id) {
            return back()->with('error', 'المستخدم موثق مسبقاً برقم قومي مختلف (' . $application->user->national_id . '). لا يمكن تغيير الرقم القومي للمستخدمين الموثقين.');
        }

        // تحويل المستخدم إلى مندوب موثق وتحديث الرقم القومي
        $application->user->update([
            'is_broker' => true,
            'is_trusted' => true, // جعله موثق
            'national_id' => $application->national_id, // تحديث الرقم القومي من الطلب
            'points' => ($application->user->points ?? 0) + 1000, // إضافة 1000 نقطة
            'broker_activated_at' => now(), // تاريخ تفعيل المندوب
        ]);

        // تحديث حالة الطلب بدلاً من حذفه
        $application->update([
            'status' => BrokerApplication::STATUS_SECOND_STAGE_ACCEPTED
        ]);

        Log::info('تم قبول طلب مندوب نهائياً', [
            'application_id' => $application->id,
            'user_id' => $application->user_id,
            'user_name' => $application->user->name,
            'admin_id' => auth()->id(),
            'admin_name' => auth()->user()->name,
        ]);

        return redirect()->route('admin.broker-applications.index')
            ->with('success', 'تم قبول الطلب نهائياً وتحويل المستخدم إلى مندوب موثق مع إعطائه 1000 نقطة');
    }

    /**
     * تحديث الرقم القومي في طلب المندوب
     */
    public function updateNationalId(Request $request, BrokerApplication $application): RedirectResponse
    {
        $request->validate([
            'national_id' => 'required|string|max:20',
        ]);

        // التحقق من حالة توثيق المستخدم
        $isUserVerified = $application->user->national_id &&
                         $application->user->identityVerification &&
                         $application->user->identityVerification->status === 'approved';

        if ($isUserVerified) {
            return back()->with('error', 'لا يمكن تعديل الرقم القومي للمستخدمين الموثقين. المستخدم موثق بالرقم: ' . $application->user->national_id);
        }

        // التحقق من عدم تكرار الرقم القومي (باستثناء نفس المستخدم)
        $existingUser = \App\Models\User::where('national_id', $request->national_id)
                                       ->where('id', '!=', $application->user->id)
                                       ->first();

        if ($existingUser) {
            return back()->with('error', 'هذا الرقم القومي مسجل مسبقاً للمستخدم: ' . $existingUser->name . ' (ID: ' . $existingUser->id . ')');
        }

        $application->update([
            'national_id' => $request->national_id,
        ]);

        return back()->with('success', 'تم تحديث الرقم القومي بنجاح.');
    }

    /**
     * رفض الطلب
     */
    public function reject(Request $request, BrokerApplication $application): RedirectResponse
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $application->update([
            'status' => BrokerApplication::STATUS_REJECTED,
            'rejection_reason' => $request->rejection_reason,
            'rejected_at' => now(),
        ]);

        Log::info('تم رفض طلب مندوب', [
            'application_id' => $application->id,
            'user_id' => $application->user_id,
            'user_name' => $application->user->name,
            'rejection_reason' => $request->rejection_reason,
            'admin_id' => auth()->id(),
            'admin_name' => auth()->user()->name,
        ]);

        return redirect()->route('admin.broker-applications.index')
            ->with('success', 'تم رفض الطلب بنجاح');
    }

    /**
     * حذف طلب مرفوض محدد
     */
    public function deleteRejected(BrokerApplication $application): RedirectResponse
    {
        // التأكد من أن الطلب مرفوض
        if ($application->status !== BrokerApplication::STATUS_REJECTED) {
            return redirect()->route('admin.broker-applications.index')
                            ->with('error', 'يمكن حذف الطلبات المرفوضة فقط');
        }

        $application->delete();

        return redirect()->route('admin.broker-applications.index')
                        ->with('success', 'تم حذف الطلب المرفوض بنجاح');
    }

    /**
     * حذف الطلبات المرفوضة القديمة (بعد أسبوعين)
     */
    public function deleteOldRejected(): RedirectResponse
    {
        $deletedCount = BrokerApplication::where('status', BrokerApplication::STATUS_REJECTED)
                                       ->where('rejected_at', '<=', now()->subWeeks(2))
                                       ->delete();

        return redirect()->route('admin.broker-applications.index')
            ->with('success', "تم حذف {$deletedCount} طلب مرفوض قديم");
    }

    /**
     * حذف طلب مندوب (عام)
     */
    public function destroy(BrokerApplication $application): RedirectResponse
    {
        try {
            $application->delete(); // سيتم حذف المستندات تلقائياً عبر Observer

            return redirect()->route('admin.broker-applications.index')
                            ->with('success', 'تم حذف طلب المندوب وجميع مستنداته بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف طلب المندوب');
        }
    }
}
