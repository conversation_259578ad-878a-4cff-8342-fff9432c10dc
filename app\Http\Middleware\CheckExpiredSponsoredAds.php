<?php

namespace App\Http\Middleware;

use App\Models\SponsoredAd;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class CheckExpiredSponsoredAds
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // تشغيل الفحص كل 10 دقائق فقط لتوفير الأداء
        $cacheKey = 'sponsored_ads_last_check';
        $lastCheck = Cache::get($cacheKey);
        
        if (!$lastCheck || now()->diffInMinutes($lastCheck) >= 10) {
            $this->checkAndExpireAds();
            Cache::put($cacheKey, now(), now()->addMinutes(10));
        }

        return $next($request);
    }

    /**
     * فحص وإنهاء الإعلانات الممولة المنتهية
     */
    private function checkAndExpireAds(): void
    {
        try {
            $expiredAds = SponsoredAd::where('is_active', true)
                ->where('status', 'active')
                ->where('expires_at', '<=', now())
                ->get();

            foreach ($expiredAds as $sponsoredAd) {
                $sponsoredAd->expireSponsorship();
            }

            if ($expiredAds->count() > 0) {
                Log::info("Expired {$expiredAds->count()} sponsored ads via middleware");
            }
        } catch (\Exception $e) {
            Log::error('Error checking expired sponsored ads: ' . $e->getMessage());
        }
    }
}
