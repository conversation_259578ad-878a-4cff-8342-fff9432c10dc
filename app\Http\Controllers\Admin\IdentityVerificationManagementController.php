<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\IdentityVerification;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class IdentityVerificationManagementController extends Controller
{
    /**
     * عرض صفحة إدارة طلبات توثيق الهوية
     */
    public function index(): View
    {
        // الطلبات المعلقة
        $pendingVerifications = IdentityVerification::with('user')
            ->where('status', IdentityVerification::STATUS_PENDING)
            ->orderBy('created_at', 'desc')
            ->paginate(15);


        // الطلبات المقبولة
        $approvedVerifications = IdentityVerification::with('user')
            ->where('status', IdentityVerification::STATUS_APPROVED)
            ->orderBy('approved_at', 'desc')
            ->paginate(10);

        $rejectedVerifications = IdentityVerification::with('user')
            ->where('status', IdentityVerification::STATUS_REJECTED)
            ->orderBy('rejected_at', 'desc')
            ->paginate(10);

        // الإحصائيات
        $stats = [
            'pending' => $pendingVerifications->count(),
            'approved' => $approvedVerifications->count(),
            'rejected' => $rejectedVerifications->count(),
            'total' => IdentityVerification::count(),
        ];

        // Get counts for tabs
        $pendingCount = $pendingVerifications->count();
        $approvedCount = $approvedVerifications->count();
        $rejectedCount = $rejectedVerifications->count();

        return view('admin.identity-verifications.index', compact(
            'pendingVerifications',
            'approvedVerifications',
            'rejectedVerifications',
            'stats',
            'pendingCount',
            'approvedCount',
            'rejectedCount'
        ));
    }

    /**
     * عرض تفاصيل طلب توثيق الهوية
     */
    public function show(IdentityVerification $identityVerification): View
    {
        $identityVerification->load('user');
        return view('admin.identity-verifications.show', compact('identityVerification'));
    }

    /**
     * قبول طلب توثيق الهوية
     */
    public function approve(IdentityVerification $identityVerification): RedirectResponse
    {
        if ($identityVerification->status !== IdentityVerification::STATUS_PENDING) {
            return back()->with('error', 'لا يمكن قبول هذا الطلب في الوقت الحالي');
        }

        // التحقق من عدم وجود رقم قومي مكرر
        $existingUser = \App\Models\User::where('national_id', $identityVerification->national_id)
            ->where('id', '!=', $identityVerification->user_id)
            ->first();

        if ($existingUser) {
            return back()->with('error',
                'هذا الرقم القومي مسجل مسبقاً للمستخدم: ' . $existingUser->name .
                ' <a href="' . route('admin.users.edit', $existingUser->id) . '" target="_blank" class="text-primary">عرض المستخدم</a>'
            );
        }

        // تحديث حالة الطلب
        $identityVerification->update([
            'status' => IdentityVerification::STATUS_APPROVED,
            'approved_at' => now(),
            'rejection_reason' => null,
        ]);

        // تحديث حالة المستخدم ليصبح موثق وحفظ الرقم القومي
        $identityVerification->user->update([
            'is_trusted' => true,
            'national_id' => $identityVerification->national_id,
        ]);

        return back()->with('success', 'تم قبول طلب توثيق الهوية بنجاح');
    }

    /**
     * رفض طلب توثيق الهوية
     */
    public function reject(Request $request, IdentityVerification $identityVerification): RedirectResponse
    {
        if ($identityVerification->status !== IdentityVerification::STATUS_PENDING) {
            return back()->with('error', 'لا يمكن رفض هذا الطلب في الوقت الحالي');
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ], [
            'rejection_reason.required' => 'سبب الرفض مطلوب',
            'rejection_reason.max' => 'سبب الرفض يجب ألا يزيد عن 1000 حرف',
        ]);

        // تحديث حالة الطلب
        $identityVerification->update([
            'status' => IdentityVerification::STATUS_REJECTED,
            'rejected_at' => now(),
            'rejection_reason' => $request->rejection_reason,
        ]);

        // التأكد من أن المستخدم ليس موثق
        $identityVerification->user->update([
            'is_trusted' => false,
        ]);

        return back()->with('success', 'تم رفض طلب توثيق الهوية');
    }

    /**
     * تحديث الرقم القومي في طلب توثيق الهوية
     */
    public function updateNationalId(Request $request, IdentityVerification $identityVerification): RedirectResponse
    {
        $request->validate([
            'national_id' => 'required|string|max:20',
        ]);

        // التحقق من عدم تكرار الرقم القومي
        $existingUser = \App\Models\User::where('national_id', $request->national_id)
                                       ->first();

        if ($existingUser) {
            return back()->with('error', 'هذا الرقم القومي مسجل مسبقاً للمستخدم: ' . $existingUser->name . ' (ID: ' . $existingUser->id . ')');
        }

        $identityVerification->update([
            'national_id' => $request->national_id,
        ]);

        return back()->with('success', 'تم تحديث الرقم القومي بنجاح.');
    }

    /**
     * حذف طلب توثيق الهوية
     */
    public function destroy(IdentityVerification $identityVerification): RedirectResponse
    {
        try {
            $identityVerification->delete(); // سيتم حذف الصور تلقائياً عبر Observer

            return redirect()->route('admin.identity-verifications.index')
                            ->with('success', 'تم حذف طلب توثيق الهوية وجميع صوره بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف طلب توثيق الهوية');
        }
    }
}
