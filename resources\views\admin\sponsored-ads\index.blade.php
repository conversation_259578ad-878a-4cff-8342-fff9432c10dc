@extends('partials.admin')
@section('title', 'إدارة الإعلانات الممولة')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'sponsored-ads'])
@php use Illuminate\Support\Str; @endphp

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'إدارة الإعلانات الممولة', 'hasBack' => true, 'backTitle' => 'Dashboard', 'backUrl' => route('admin.dashboard')])
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">إدارة الإعلانات الممولة</h3>
                    <div>
                        <a href="{{ route('admin.sponsored-ads.update-all') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-sync"></i> تحديث الكل
                        </a>
                    </div>
                </div>

                <!-- إحصائيات -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-bullhorn"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي</span>
                                    <span class="info-box-number">{{ $stats['total'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-play"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">نشط</span>
                                    <span class="info-box-number">{{ $stats['active'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">معلق</span>
                                    <span class="info-box-number">{{ $stats['pending'] }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-stop"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">غير نشط</span>
                                    <span class="info-box-number">{{ $stats['inactive'] ?? 0 }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الإيرادات</span>
                                    <span class="info-box-number">{{ money($stats['total_revenue']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs nav-tabs-header mb-4" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link {{ ($activeTab ?? 'active') === 'active' ? 'active' : '' }}"
                               href="{{ route('admin.sponsored-ads.index', ['tab' => 'active']) }}">
                                <i class="fas fa-check-circle me-2"></i>
                                النشطة ({{ $stats['active'] }})
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ ($activeTab ?? '') === 'inactive' ? 'active' : '' }}"
                               href="{{ route('admin.sponsored-ads.index', ['tab' => 'inactive']) }}">
                                <i class="fas fa-times-circle me-2"></i>
                                غير نشطة ({{ $stats['inactive'] ?? 0 }})
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ ($activeTab ?? '') === 'pending' ? 'active' : '' }}"
                            href="{{ route('admin.sponsored-ads.index', ['tab' => 'pending']) }}">
                                <i class="fas fa-clock me-2"></i>
                                معلقة ({{ $stats['pending'] }})
                            </a>
                        </li>
                    </ul>

                    <!-- فلاتر البحث -->
                    <form method="GET" class="mb-4">
                        <input type="hidden" name="tab" value="{{ $activeTab ?? 'active' }}">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="البحث بعنوان الإعلان أو ID" value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <select name="priority" class="form-control">
                                    <option value="">جميع الأولويات</option>
                                    @foreach($priorities as $value => $label)
                                        <option value="{{ $value }}" {{ request('priority') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="country_id" class="form-control">
                                    <option value="">جميع الدول</option>
                                    @foreach($countries as $country)
                                        <option value="{{ $country->id }}" {{ request('country_id') == $country->id ? 'selected' : '' }}>
                                            {{ $country->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary">بحث</button>
                                <a href="{{ route('admin.sponsored-ads.index', ['tab' => $activeTab ?? 'active']) }}" class="btn btn-secondary">إعادة تعيين</a>
                            </div>
                        </div>
                    </form>

                    <!-- جدول الإعلانات الممولة -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>الإعلان</th>
                                    <th>المستخدم</th>
                                    <th>التكلفة</th>
                                    <th>المدة</th>
                                    <th>الحالة</th>
                                    <th>بداية الرعاية</th>
                                    <th>انتهاء الرعاية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sponsoredAds as $sponsoredAd)
                                <tr>
                                    <td>{{ $sponsoredAd->id }}</td>
                                    <td>
                                        <a href="{{ route('admin.ads.show', $sponsoredAd->ad->slug) }}" target="_blank">
                                            {{ \Illuminate\Support\Str::limit($sponsoredAd->ad->title, 30) }}
                                        </a>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.users.show', $sponsoredAd->ad->user->id) }}" target="_blank">
                                            {{ $sponsoredAd->ad->user->name }}
                                        </a>
                                    </td>
                                    <td>{{ money($sponsoredAd->cost) }}</td>
                                    <td>{{ $sponsoredAd->total_minutes }} دقيقة</td>
                                    <td>
                                        <span class="badge bg-{{ $sponsoredAd->isActive() ? 'success' : ($sponsoredAd->isExpired() ? 'danger' : 'warning') }}">
                                            {{ $sponsoredAd->status }}
                                        </span>
                                    </td>
                                    <td>{{ $sponsoredAd->started_at ? $sponsoredAd->started_at->format('Y-m-d H:i') : '-' }}</td>
                                    <td>{{ $sponsoredAd->expires_at ? $sponsoredAd->expires_at->format('Y-m-d H:i') : '-' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.sponsored-ads.show', $sponsoredAd) }}" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($sponsoredAd->status == 'pending')
                                                @if($sponsoredAd->ad->status->value === 'published')
                                                    <!-- الإعلان منشور، فقط تفعيل الرعاية -->
                                                    <form method="POST" action="{{ route('admin.sponsored-ads.activate', $sponsoredAd) }}" style="display: inline;">
                                                        @csrf
                                                        <button type="submit" class="btn btn-success btn-sm" onclick="return confirm('تفعيل الرعاية؟')" title="تفعيل الرعاية">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                    </form>
                                                @else
                                                    <!-- الإعلان غير منشور، إظهار زر الموافقة والتفعيل -->
                                                    <form method="POST" action="{{ route('admin.sponsored-ads.approve-and-activate', $sponsoredAd) }}" style="display: inline;">
                                                        @csrf
                                                        <button type="submit" class="btn btn-primary btn-sm" onclick="return confirm('سيتم نشر الإعلان وتفعيل الرعاية. هل تريد المتابعة؟')" title="نشر الإعلان وتفعيل الرعاية">
                                                            <i class="fas fa-check-double"></i>
                                                        </button>
                                                    </form>
                                                    <a href="{{ route('admin.ads.edit', $sponsoredAd->ad->slug) }}" class="btn btn-warning btn-sm" target="_blank" title="مراجعة الإعلان">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif

                                                <form method="POST" action="{{ route('admin.sponsored-ads.reject', $sponsoredAd) }}" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('رفض الرعاية؟')" title="رفض">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </form>
                                            @elseif($sponsoredAd->is_active)
                                                <form method="POST" action="{{ route('admin.sponsored-ads.deactivate', $sponsoredAd) }}" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('إيقاف الرعاية؟')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            <!-- حذف الرعاية فقط -->
                                            <form method="POST" action="{{ route('admin.sponsored-ads.destroy', $sponsoredAd) }}" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-secondary btn-sm" onclick="return confirm('حذف سجل الرعاية فقط؟ (الإعلان سيبقى موجود)')" title="حذف الرعاية فقط">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </form>

                                            <!-- حذف الإعلان كاملاً -->
                                            <form method="POST" action="{{ route('admin.sponsored-ads.destroy-ad', $sponsoredAd) }}" style="display: inline;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('حذف الإعلان كاملاً؟ هذا الإجراء لا يمكن التراجع عنه!')" title="حذف الإعلان كاملاً">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center">لا توجد إعلانات ممولة</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {{ $sponsoredAds->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
        <!-- CONTAINER END -->
    </div>
</div>
@endsection
