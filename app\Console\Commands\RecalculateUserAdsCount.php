<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Ad;
use App\Enums\AdStatus;
use Illuminate\Console\Command;

class RecalculateUserAdsCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:recalculate-ads-count {--user-id= : Specific user ID to recalculate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate the Number_Ads count for all users or a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        
        if ($userId) {
            $this->recalculateForUser($userId);
        } else {
            $this->recalculateForAllUsers();
        }
    }

    /**
     * Recalculate ads count for a specific user
     */
    private function recalculateForUser($userId)
    {
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found.");
            return;
        }

        $publishedAdsCount = Ad::where('user_id', $user->id)
            ->where('status', AdStatus::PUBLISHED)
            ->count();

        $user->update(['Number_Ads' => $publishedAdsCount]);

        $this->info("Updated user {$user->name} (ID: {$user->id}): {$publishedAdsCount} published ads");
    }

    /**
     * Recalculate ads count for all users
     */
    private function recalculateForAllUsers()
    {
        $this->info('Starting recalculation of ads count for all users...');
        
        $users = User::all();
        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->start();

        $updatedCount = 0;

        foreach ($users as $user) {
            // حساب عدد الإعلانات المنشورة للمستخدم
            $publishedAdsCount = Ad::where('user_id', $user->id)
                ->where('status', AdStatus::PUBLISHED)
                ->count();

            // تحديث العدد إذا كان مختلفاً
            if ($user->Number_Ads != $publishedAdsCount) {
                $user->update(['Number_Ads' => $publishedAdsCount]);
                $updatedCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Recalculation completed! Updated {$updatedCount} users out of {$users->count()} total users.");
    }
}
