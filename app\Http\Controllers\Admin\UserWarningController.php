<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserWarning;
use App\Models\User;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UserWarningController extends Controller
{
    /**
     * Store a new warning.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'type' => 'required|in:' . implode(',', array_keys(UserWarning::TYPES)),
            'title' => 'required|string|max:255',
            'reason' => 'required|string|max:1000',
            'admin_notes' => 'nullable|string|max:1000',
            'duration_days' => 'nullable|integer|min:1|max:365',
            'report_id' => 'nullable|exists:reports,id'
        ]);

        try {
            $expiresAt = null;
            if ($request->duration_days) {
                $expiresAt = Carbon::now()->addDays($request->duration_days);
            }

            $warning = UserWarning::create([
                'user_id' => $request->user_id,
                'admin_id' => Auth::guard('admin_web')->id(),
                'report_id' => $request->report_id,
                'type' => $request->type,
                'title' => $request->title,
                'reason' => $request->reason,
                'admin_notes' => $request->admin_notes,
                'expires_at' => $expiresAt,
                'is_active' => true
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم توجيه التحذير بنجاح',
                'warning' => $warning->load(['user', 'admin'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء توجيه التحذير'
            ], 500);
        }
    }

    /**
     * Get user warnings.
     */
    public function getUserWarnings(Request $request, User $user): JsonResponse
    {
        $warnings = UserWarning::where('user_id', $user->id)
            ->with(['admin', 'report'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'warnings' => $warnings
        ]);
    }

    /**
     * Deactivate a warning.
     */
    public function deactivate(Request $request, UserWarning $warning): JsonResponse
    {
        try {
            $warning->update(['is_active' => false]);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء التحذير بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء التحذير'
            ], 500);
        }
    }

    /**
     * Delete content related to report.
     */
    public function deleteContent(Request $request): JsonResponse
    {
        $request->validate([
            'report_id' => 'required|exists:reports,id',
            'action' => 'required|in:delete_ad,delete_comment,delete_review,delete_message'
        ]);

        try {
            $report = Report::with('reportable')->findOrFail($request->report_id);

            if (!$report->reportable) {
                return response()->json([
                    'success' => false,
                    'message' => 'المحتوى المبلغ عنه غير موجود أو تم حذفه مسبقاً'
                ], 404);
            }

            $message = '';
            $deleted = false;

            switch ($request->action) {
                case 'delete_ad':
                    if ($report->reportable_type === 'App\Models\Ad') {
                        $adTitle = $report->reportable->title;
                        $report->reportable->delete();
                        $message = "تم حذف الإعلان '{$adTitle}' بنجاح";
                        $deleted = true;
                    }
                    break;

                case 'delete_comment':
                    if ($report->reportable_type === 'App\Models\Comment') {
                        $commentContent = substr($report->reportable->content, 0, 50) . '...';
                        $report->reportable->delete();
                        $message = "تم حذف التعليق '{$commentContent}' بنجاح";
                        $deleted = true;
                    }
                    break;

                case 'delete_review':
                    if (in_array($report->reportable_type, ['App\Models\ProductReview', 'App\Models\UserRating'])) {
                        $reviewType = $report->reportable_type === 'App\Models\ProductReview' ? 'تقييم المنتج' : 'تقييم المستخدم';
                        $report->reportable->delete();
                        $message = "تم حذف {$reviewType} بنجاح";
                        $deleted = true;
                    }
                    break;

                case 'delete_message':
                    if ($report->reportable_type === 'App\Models\ChMessage') {
                        $report->reportable->delete();
                        $message = 'تم حذف الرسالة بنجاح';
                        $deleted = true;
                    }
                    break;
            }

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'نوع المحتوى غير متطابق مع الإجراء المطلوب'
                ], 400);
            }

            // تسجيل العملية في الـ logs
            Log::info('Content deleted by admin', [
                'admin_id' => \Auth::guard('admin_web')->id(),
                'report_id' => $report->id,
                'action' => $request->action,
                'reportable_type' => $report->reportable_type,
                'reportable_id' => $report->reportable_id
            ]);

            // تحديث حالة البلاغ
            $report->update([
                'status' => 'resolved',
                'admin_notes' => 'تم حذف المحتوى المبلغ عنه',
                'reviewed_by' => \Auth::guard('admin_web')->id(),
                'reviewed_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting content', [
                'error' => $e->getMessage(),
                'report_id' => $request->report_id,
                'action' => $request->action,
                'admin_id' => \Auth::guard('admin_web')->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف المحتوى: ' . $e->getMessage()
            ], 500);
        }
    }
}
