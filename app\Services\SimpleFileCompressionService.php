<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class SimpleFileCompressionService
{
    /**
     * Compress and store image using basic file operations and quality reduction
     */
    public function compressAndStore(UploadedFile $file, string $directory, array $options = []): array
    {
        // Default options
        $defaultOptions = [
            'quality' => 75,
            'max_width' => 1200,
            'max_height' => 1200,
            'format' => 'jpg',
            'max_file_size' => 500 * 1024, // 500KB target
        ];

        $options = array_merge($defaultOptions, $options);

        // Generate unique filename
        $uuid = Str::uuid();
        $extension = strtolower($file->getClientOriginalExtension());
        
        // Force JPG for better compression
        if (in_array($extension, ['jpeg', 'jpg', 'png', 'gif', 'webp'])) {
            $extension = 'jpg';
        }
        
        $filename = $uuid . '.' . $extension;
        $path = $directory . '/' . $filename;

        // Get original file size
        $originalSize = $file->getSize();

        try {
            // If file is already small enough, store as-is
            if ($originalSize <= $options['max_file_size']) {
                $storedPath = $file->storeAs($directory, $filename, 'public');
                
                if (!$storedPath) {
                    return [
                        'success' => false,
                        'error' => 'Failed to store file'
                    ];
                }

                $compressedSize = Storage::disk('public')->size($storedPath);
                $compressionRatio = 0;

                Log::info('Image stored without compression (already small)', [
                    'original_filename' => $file->getClientOriginalName(),
                    'new_filename' => $filename,
                    'original_size' => $originalSize,
                    'stored_size' => $compressedSize,
                    'path' => $storedPath
                ]);

                return [
                    'success' => true,
                    'path' => $storedPath,
                    'url' => Storage::disk('public')->url($storedPath),
                    'filename' => $filename,
                    'original_size' => $originalSize,
                    'compressed_size' => $compressedSize,
                    'compression_ratio' => $compressionRatio,
                    'format' => $extension
                ];
            }

            // Try to compress using file operations
            $compressedContent = $this->compressImageContent($file, $options);
            
            if ($compressedContent === false) {
                // Fallback: store original file
                $storedPath = $file->storeAs($directory, $filename, 'public');
                $compressedSize = Storage::disk('public')->size($storedPath);
                $compressionRatio = 0;
            } else {
                // Store compressed content
                Storage::disk('public')->put($path, $compressedContent);
                $compressedSize = strlen($compressedContent);
                $storedPath = $path;
            }

            $compressionRatio = $originalSize > 0 ? round((($originalSize - $compressedSize) / $originalSize) * 100, 2) : 0;

            // Log compression results
            Log::info('Image processed successfully', [
                'original_filename' => $file->getClientOriginalName(),
                'new_filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio . '%',
                'format' => $extension,
                'path' => $storedPath,
                'method' => $compressedContent !== false ? 'compressed' : 'original'
            ]);

            return [
                'success' => true,
                'path' => $storedPath,
                'url' => Storage::disk('public')->url($storedPath),
                'filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'format' => $extension,
                'dimensions' => [
                    'width' => 'auto-resized',
                    'height' => 'auto-resized'
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Image processing failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Try to compress image content using available methods
     */
    private function compressImageContent(UploadedFile $file, array $options)
    {
        $originalContent = file_get_contents($file->getRealPath());
        $originalSize = strlen($originalContent);
        
        // Method 1: Try to reduce file size by re-encoding (if possible)
        $tempFile = tempnam(sys_get_temp_dir(), 'compress_');
        file_put_contents($tempFile, $originalContent);
        
        // Try different compression techniques
        $compressedContent = $this->tryImageMagickCompression($tempFile, $options);
        
        if ($compressedContent === false) {
            $compressedContent = $this->tryGDCompression($tempFile, $options);
        }
        
        if ($compressedContent === false) {
            $compressedContent = $this->tryBasicCompression($originalContent, $options);
        }
        
        unlink($tempFile);
        
        return $compressedContent;
    }

    /**
     * Try ImageMagick compression (if available)
     */
    private function tryImageMagickCompression($filePath, $options)
    {
        if (!class_exists('Imagick')) {
            return false;
        }

        try {
            $imagick = new \Imagick($filePath);
            
            // Set compression quality
            $imagick->setImageCompressionQuality($options['quality']);
            $imagick->setImageFormat('JPEG');
            
            // Resize if needed
            $geometry = $imagick->getImageGeometry();
            if ($geometry['width'] > $options['max_width'] || $geometry['height'] > $options['max_height']) {
                $imagick->resizeImage($options['max_width'], $options['max_height'], \Imagick::FILTER_LANCZOS, 1, true);
            }
            
            // Strip metadata
            $imagick->stripImage();
            
            $compressedContent = $imagick->getImageBlob();
            $imagick->destroy();
            
            return $compressedContent;
            
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Try GD compression (if available)
     */
    private function tryGDCompression($filePath, $options)
    {
        if (!function_exists('imagecreatefromjpeg')) {
            return false;
        }

        try {
            $imageInfo = @getimagesize($filePath);
            if ($imageInfo === false) {
                return false;
            }

            // Create image resource based on type
            switch ($imageInfo[2]) {
                case IMAGETYPE_JPEG:
                    $image = @imagecreatefromjpeg($filePath);
                    break;
                case IMAGETYPE_PNG:
                    $image = @imagecreatefrompng($filePath);
                    break;
                case IMAGETYPE_GIF:
                    $image = @imagecreatefromgif($filePath);
                    break;
                default:
                    return false;
            }

            if ($image === false) {
                return false;
            }

            // Create output buffer
            ob_start();
            imagejpeg($image, null, $options['quality']);
            $compressedContent = ob_get_contents();
            ob_end_clean();

            imagedestroy($image);
            
            return $compressedContent;
            
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Basic compression using file operations
     */
    private function tryBasicCompression($content, $options)
    {
        // For now, just return false to use original file
        // This can be enhanced with other compression methods
        return false;
    }

    /**
     * Get optimized settings for different image types
     */
    public function getOptimizedSettings(string $type): array
    {
        return match ($type) {
            'ad_images' => [
                'quality' => 70,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg',
                'max_file_size' => 400 * 1024 // 400KB
            ],
            'profile_avatar' => [
                'quality' => 75,
                'max_width' => 400,
                'max_height' => 400,
                'format' => 'jpg',
                'max_file_size' => 150 * 1024 // 150KB
            ],
            'identity_documents' => [
                'quality' => 85,
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg',
                'max_file_size' => 800 * 1024 // 800KB
            ],
            'broker_documents' => [
                'quality' => 85,
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg',
                'max_file_size' => 800 * 1024 // 800KB
            ],
            default => [
                'quality' => 75,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg',
                'max_file_size' => 500 * 1024 // 500KB
            ]
        };
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, int $maxSizeMB = 10): array
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
            ];
        }

        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Invalid file extension.'
            ];
        }

        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'error' => "File size exceeds {$maxSizeMB}MB limit."
            ];
        }

        if (!$file->isValid()) {
            return [
                'valid' => false,
                'error' => 'Invalid file upload.'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Format file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
