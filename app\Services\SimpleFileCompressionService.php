<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManagerStatic as ImageManager;

class SimpleFileCompressionService
{
    /**
     * Advanced image compression and optimization using multiple techniques
     */
    public function compressAndStore(UploadedFile $file, string $directory, array $options = []): array
    {
        // Default options with aggressive compression
        $defaultOptions = [
            'quality' => 75,
            'max_width' => 1200,
            'max_height' => 1200,
            'format' => 'webp', // WebP for better compression
            'max_file_size' => 300 * 1024, // 300KB target (more aggressive)
            'progressive' => true,
            'strip_metadata' => true,
            'optimize' => true,
            'blur_threshold' => 0.5, // Slight blur for better compression
        ];

        $options = array_merge($defaultOptions, $options);

        // Generate unique filename
        $uuid = Str::uuid();
        $originalExtension = strtolower($file->getClientOriginalExtension());

        // Use WebP for maximum compression, fallback to JPG
        $targetFormat = $options['format'] === 'webp' ? 'webp' : 'jpg';
        $filename = $uuid . '.' . $targetFormat;
        $path = $directory . '/' . $filename;

        // Get original file size
        $originalSize = $file->getSize();

        try {
            // Always compress images, even small ones, for consistency
            $result = $this->advancedImageCompression($file, $options);

            if (!$result['success']) {
                return $result;
            }

            // Store the compressed image
            Storage::disk('public')->put($path, $result['compressed_data']);
            $compressedSize = strlen($result['compressed_data']);

            // If compression didn't achieve target, try more aggressive settings
            if ($compressedSize > $options['max_file_size'] && $originalSize > $options['max_file_size']) {
                $aggressiveOptions = $options;
                $aggressiveOptions['quality'] = max(30, $options['quality'] - 20);
                $aggressiveOptions['max_width'] = (int)($options['max_width'] * 0.8);
                $aggressiveOptions['max_height'] = (int)($options['max_height'] * 0.8);

                $aggressiveResult = $this->advancedImageCompression($file, $aggressiveOptions);
                if ($aggressiveResult['success'] && strlen($aggressiveResult['compressed_data']) < $compressedSize) {
                    Storage::disk('public')->put($path, $aggressiveResult['compressed_data']);
                    $compressedSize = strlen($aggressiveResult['compressed_data']);
                    $result = $aggressiveResult;
                }
            }

            $compressionRatio = $originalSize > 0 ? round((($originalSize - $compressedSize) / $originalSize) * 100, 2) : 0;

            // Log compression results
            Log::info('Advanced image compression completed', [
                'original_filename' => $file->getClientOriginalName(),
                'new_filename' => $filename,
                'original_size' => $this->formatFileSize($originalSize),
                'compressed_size' => $this->formatFileSize($compressedSize),
                'compression_ratio' => $compressionRatio . '%',
                'format' => $targetFormat,
                'dimensions' => $result['dimensions'],
                'path' => $path,
                'techniques_used' => $result['techniques_used'] ?? []
            ]);

            return [
                'success' => true,
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'format' => $targetFormat,
                'dimensions' => $result['dimensions'],
                'techniques_used' => $result['techniques_used'] ?? []
            ];

        } catch (\Exception $e) {
            Log::error('Advanced image compression failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Advanced image compression using Intervention Image with multiple optimization techniques
     */
    private function advancedImageCompression(UploadedFile $file, array $options): array
    {
        try {
            $techniquesUsed = [];

            // Load image with Intervention Image
            $image = Image::make($file->getRealPath());
            $originalWidth = $image->width();
            $originalHeight = $image->height();

            $techniquesUsed[] = 'intervention_image_loaded';

            // 1. Smart resizing - maintain aspect ratio but reduce dimensions
            if ($originalWidth > $options['max_width'] || $originalHeight > $options['max_height']) {
                $image->resize($options['max_width'], $options['max_height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize(); // Prevent upsizing
                });
                $techniquesUsed[] = 'smart_resize';
            }

            // 2. Strip all metadata (EXIF, IPTC, etc.) for smaller file size
            if ($options['strip_metadata']) {
                // This is handled by encoding options
                $techniquesUsed[] = 'metadata_stripped';
            }

            // 3. Apply slight sharpening to compensate for compression
            $image->sharpen(10);
            $techniquesUsed[] = 'sharpening_applied';

            // 4. Optimize colors - reduce color palette if possible
            $image->limitColors(256, '#ffffff');
            $techniquesUsed[] = 'color_optimization';

            // 5. Apply slight blur for better compression (if enabled)
            if (isset($options['blur_threshold']) && $options['blur_threshold'] > 0) {
                $image->blur($options['blur_threshold']);
                $techniquesUsed[] = 'compression_blur';
            }

            // 6. Encode with optimized settings
            $encodedData = $this->encodeImageWithOptimization($image, $options, $techniquesUsed);

            return [
                'success' => true,
                'compressed_data' => $encodedData,
                'dimensions' => [
                    'width' => $image->width(),
                    'height' => $image->height(),
                    'original_width' => $originalWidth,
                    'original_height' => $originalHeight
                ],
                'techniques_used' => $techniquesUsed
            ];

        } catch (\Exception $e) {
            Log::error('Advanced compression failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Encode image with advanced optimization techniques
     */
    private function encodeImageWithOptimization($image, array $options, array &$techniquesUsed): string
    {
        $format = $options['format'];
        $quality = $options['quality'];

        // Try WebP first for maximum compression
        if ($format === 'webp' && function_exists('imagewebp')) {
            try {
                $encodedData = $image->encode('webp', $quality);
                $techniquesUsed[] = 'webp_encoding';

                // If WebP is too large, try with lower quality
                if (strlen($encodedData) > $options['max_file_size']) {
                    $lowerQuality = max(20, $quality - 30);
                    $encodedData = $image->encode('webp', $lowerQuality);
                    $techniquesUsed[] = 'webp_quality_reduced';
                }

                return $encodedData;
            } catch (\Exception $e) {
                Log::warning('WebP encoding failed, falling back to JPEG', ['error' => $e->getMessage()]);
            }
        }

        // Fallback to JPEG with progressive and optimization
        try {
            // Progressive JPEG for better perceived loading
            $jpegData = $image->encode('jpg', $quality);
            $techniquesUsed[] = 'progressive_jpeg';

            // If still too large, reduce quality further
            if (strlen($jpegData) > $options['max_file_size']) {
                $attempts = 0;
                $currentQuality = $quality;

                while (strlen($jpegData) > $options['max_file_size'] && $currentQuality > 20 && $attempts < 5) {
                    $currentQuality -= 15;
                    $jpegData = $image->encode('jpg', $currentQuality);
                    $attempts++;
                }

                if ($attempts > 0) {
                    $techniquesUsed[] = 'aggressive_quality_reduction';
                }
            }

            return $jpegData;

        } catch (\Exception $e) {
            Log::error('JPEG encoding failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Create multiple image variants for different use cases
     */
    public function createImageVariants(UploadedFile $file, string $directory, string $imageType): array
    {
        $variants = [];
        $baseSettings = $this->getOptimizedSettings($imageType);

        // Create different variants based on image type
        switch ($imageType) {
            case 'ad_images':
                $variants = [
                    'thumbnail' => array_merge($baseSettings, [
                        'max_width' => 300,
                        'max_height' => 300,
                        'quality' => 60,
                        'max_file_size' => 50 * 1024 // 50KB
                    ]),
                    'medium' => array_merge($baseSettings, [
                        'max_width' => 800,
                        'max_height' => 800,
                        'quality' => 65,
                        'max_file_size' => 200 * 1024 // 200KB
                    ]),
                    'large' => $baseSettings
                ];
                break;

            case 'profile_avatar':
                $variants = [
                    'small' => array_merge($baseSettings, [
                        'max_width' => 150,
                        'max_height' => 150,
                        'quality' => 70,
                        'max_file_size' => 30 * 1024 // 30KB
                    ]),
                    'medium' => $baseSettings
                ];
                break;

            default:
                $variants = ['original' => $baseSettings];
        }

        $results = [];
        foreach ($variants as $variantName => $settings) {
            $variantDirectory = $directory . '/' . $variantName;
            $result = $this->compressAndStore($file, $variantDirectory, $settings);

            if ($result['success']) {
                $results[$variantName] = $result;
            }
        }

        return $results;
    }

    /**
     * Get highly optimized settings for different image types with aggressive compression
     */
    public function getOptimizedSettings(string $type): array
    {
        return match ($type) {
            'ad_images' => [
                'quality' => 60, // Reduced from 70
                'max_width' => 1000, // Reduced from 1200
                'max_height' => 1000,
                'format' => 'webp', // Changed to WebP for better compression
                'max_file_size' => 200 * 1024, // Reduced from 400KB to 200KB
                'progressive' => true,
                'strip_metadata' => true,
                'optimize' => true,
                'blur_threshold' => 0.3
            ],
            'profile_avatar' => [
                'quality' => 65, // Reduced from 75
                'max_width' => 300, // Reduced from 400
                'max_height' => 300,
                'format' => 'webp', // Changed to WebP
                'max_file_size' => 80 * 1024, // Reduced from 150KB to 80KB
                'progressive' => true,
                'strip_metadata' => true,
                'optimize' => true,
                'blur_threshold' => 0.2
            ],
            'identity_documents' => [
                'quality' => 75, // Reduced from 85 but kept higher for document readability
                'max_width' => 1400, // Reduced from 1600
                'max_height' => 1000, // Reduced from 1200
                'format' => 'webp', // Changed to WebP
                'max_file_size' => 400 * 1024, // Reduced from 800KB to 400KB
                'progressive' => true,
                'strip_metadata' => true,
                'optimize' => true,
                'blur_threshold' => 0.1 // Minimal blur for documents
            ],
            'broker_documents' => [
                'quality' => 75, // Reduced from 85
                'max_width' => 1400, // Reduced from 1600
                'max_height' => 1000, // Reduced from 1200
                'format' => 'webp', // Changed to WebP
                'max_file_size' => 400 * 1024, // Reduced from 800KB to 400KB
                'progressive' => true,
                'strip_metadata' => true,
                'optimize' => true,
                'blur_threshold' => 0.1 // Minimal blur for documents
            ],
            default => [
                'quality' => 65, // Reduced from 75
                'max_width' => 1000, // Reduced from 1200
                'max_height' => 1000,
                'format' => 'webp', // Changed to WebP
                'max_file_size' => 250 * 1024, // Reduced from 500KB to 250KB
                'progressive' => true,
                'strip_metadata' => true,
                'optimize' => true,
                'blur_threshold' => 0.3
            ]
        };
    }

    /**
     * Enhanced image validation with size and quality checks
     */
    public function validateImage(UploadedFile $file, int $maxSizeMB = 25): array
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/tiff'];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'tif'];
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        // Check MIME type
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, WebP, BMP, and TIFF are allowed.'
            ];
        }

        // Check file extension
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Invalid file extension.'
            ];
        }

        // Check file size (increased limit to 25MB to handle large images before compression)
        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'error' => "File size exceeds {$maxSizeMB}MB limit. Please choose a smaller image."
            ];
        }

        // Check if file upload is valid
        if (!$file->isValid()) {
            return [
                'valid' => false,
                'error' => 'Invalid file upload. Please try again.'
            ];
        }

        // Additional validation: Check if it's actually an image
        try {
            $imageInfo = @getimagesize($file->getRealPath());
            if ($imageInfo === false) {
                return [
                    'valid' => false,
                    'error' => 'File is not a valid image.'
                ];
            }

            // Check minimum dimensions (avoid tiny images)
            if ($imageInfo[0] < 50 || $imageInfo[1] < 50) {
                return [
                    'valid' => false,
                    'error' => 'Image dimensions are too small. Minimum size is 50x50 pixels.'
                ];
            }

            // Check maximum dimensions (avoid extremely large images)
            if ($imageInfo[0] > 10000 || $imageInfo[1] > 10000) {
                return [
                    'valid' => false,
                    'error' => 'Image dimensions are too large. Maximum size is 10000x10000 pixels.'
                ];
            }

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => 'Unable to process image file.'
            ];
        }

        return [
            'valid' => true,
            'dimensions' => [
                'width' => $imageInfo[0],
                'height' => $imageInfo[1]
            ],
            'original_size' => $file->getSize()
        ];
    }

    /**
     * Format file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Batch compress multiple images with progress tracking
     */
    public function batchCompress(array $files, string $directory, string $imageType): array
    {
        $results = [];
        $totalFiles = count($files);
        $processed = 0;
        $totalOriginalSize = 0;
        $totalCompressedSize = 0;

        foreach ($files as $index => $file) {
            $result = $this->compressAndStore($file, $directory, $this->getOptimizedSettings($imageType));

            $results[] = $result;
            $processed++;

            if ($result['success']) {
                $totalOriginalSize += $result['original_size'];
                $totalCompressedSize += $result['compressed_size'];
            }

            // Log progress for large batches
            if ($totalFiles > 5) {
                Log::info("Batch compression progress", [
                    'processed' => $processed,
                    'total' => $totalFiles,
                    'current_file' => $file->getClientOriginalName(),
                    'success' => $result['success']
                ]);
            }
        }

        $overallCompressionRatio = $totalOriginalSize > 0 ?
            round((($totalOriginalSize - $totalCompressedSize) / $totalOriginalSize) * 100, 2) : 0;

        return [
            'results' => $results,
            'summary' => [
                'total_files' => $totalFiles,
                'successful' => count(array_filter($results, fn($r) => $r['success'])),
                'failed' => count(array_filter($results, fn($r) => !$r['success'])),
                'total_original_size' => $this->formatFileSize($totalOriginalSize),
                'total_compressed_size' => $this->formatFileSize($totalCompressedSize),
                'overall_compression_ratio' => $overallCompressionRatio . '%',
                'space_saved' => $this->formatFileSize($totalOriginalSize - $totalCompressedSize)
            ]
        ];
    }

    /**
     * Get compression statistics for monitoring
     */
    public function getCompressionStats(): array
    {
        // This could be enhanced to read from database or cache
        return [
            'total_images_processed' => 'N/A',
            'average_compression_ratio' => 'N/A',
            'total_space_saved' => 'N/A',
            'most_effective_format' => 'WebP',
            'recommended_settings' => $this->getOptimizedSettings('default')
        ];
    }

    /**
     * Test compression with different settings to find optimal configuration
     */
    public function testCompressionSettings(UploadedFile $file): array
    {
        $testSettings = [
            'conservative' => [
                'quality' => 80,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            'balanced' => [
                'quality' => 65,
                'max_width' => 1000,
                'max_height' => 1000,
                'format' => 'webp'
            ],
            'aggressive' => [
                'quality' => 45,
                'max_width' => 800,
                'max_height' => 800,
                'format' => 'webp'
            ]
        ];

        $results = [];
        $originalSize = $file->getSize();

        foreach ($testSettings as $settingName => $settings) {
            try {
                $result = $this->advancedImageCompression($file, $settings);
                if ($result['success']) {
                    $compressedSize = strlen($result['compressed_data']);
                    $compressionRatio = round((($originalSize - $compressedSize) / $originalSize) * 100, 2);

                    $results[$settingName] = [
                        'compressed_size' => $this->formatFileSize($compressedSize),
                        'compression_ratio' => $compressionRatio . '%',
                        'dimensions' => $result['dimensions'],
                        'techniques_used' => $result['techniques_used']
                    ];
                }
            } catch (\Exception $e) {
                $results[$settingName] = [
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'original_size' => $this->formatFileSize($originalSize),
            'test_results' => $results,
            'recommendation' => $this->getRecommendedSetting($results)
        ];
    }

    /**
     * Get recommended compression setting based on test results
     */
    private function getRecommendedSetting(array $testResults): string
    {
        // Simple logic to recommend based on compression ratio
        $bestRatio = 0;
        $recommended = 'balanced';

        foreach ($testResults as $setting => $result) {
            if (isset($result['compression_ratio'])) {
                $ratio = (float) str_replace('%', '', $result['compression_ratio']);
                if ($ratio > $bestRatio && $ratio < 90) { // Avoid over-compression
                    $bestRatio = $ratio;
                    $recommended = $setting;
                }
            }
        }

        return $recommended;
    }
}
