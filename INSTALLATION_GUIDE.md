# دليل تثبيت نظام الإعلانات الممولة

## خطوات التثبيت

### 1. تشغيل Migration
```bash
php artisan migrate
```
هذا سيضيف الأعمدة الجديدة لجدول `sponsored_ads`:
- `views_count` (عدد المشاهدات)
- `clicks_count` (عدد النقرات)  
- `ctr` (معدل النقر)

### 2. تحديث Composer (إذا لزم الأمر)
```bash
composer dump-autoload
```

### 3. مسح الكاش
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### 4. تشغيل المهام المجدولة
تأكد من أن cron job يعمل:
```bash
# إضافة في crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### 5. اخ<PERSON><PERSON><PERSON>ر النظام

#### أ. اختبار الإعلانات الممولة في البحث
1. اذهب إلى `http://127.0.0.1:8000/live-auction`
2. تأكد من ظهور الإعلانات الممولة كل 4 إعلانات عادية
3. تحقق من التصميم المميز (إطار ذهبي + شارة "إعلان ممول")

#### ب. اختبار صفحة التفاصيل
1. اذهب إلى أي إعلان: `http://127.0.0.1:8000/auction-details/[slug]`
2. تحقق من ظهور الإعلانات الممولة في الـ sidebar
3. تأكد من أنها من نفس الفئة الرئيسية

#### ج. اختبار التتبع
1. افتح Developer Tools في المتصفح
2. انقر على إعلان ممول
3. تحقق من إرسال طلب POST إلى `/api/sponsored-ads/{id}/click`

### 6. اختبار الأوامر

#### تشغيل تنظيف الإعلانات المنتهية (معاينة)
```bash
php artisan sponsored-ads:cleanup --dry-run
```

#### تشغيل التنظيف الفعلي
```bash
php artisan sponsored-ads:cleanup
```

### 7. التحقق من API Endpoints

#### تتبع المشاهدة
```bash
curl -X POST http://127.0.0.1:8000/api/sponsored-ads/1/view \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your-csrf-token"
```

#### تتبع النقرة
```bash
curl -X POST http://127.0.0.1:8000/api/sponsored-ads/1/click \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your-csrf-token"
```

#### الحصول على الإحصائيات
```bash
curl http://127.0.0.1:8000/api/sponsored-ads/1/stats
```

## استكشاف الأخطاء

### 1. الإعلانات الممولة لا تظهر
- تحقق من وجود إعلانات ممولة نشطة في قاعدة البيانات
- تأكد من أن `expires_at > now()`
- تحقق من أن `status = 'active'` و `is_active = true`

### 2. التتبع لا يعمل
- تحقق من CSRF token في الصفحة
- تأكد من تحميل JavaScript بشكل صحيح
- راجع console للأخطاء

### 3. الأداء بطيء
- تحقق من الفهارس في قاعدة البيانات
- راجع إعدادات الكاش
- قلل مدة الكاش إذا لزم الأمر

### 4. الإعلانات المنتهية لا تختفي
- تأكد من تشغيل المهام المجدولة
- شغل الأمر يدوياً: `php artisan sponsored-ads:cleanup`
- تحقق من logs للأخطاء

## إعدادات الإنتاج

### 1. الكاش
- استخدم Redis أو Memcached للكاش
- اضبط مدة الكاش حسب الحاجة

### 2. قاعدة البيانات
- تأكد من وجود الفهارس
- راقب أداء الاستعلامات
- فكر في تقسيم الجداول إذا كبرت

### 3. المراقبة
- راقب معدلات النقر
- راقب استخدام الكاش
- راقب أداء API endpoints

### 4. النسخ الاحتياطي
- اعمل نسخ احتياطي دورية
- احتفظ بإحصائيات الإعلانات الممولة

## الصيانة الدورية

### يومياً
- راجع logs للأخطاء
- تحقق من عمل المهام المجدولة

### أسبوعياً  
- راجع إحصائيات الأداء
- نظف logs القديمة

### شهرياً
- راجع استخدام قاعدة البيانات
- حدث الفهارس إذا لزم الأمر
- راجع إعدادات الكاش

## الدعم

إذا واجهت مشاكل:
1. راجع logs في `storage/logs/laravel.log`
2. تحقق من إعدادات قاعدة البيانات
3. تأكد من صحة الـ permissions
4. راجع إعدادات الـ web server
