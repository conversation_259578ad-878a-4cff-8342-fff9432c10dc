<?php

namespace App\Http\Controllers\representative;

use App\Contracts\Repositories\AdRepositoryInterface;
use App\Contracts\Repositories\AuthenticateRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\Ad\CreateAdRequest;
use App\Http\Requests\Ad\FilterAdRequest;
use App\Http\Requests\Ad\FilterUserAdsRequest;
use App\Http\Requests\Ad\ReportAdRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use App\Contracts\Repositories\CountryRepositoryInterface;
use App\Repositories\AdRepository;
use App\Models\Ad;
use Illuminate\Http\Request;
use App\Models\AdBrokerRequest;
use App\Models\Deals;
use App\Models\BrokerInterest;
use Illuminate\Support\Facades\DB;


class RepresentativeController extends Controller
{
     /**
     * Instantiate new controller instance
     */
    public function __construct(protected AdRepositoryInterface $adRepository,protected CountryRepositoryInterface $countryRepository, protected AuthenticateRepositoryInterface $authRepository)
    {}
    

        
    /**
     * Index page for listing ads.
     * 
     * @param \App\Http\Requests\Ad\FilterAdsRequest $query
     * @return \Illuminate\View\View
     */

     public function show(FilterAdRequest $query): View
     {
        $user = auth()->user();

        // بناء الاستعلام الأساسي للإعلانات التي تحتاج وساطة
        $adsQuery = Ad::where('needs_brokering', 1)
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->with(['user', 'category', 'country', 'state', 'city', 'media'])
            ->orderBy('created_at', 'desc');

        // تطبيق الفلاتر
        if ($query->filled('category')) {
            // البحث في الفئة المحددة وجميع الفئات الفرعية
            $category = \App\Models\Category::find($query->category);
            if ($category) {
                $categoryIds = $category->descendants()->pluck('id')->push($category->id);
                $adsQuery->whereIn('category_id', $categoryIds);
            } else {
                $adsQuery->where('category_id', $query->category);
            }
        }

        if ($query->filled('country')) {
            $adsQuery->where('country_id', $query->country);
        }

        if ($query->filled('state')) {
            $adsQuery->where('state_id', $query->state);
        }

        if ($query->filled('city')) {
            $adsQuery->where('city_id', $query->city);
        }

        if ($query->filled('min_price')) {
            $adsQuery->where('price', '>=', $query->min_price);
        }

        if ($query->filled('max_price')) {
            $adsQuery->where('price', '<=', $query->max_price);
        }

        if ($query->filled('search')) {
            $adsQuery->where(function($q) use ($query) {
                $q->where('title', 'like', '%' . $query->search . '%')
                  ->orWhere('description', 'like', '%' . $query->search . '%');
            });
        }

        // جلب الإعلانات مع التصفح
        $needs_brokering = $adsQuery->paginate(12);

        // إحصائيات للمندوب
        $myInterests = \App\Models\AdBrokerRequest::where('user_id', $user->id)->count();
        $myDeals = \App\Models\Deals::where('email', $user->email)->count();

        return view('ads.representative.index', [
            'needs_brokering' => $needs_brokering,
            'myInterests' => $myInterests,
            'myDeals' => $myDeals,
            'categories' => \App\Models\Category::whereIsRoot()->get(),
        ]);
    }
 
  
    public function brokeredAds(Request $request)
    {
        // التحقق من وجود المستخدم أولاً
        $user = auth()->user();
        if (!$user) {
            return back()->with('error', 'يجب تسجيل الدخول أولاً.');
        }

        // التحقق من أن المستخدم مندوب
        if (!$user->is_broker) {
            return back()->with('error', 'يجب أن تكون مندوباً لاستخدام هذه الخدمة.');
        }

        $found = Deals::where('unique_ad_code', $request->code)->first();
        $ad = Ad::where('id', $request->token)->first();

        if (!$ad) {
            return back()->with('error', 'الإعلان غير موجود.');
        }

        // التحقق من أن المستخدم ليس صاحب الإعلان
        if ($ad->user_id === $user->id) {
            return back()->with('error', 'لا يمكنك إدخال كود إعلانك الخاص.');
        }

        if ($ad->unique_ad_code != $request->code) {
            return back()->with('error', 'الكود غير صالح.');
        }
        elseif ($found != null) {
            return back()->with('error', 'تم تسجيل الكود من قبل');
        }
        elseif ($ad->unique_ad_code == $request->code) {
            Deals::create([
                'email' => $user->email,
                'unique_ad_code' => $ad->unique_ad_code,
                'ad_id' => $request->token,
            ]);
            return back()->with('success', 'تم تسجيل الكود بشكل صحيح.');
        }
        else {
            return back()->with('error', 'الكود غير صالح.');
        }
    }



        



    public function storeBrokerInterest(Request $request)
    {
        $request->validate([
            'ad_id' => 'required|uuid|exists:ads,id',
        ]);

        $user = auth()->user();
        if (!$user) {
            return back()->with('error', 'يجب تسجيل الدخول أولاً.');
        }

        if (!($user->is_broker ?? false)) {
            return back()->with('error', 'أنت مش مندوب!');
        }

        $ad = Ad::findOrFail($request->ad_id);

        // التحقق من أن المستخدم ليس صاحب الإعلان
        if ($ad->seller_email === $user->email) {
            return back()->with('error', 'لا يمكنك إظهار الاهتمام بإعلانك الخاص.');
        }

        // التحقق من أن الكود لم يتم استخدامه لهذا الإعلان من قبل
        if (BrokerInterest::isCodeUsedForAd($ad->id)) {
            return back()->with('error', 'تم استخدام كود هذا الإعلان من قبل مندوب آخر. لا يمكن إظهار الاهتمام بهذا الإعلان.');
        }

        // التحقق من عدم وجود اهتمام سابق
        if ($user->hasInterestInAd($ad->id)) {
            return back()->with('info', 'أنت بالفعل مهتم بهذا الإعلان.');
        }

        // حساب النقاط المطلوبة
        $requiredPoints = BrokerInterest::calculateRequiredPoints($ad->price);

        // التحقق من وجود نقاط كافية
        if (!$user->hasEnoughPoints($requiredPoints)) {
            return back()->with('error', "تحتاج إلى {$requiredPoints} نقطة لإظهار الاهتمام بهذا الإعلان. رصيدك الحالي: {$user->points} نقطة");
        }

        try {
            DB::beginTransaction();

            // خصم النقاط من المندوب
            $user->deductPoints($requiredPoints);

            // إنشاء الاهتمام
            BrokerInterest::create([
                'broker_id' => $user->id,
                'ad_id' => $ad->id,
                'points_paid' => $requiredPoints,
                'status' => 'active',
                'expires_at' => now()->addDays(10),
            ]);

            DB::commit();

            return back()->with('success', "تم تسجيل اهتمامك بالإعلان. تم خصم {$requiredPoints} نقطة من رصيدك.");

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'حدث خطأ أثناء تسجيل الاهتمام , حاول مرة أخرى , قد يكون اخطأ انك قمت بالاهتمام بهذا الاعلان مسبقا. ');
        }
    }

    public function enterBrokerCode(Request $request)
    {
        $request->validate([
            'ad_id' => 'required|uuid|exists:ads,id',
            'broker_code' => 'required|string|min:6|max:20',
        ]);

        $user = auth()->user();
        $ad = Ad::findOrFail($request->ad_id);

        // التحقق من أن المستخدم ليس صاحب الإعلان
        if ($ad->seller_email === $user->email) {
            return back()->with('error', 'لا يمكنك إدخال الكود لإعلانك الخاص.');
        }

        // التحقق من وجود اهتمام نشط في الجدول الجديد
        $brokerInterest = BrokerInterest::where('broker_id', $user->id)
                                       ->where('ad_id', $ad->id)
                                       ->where('status', 'active')
                                       ->first();

        if (!$brokerInterest) {
            return back()->with('error', 'يجب إظهار الاهتمام أولاً قبل إدخال الكود.');
        }

        // التحقق من صحة الكود
        if ($ad->unique_ad_code !== $request->broker_code) {
            return back()->with('error', 'كود المندوب غير صحيح.');
        }

        // التحقق من أن الكود لم يتم استخدامه لهذا الإعلان من قبل أي مندوب
        if (BrokerInterest::isCodeUsedForAd($ad->id)) {
            return back()->with('error', 'تم استخدام هذا الكود لهذا الإعلان من قبل مندوب آخر. لا يمكن استخدام الكود أكثر من مرة.');
        }

        // التحقق من أن الكود لم يتم إدخاله من قبل
        if ($brokerInterest->code_entered) {
            return back()->with('info', 'تم إدخال الكود من قبل.');
        }

        // التحقق من عدم وجود نقاط مكتسبة مسبقاً من هذا الإعلان
        $previousEarnings = BrokerInterest::where('broker_id', $user->id)
                                         ->where('ad_id', $ad->id)
                                         ->where('points_earned', '>', 0)
                                         ->exists();

        if ($previousEarnings) {
            return back()->with('error', 'لقد حصلت على نقاط من هذا الإعلان من قبل. لا يمكن الحصول على نقاط مضاعفة.');
        }

        try {
            DB::beginTransaction();

            // استخدام الاهتمام الذي تم العثور عليه مسبقاً

            // التحقق من انتهاء صلاحية الاهتمام (أكثر من 10 أيام)
            if ($brokerInterest->isExpired()) {
                $brokerInterest->update(['status' => 'expired']);
                DB::rollback();
                return back()->with('error', 'انتهت صلاحية الاهتمام بهذا الإعلان (أكثر من 10 أيام).');
            }

            // حساب النقاط المكتسبة بناءً على التوقيت
            $earnedPoints = $brokerInterest->calculateEarnedPoints();
            $daysSinceInterest = $brokerInterest->created_at->diffInDays(now());

            // تحديث الاهتمام في الجدول الجديد
            $brokerInterest->update([
                'code_entered' => true,
                'code_entered_at' => now(),
                'code_used_for_ad' => true,
                'points_earned' => $earnedPoints,
            ]);

            // إضافة النقاط للمندوب
            $user->addPoints($earnedPoints);

            // تحديث تاريخ آخر كود ناجح للمندوب (هذا ما يجدد فترة النشاط)
            $user->update([
                'last_successful_code_at' => now(),
            ]);

            // حذف الاهتمام من الجدول القديم
            AdBrokerRequest::where('user_id', $user->id)
                          ->where('ad_id', $ad->id)
                          ->delete();

            DB::commit();

            // رسالة النجاح مع تفاصيل النقاط
            $message = "تم إدخال الكود بنجاح! حصلت على {$earnedPoints} نقطة (خصم 0%).";

            return back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'حدث خطأ أثناء إدخال الكود. حاول مرة أخرى.');
        }
    }

    public function cancelBrokerInterest(Request $request)
    {
        $request->validate([
            'ad_id' => 'required|uuid|exists:ads,id',
        ]);

        $user = auth()->user();
        $ad = Ad::findOrFail($request->ad_id);

        // البحث عن الاهتمام في الجدول الجديد
        $brokerInterest = BrokerInterest::where('broker_id', $user->id)
                                       ->where('ad_id', $ad->id)
                                       ->where('status', 'active')
                                       ->first();

        if (!$brokerInterest) {
            return back()->with('error', 'لم يتم العثور على اهتمام نشط لإلغائه.');
        }

        // منع إلغاء الاهتمام بعد إدخال الكود
        if ($brokerInterest->code_entered) {
            return back()->with('error', 'لا يمكن إلغاء الاهتمام بعد إدخال الكود. لقد حصلت على النقاط بالفعل.');
        }

        try {
            DB::beginTransaction();

            // حساب النقاط المسترجعة
            $refundPoints = $brokerInterest->calculateCancelRefundPoints();
            $daysSinceInterest = $brokerInterest->created_at->diffInDays(now());

            // تحديث حالة الاهتمام
            $brokerInterest->update([
                'status' => 'cancelled',
                'points_earned' => $refundPoints,
            ]);

            // إضافة النقاط المسترجعة للمندوب
            $user->addPoints($refundPoints);

            // حذف الاهتمام من الجدول القديم
            AdBrokerRequest::where('user_id', $user->id)
                          ->where('ad_id', $ad->id)
                          ->delete();

            DB::commit();

            // رسالة النجاح مع تفاصيل الاسترداد
            $message = "تم إلغاء الاهتمام بنجاح! تم استرداد {$refundPoints} نقطة.";

            if ($daysSinceInterest <= 5) {
                $message .= " (استرداد كامل لأن الإلغاء تم خلال 5 أيام)";
            } else {
                $message .= " (خصم 20% لأن الإلغاء تم بعد 5 أيام)";
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'حدث خطأ أثناء إلغاء الاهتمام. حاول مرة أخرى.');
        }
    }
}