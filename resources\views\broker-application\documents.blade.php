@extends('partials.app')

@section('title', 'رفع المستندات - التقديم كمندوب')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-success text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        رفع المستندات المطلوبة
                    </h2>
                    <p class="mb-0 mt-2">المرحلة الثانية - رفع المستندات التأكيدية</p>
                </div>

                <div class="card-body p-5">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- معلومات الطلب -->
                    <div class="alert alert-info mb-4">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات طلبك
                        </h5>
                        <p class="mb-2"><strong>الاسم:</strong> {{ $application->full_name }}</p>
                        <p class="mb-2"><strong>تاريخ التقديم:</strong> {{ $application->created_at->format('Y-m-d H:i') }}</p>
                        <p class="mb-0"><strong>الحالة:</strong> 
                            <span class="badge bg-success">{{ $application->status_text }}</span>
                        </p>
                    </div>

                    <!-- نموذج رفع المستندات -->
                    <form method="POST" action="{{ route('broker-application.store-documents') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- الرقم القومي -->
                        <div class="mb-4">
                            <label for="national_id" class="form-label required">
                                <i class="fas fa-id-card me-2"></i>
                                الرقم القومي
                            </label>

                            @php
                                $isVerified = auth()->user()->national_id &&
                                             auth()->user()->identityVerification &&
                                             auth()->user()->identityVerification->status === 'approved';
                            @endphp

                            @if($isVerified)
                                <!-- المستخدم موثق - لا يمكن تغيير الرقم -->
                                <input type="text"
                                       class="form-control form-control-lg bg-light"
                                       id="national_id"
                                       name="national_id"
                                       value="{{ auth()->user()->national_id }}"
                                       readonly
                                       required>
                                <div class="text-success mt-2">
                                    <i class="fas fa-shield-check me-1"></i>
                                    <small><strong>هويتك موثقة مسبقاً بهذا الرقم القومي ولا يمكن تغييره</strong></small>
                                </div>
                                <div class="alert alert-info mt-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>
                                        نظراً لأن هويتك موثقة مسبقاً، سيتم استخدام نفس الرقم القومي المؤكد.
                                        هذا يضمن أمان حسابك ومنع التلاعب.
                                    </small>
                                </div>
                            @else
                                <!-- المستخدم غير موثق - يمكن إدخال الرقم -->
                                <input type="text"
                                       class="form-control form-control-lg @error('national_id') is-invalid @enderror"
                                       id="national_id"
                                       name="national_id"
                                       value="{{ old('national_id', auth()->user()->national_id) }}"
                                       placeholder="أدخل الرقم القومي"
                                       maxlength="20"
                                       required>
                                @error('national_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب أن يكون الرقم القومي مطابق للمستندات المرفوعة
                                </div>
                            @endif
                        </div>
                        
                        <div class="row">
                            <!-- شهادة التعليم -->
                            <div class="col-12 mb-4">
                                <label for="education_certificate" class="form-label fw-bold text-danger">
                                    <i class="fas fa-graduation-cap me-2 text-primary"></i>
                                    شهادة التعليم * (مطلوبة)
                                </label>
                                <input type="file" 
                                       class="form-control @error('education_certificate') is-invalid @enderror" 
                                       id="education_certificate" 
                                       name="education_certificate" 
                                       accept=".pdf,.jpg,.jpeg,.png"
                                       required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب رفع صورة واضحة من شهادة التعليم (PDF, JPG, PNG - حد أقصى 5MB)
                                </div>
                                @error('education_certificate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- إثبات الخبرة -->
                            <div class="col-12 mb-4">
                                <label for="experience_proof" class="form-label fw-bold">
                                    <i class="fas fa-briefcase me-2 text-primary"></i>
                                    إثبات الخبرة (اختياري)
                                </label>
                                <input type="file" 
                                       class="form-control @error('experience_proof') is-invalid @enderror" 
                                       id="experience_proof" 
                                       name="experience_proof" 
                                       accept=".pdf,.jpg,.jpeg,.png">
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    شهادات خبرة، توصيات، أو أي مستندات تثبت خبرتك السابقة
                                </div>
                                @error('experience_proof')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- البطاقة الشخصية -->
                            <div class="col-12 mb-4">
                                <label for="id_card" class="form-label fw-bold text-danger">
                                    <i class="fas fa-id-card me-2 text-primary"></i>
                                    البطاقة الشخصية * (مطلوبة)
                                </label>
                                <input type="file" 
                                       class="form-control @error('id_card') is-invalid @enderror" 
                                       id="id_card" 
                                       name="id_card" 
                                       accept=".pdf,.jpg,.jpeg,.png"
                                       required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    صورة واضحة من البطاقة الشخصية (الوجهين)
                                </div>
                                @error('id_card')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- مستندات إضافية -->
                            <div class="col-12 mb-4">
                                <label for="additional_documents" class="form-label fw-bold">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>
                                    مستندات إضافية (اختياري)
                                </label>
                                <input type="file" 
                                       class="form-control @error('additional_documents.*') is-invalid @enderror" 
                                       id="additional_documents" 
                                       name="additional_documents[]" 
                                       accept=".pdf,.jpg,.jpeg,.png"
                                       multiple>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يمكنك رفع مستندات إضافية تدعم طلبك (يمكن اختيار عدة ملفات)
                                </div>
                                @error('additional_documents.*')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg px-5">
                                <i class="fas fa-upload me-2"></i>
                                رفع المستندات
                            </button>
                        </div>
                    </form>

                    <!-- تعليمات مهمة -->
                    <div class="mt-5 pt-4 border-top">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تعليمات مهمة
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        تأكد من وضوح جميع المستندات
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        الحد الأقصى لحجم الملف 5MB
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        الصيغ المقبولة: PDF, JPG, PNG
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        المراجعة النهائية تستغرق 2-3 أيام
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
