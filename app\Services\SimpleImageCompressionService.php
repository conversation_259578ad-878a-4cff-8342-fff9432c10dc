<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class SimpleImageCompressionService
{
    /**
     * Compress and store image using basic PHP functions
     */
    public function compressAndStore(UploadedFile $file, string $directory, array $options = []): array
    {
        // Default options
        $defaultOptions = [
            'quality' => 75,
            'max_width' => 1200,
            'max_height' => 1200,
            'format' => 'jpg', // Use JPG as it's more widely supported
        ];

        $options = array_merge($defaultOptions, $options);

        // Generate unique filename
        $uuid = Str::uuid();
        $extension = $options['format'] === 'webp' ? 'jpg' : $options['format']; // Fallback to JPG
        $filename = $uuid . '.' . $extension;
        $path = $directory . '/' . $filename;

        // Get original file size
        $originalSize = $file->getSize();

        try {
            // Get image info
            $imageInfo = getimagesize($file->getRealPath());
            if ($imageInfo === false) {
                return [
                    'success' => false,
                    'error' => 'Invalid image file'
                ];
            }

            [$originalWidth, $originalHeight, $imageType] = $imageInfo;

            // Create image resource based on type
            $sourceImage = $this->createImageFromFile($file->getRealPath(), $imageType);
            if ($sourceImage === false) {
                return [
                    'success' => false,
                    'error' => 'Could not create image resource'
                ];
            }

            // Calculate new dimensions
            $newDimensions = $this->calculateNewDimensions(
                $originalWidth, 
                $originalHeight, 
                $options['max_width'], 
                $options['max_height']
            );

            // Create new image with new dimensions
            $newImage = imagecreatetruecolor($newDimensions['width'], $newDimensions['height']);
            
            // Preserve transparency for PNG
            if ($imageType === IMAGETYPE_PNG) {
                imagealphablending($newImage, false);
                imagesavealpha($newImage, true);
                $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                imagefill($newImage, 0, 0, $transparent);
            }

            // Resize image
            imagecopyresampled(
                $newImage, $sourceImage,
                0, 0, 0, 0,
                $newDimensions['width'], $newDimensions['height'],
                $originalWidth, $originalHeight
            );

            // Create temporary file
            $tempPath = tempnam(sys_get_temp_dir(), 'compressed_');
            
            // Save compressed image
            $success = false;
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    $success = imagejpeg($newImage, $tempPath, $options['quality']);
                    break;
                case 'png':
                    // PNG quality is 0-9, convert from 0-100
                    $pngQuality = (int) ((100 - $options['quality']) / 10);
                    $success = imagepng($newImage, $tempPath, $pngQuality);
                    break;
                case 'gif':
                    $success = imagegif($newImage, $tempPath);
                    break;
            }

            // Clean up memory
            imagedestroy($sourceImage);
            imagedestroy($newImage);

            if (!$success) {
                return [
                    'success' => false,
                    'error' => 'Failed to save compressed image'
                ];
            }

            // Store the compressed image
            $compressedContent = file_get_contents($tempPath);
            Storage::disk('public')->put($path, $compressedContent);

            // Get compressed file size
            $compressedSize = strlen($compressedContent);
            $compressionRatio = round((($originalSize - $compressedSize) / $originalSize) * 100, 2);

            // Clean up temp file
            unlink($tempPath);

            // Log compression results
            Log::info('Image compressed successfully (Simple)', [
                'original_filename' => $file->getClientOriginalName(),
                'new_filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio . '%',
                'format' => $extension,
                'quality' => $options['quality'],
                'dimensions' => $newDimensions['width'] . 'x' . $newDimensions['height']
            ]);

            return [
                'success' => true,
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'format' => $extension,
                'dimensions' => [
                    'width' => $newDimensions['width'],
                    'height' => $newDimensions['height']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Simple image compression failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create image resource from file
     */
    private function createImageFromFile(string $filePath, int $imageType)
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($filePath);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($filePath);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($filePath);
            case IMAGETYPE_WEBP:
                return function_exists('imagecreatefromwebp') ? imagecreatefromwebp($filePath) : false;
            default:
                return false;
        }
    }

    /**
     * Calculate new dimensions maintaining aspect ratio
     */
    private function calculateNewDimensions(int $originalWidth, int $originalHeight, int $maxWidth, int $maxHeight): array
    {
        // If image is smaller than max dimensions, keep original size
        if ($originalWidth <= $maxWidth && $originalHeight <= $maxHeight) {
            return [
                'width' => $originalWidth,
                'height' => $originalHeight
            ];
        }

        // Calculate aspect ratio
        $aspectRatio = $originalWidth / $originalHeight;

        // Calculate new dimensions
        if ($originalWidth > $originalHeight) {
            $newWidth = $maxWidth;
            $newHeight = (int) ($maxWidth / $aspectRatio);
            
            if ($newHeight > $maxHeight) {
                $newHeight = $maxHeight;
                $newWidth = (int) ($maxHeight * $aspectRatio);
            }
        } else {
            $newHeight = $maxHeight;
            $newWidth = (int) ($maxHeight * $aspectRatio);
            
            if ($newWidth > $maxWidth) {
                $newWidth = $maxWidth;
                $newHeight = (int) ($maxWidth / $aspectRatio);
            }
        }

        return [
            'width' => $newWidth,
            'height' => $newHeight
        ];
    }

    /**
     * Get optimized settings for different image types
     */
    public function getOptimizedSettings(string $type): array
    {
        return match ($type) {
            'ad_images' => [
                'quality' => 80,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            'profile_avatar' => [
                'quality' => 85,
                'max_width' => 400,
                'max_height' => 400,
                'format' => 'jpg'
            ],
            'identity_documents' => [
                'quality' => 90,
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            'broker_documents' => [
                'quality' => 90,
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            default => [
                'quality' => 75,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg'
            ]
        };
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, int $maxSizeMB = 10): array
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
            ];
        }

        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'error' => "File size exceeds {$maxSizeMB}MB limit."
            ];
        }

        return ['valid' => true];
    }

    /**
     * Format file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
