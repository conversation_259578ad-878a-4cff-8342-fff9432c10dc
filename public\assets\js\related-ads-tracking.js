/**
 * Related Ads Tracking System
 * Advanced tracking for related ads with sponsored ads integration
 */

class RelatedAdsTracker {
    constructor() {
        this.viewedAds = new Set();
        this.clickedAds = new Set();
        this.observer = null;
        this.apiEndpoints = {
            trackView: '/api/sponsored-ads/track-view',
            trackClick: '/api/sponsored-ads/track-click'
        };
        this.init();
    }

    /**
     * Initialize the tracking system
     */
    init() {
        this.setupIntersectionObserver();
        this.setupClickTracking();
        this.setupErrorHandling();
        console.log('Related Ads Tracker initialized');
    }

    /**
     * Setup intersection observer for view tracking
     */
    setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) {
            console.warn('IntersectionObserver not supported, falling back to scroll tracking');
            this.setupScrollTracking();
            return;
        }

        const options = {
            threshold: 0.6, // 60% of the ad must be visible
            rootMargin: '0px 0px -50px 0px'
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.handleAdView(entry.target);
                }
            });
        }, options);

        // Start observing all related ad cards
        this.observeRelatedAds();
    }

    /**
     * Observe all related ad cards
     */
    observeRelatedAds() {
        const relatedAdCards = document.querySelectorAll('.related-ad-card');
        relatedAdCards.forEach(card => {
            if (card.getAttribute('data-ad-id')) {
                this.observer.observe(card);
            }
        });
    }

    /**
     * Handle ad view tracking
     */
    handleAdView(adCard) {
        const adId = adCard.getAttribute('data-ad-id');
        
        if (!adId || this.viewedAds.has(adId)) {
            return;
        }

        this.viewedAds.add(adId);
        
        const isSponsored = adCard.querySelector('.sponsored-badge') !== null;
        const adType = isSponsored ? 'sponsored' : 'regular';
        
        // Add visual feedback
        this.addViewedIndicator(adCard);
        
        // Track the view
        this.trackView(adId, adType);
        
        // Stop observing this ad
        if (this.observer) {
            this.observer.unobserve(adCard);
        }
    }

    /**
     * Add visual indicator for viewed ads
     */
    addViewedIndicator(adCard) {
        adCard.classList.add('viewed');
        
        // Add a subtle animation
        adCard.style.transform = 'scale(1.02)';
        setTimeout(() => {
            adCard.style.transform = '';
        }, 200);
    }

    /**
     * Setup click tracking
     */
    setupClickTracking() {
        document.addEventListener('click', (event) => {
            const adCard = event.target.closest('.related-ad-card');
            const viewButton = event.target.closest('.view-ad-btn');
            
            if (adCard && viewButton) {
                this.handleAdClick(adCard, event);
            }
        });
    }

    /**
     * Handle ad click tracking
     */
    handleAdClick(adCard, event) {
        const adId = adCard.getAttribute('data-ad-id');
        
        if (!adId) {
            return;
        }

        const isSponsored = adCard.querySelector('.sponsored-badge') !== null;
        const adType = isSponsored ? 'sponsored' : 'regular';
        
        // Add loading state
        this.addLoadingState(adCard);
        
        // Track the click
        this.trackClick(adId, adType);
        
        // Add to clicked ads set
        this.clickedAds.add(adId);
        
        // Enhanced analytics
        this.trackAdvancedMetrics(adCard, adType);
    }

    /**
     * Add loading state to ad card
     */
    addLoadingState(adCard) {
        adCard.classList.add('loading');
        
        // Remove loading state after a short delay
        setTimeout(() => {
            adCard.classList.remove('loading');
        }, 1000);
    }

    /**
     * Track view via API
     */
    async trackView(adId, adType) {
        try {
            const response = await fetch(this.apiEndpoints.trackView, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.getCSRFToken()
                },
                body: JSON.stringify({
                    ad_id: adId,
                    ad_type: adType,
                    source: 'related_ads',
                    current_ad_id: this.getCurrentAdId(),
                    timestamp: Date.now(),
                    viewport: this.getViewportInfo(),
                    user_agent: navigator.userAgent
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            console.log(`View tracked for ad ${adId} (${adType})`);
        } catch (error) {
            console.error('Error tracking view:', error);
            this.handleTrackingError('view', adId, error);
        }
    }

    /**
     * Track click via API
     */
    async trackClick(adId, adType) {
        try {
            const response = await fetch(this.apiEndpoints.trackClick, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.getCSRFToken()
                },
                body: JSON.stringify({
                    ad_id: adId,
                    ad_type: adType,
                    source: 'related_ads',
                    current_ad_id: this.getCurrentAdId(),
                    timestamp: Date.now(),
                    viewport: this.getViewportInfo(),
                    referrer: document.referrer
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            console.log(`Click tracked for ad ${adId} (${adType})`);
        } catch (error) {
            console.error('Error tracking click:', error);
            this.handleTrackingError('click', adId, error);
        }
    }

    /**
     * Track advanced metrics
     */
    trackAdvancedMetrics(adCard, adType) {
        const metrics = {
            scrollPosition: window.pageYOffset,
            timeOnPage: Date.now() - this.pageLoadTime,
            adPosition: this.getAdPosition(adCard),
            deviceType: this.getDeviceType(),
            connectionType: this.getConnectionType()
        };

        // Store metrics for later analysis
        this.storeMetrics(adCard.getAttribute('data-ad-id'), metrics);
    }

    /**
     * Get ad position in the list
     */
    getAdPosition(adCard) {
        const allCards = document.querySelectorAll('.related-ad-card');
        return Array.from(allCards).indexOf(adCard) + 1;
    }

    /**
     * Get device type
     */
    getDeviceType() {
        const width = window.innerWidth;
        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    }

    /**
     * Get connection type
     */
    getConnectionType() {
        if ('connection' in navigator) {
            return navigator.connection.effectiveType || 'unknown';
        }
        return 'unknown';
    }

    /**
     * Get viewport information
     */
    getViewportInfo() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollY: window.pageYOffset
        };
    }

    /**
     * Get CSRF token
     */
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    /**
     * Get current ad ID
     */
    getCurrentAdId() {
        // Extract from URL or page data
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 1] || '';
    }

    /**
     * Store metrics locally
     */
    storeMetrics(adId, metrics) {
        const key = `ad_metrics_${adId}`;
        localStorage.setItem(key, JSON.stringify(metrics));
    }

    /**
     * Setup error handling
     */
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            if (event.filename && event.filename.includes('related-ads')) {
                console.error('Related Ads Tracker Error:', event.error);
            }
        });
    }

    /**
     * Handle tracking errors
     */
    handleTrackingError(type, adId, error) {
        // Store failed tracking attempts for retry
        const failedTracking = JSON.parse(localStorage.getItem('failed_tracking') || '[]');
        failedTracking.push({
            type,
            adId,
            error: error.message,
            timestamp: Date.now()
        });
        localStorage.setItem('failed_tracking', JSON.stringify(failedTracking));
    }

    /**
     * Fallback scroll tracking for older browsers
     */
    setupScrollTracking() {
        let ticking = false;
        
        const handleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.checkVisibleAds();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', handleScroll);
    }

    /**
     * Check which ads are visible (fallback method)
     */
    checkVisibleAds() {
        const relatedAdCards = document.querySelectorAll('.related-ad-card');
        
        relatedAdCards.forEach(card => {
            if (this.isElementVisible(card)) {
                this.handleAdView(card);
            }
        });
    }

    /**
     * Check if element is visible (fallback method)
     */
    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        
        return rect.top < windowHeight && rect.bottom > 0;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.relatedAdsTracker = new RelatedAdsTracker();
});

// Global function for manual click tracking (backward compatibility)
window.trackRelatedAdClick = function(adId, adType) {
    if (window.relatedAdsTracker) {
        window.relatedAdsTracker.trackClick(adId, adType);
    }
};
