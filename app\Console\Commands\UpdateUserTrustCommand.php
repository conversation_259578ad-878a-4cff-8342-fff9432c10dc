<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class UpdateUserTrustCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:update-trust {user_id} {--trusted=true}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update user trust status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $trusted = $this->option('trusted') === 'true';

        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID {$userId} not found!");
            return;
        }

        $user->is_trusted = $trusted;
        $user->save();

        $status = $trusted ? 'TRUSTED' : 'NOT TRUSTED';
        $this->info("User {$user->name} ({$user->email}) is now: {$status}");
    }
}
