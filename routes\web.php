<?php


use App\Http\Controllers\Page\ContactController;
use App\Http\Controllers\Page\HomeController;
use App\Http\Controllers\User\Ad\AdController;
use App\Http\Controllers\User\RatingController;
use App\Http\Controllers\ProductReviewController;
use App\Http\Controllers\representative\RepresentativeController;
use App\Http\Controllers\User\Bid\BidController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Page\BlogController;
use App\Http\Controllers\Page\CommentController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::post('/users/{user}/rate', [RatingController::class, 'rateUser'])->middleware('auth')->name('rate-user');

// Product Reviews Routes
Route::post('/products/{ad}/review', [ProductReviewController::class, 'store'])->middleware('auth')->name('product.review.store');
Route::get('/api/products/{ad}/reviews', [ProductReviewController::class, 'getReviews'])->name('product.reviews.api');

// Page Routes
Route::post('/broker-interest', [RepresentativeController::class, 'storeBrokerInterest'])->middleware(['auth', 'ensure.broker', 'ensure.active.broker'])->name('representative.store-broker-interest');
Route::post('/brokeredAds', [RepresentativeController::class, 'brokeredAds'])->name('brokeredAds');
Route::get('/representative', [RepresentativeController::class, 'show'])->middleware(['ensure.broker', 'ensure.active.broker'])->name('representative');

// Broker Code Route
Route::middleware(['auth', 'ensure.broker', 'ensure.active.broker'])->group(function () {
    Route::post('/broker/enter-code', [RepresentativeController::class, 'enterBrokerCode'])->name('broker.enter-code');
Route::post('/broker/cancel-interest', [RepresentativeController::class, 'cancelBrokerInterest'])->name('broker.cancel-interest');
});

Route::get(uri: '/', action: HomeController::class)->name('home');
Route::view('/about', 'pages.about.index')->name('about');
Route::view('/contact', 'pages.contact.index')->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::view('/how-it-works', 'pages.how-it-works.index')->name('how-it-works');
Route::post('/bid/{ads:slug}', [BidController::class, 'bid'])->name('bid.handle')->middleware(['auth:web', 'check.warnings:bidding_ban']);
Route::webhooks('paystack-webhook', 'paystack-webhook');
Route::get('/live-auction', [AdController::class, 'index'])->name('live-auction');
Route::get('/api/subcategories/{category}', [AdController::class, 'getSubcategories'])->name('api.subcategories');
Route::get('/api/attributes/{category}', [AdController::class, 'getAttributes'])->name('api.attributes');
Route::get('/blog', [BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{post:slug}', [BlogController::class, 'show'])->name('blog.show');
Route::post('/blog/{post:slug}/comment', [CommentController::class, 'store'])->middleware('check.warnings:comment_ban')->name('blog.comment.store');
Route::get('/auction-details/{ads:slug}', [AdController::class, 'show'])->name('auction-details')->middleware('increase.ad.views');

// Related Ads AJAX Routes
Route::post('/related-ads/load-more', [\App\Http\Controllers\Api\RelatedAdsController::class, 'loadMore'])->name('related-ads.load-more');
Route::post('/related-ads/search', [\App\Http\Controllers\Api\RelatedAdsController::class, 'search'])->name('related-ads.search');

Route::get('/affiliate', [AdController::class, 'affiliate'])->name('affiliate');
Route::get('/ad_code', [AdController::class, 'ad_code'])->name('ad_code');
Route::get('/user-profile/{username}', [AdController::class, 'userProfile'])->name('public.user.profile');


Route::middleware(['auth', 'ensure.email.verified'])->group(function () {
    Route::get('/add-listing', [AdController::class, 'create'])->middleware('check.warnings:posting_ban')->name('add-listing');
    Route::post('/add-listing', [AdController::class, 'store'])->middleware('check.warnings:posting_ban')->name('add-listing.handle');
});


// Laravel Email Verification Aliases (for compatibility)
use Illuminate\Foundation\Auth\EmailVerificationRequest;

Route::get('/verify-email/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();
    return redirect()->route('user.dashboard')->with('success', 'Your email has been verified successfully!');
})->middleware(['auth', 'signed'])->name('verification.verify');

// صفحة تقييم المستخدم
Route::get('/users/{id}/rate', [App\Http\Controllers\User\RatingController::class, 'showRatePage'])->middleware('auth')->name('users.rate');

// Report routes
Route::middleware('auth')->group(function () {
    Route::post('/reports', [App\Http\Controllers\ReportController::class, 'store'])->name('reports.store');
    Route::get('/reports/modal', [App\Http\Controllers\ReportController::class, 'getModal'])->name('reports.modal');
});

// Chat routes
Route::middleware('auth')->group(function () {
    Route::post('/chat/block-user', [App\Http\Controllers\ChatController::class, 'blockUser'])->name('chat.block');
    Route::post('/chat/unblock-user', [App\Http\Controllers\ChatController::class, 'unblockUser'])->name('chat.unblock');
    Route::post('/chat/report', [App\Http\Controllers\ChatController::class, 'reportMessage'])->name('chat.report');
    Route::post('/chat/check-block', [App\Http\Controllers\ChatController::class, 'checkBlock'])->name('chat.check-block');
});

// طلبات التقديم للمندوبين
Route::middleware(['auth'])->group(function () {
    Route::get('/broker-application', [App\Http\Controllers\BrokerApplicationController::class, 'create'])->name('broker-application.create');
    Route::post('/broker-application', [App\Http\Controllers\BrokerApplicationController::class, 'store'])->name('broker-application.store');
    Route::get('/broker-application/documents', [App\Http\Controllers\BrokerApplicationController::class, 'documents'])->name('broker-application.documents');
    Route::post('/broker-application/documents', [App\Http\Controllers\BrokerApplicationController::class, 'storeDocuments'])->name('broker-application.store-documents');
});

// توثيق الهوية
Route::middleware(['auth'])->group(function () {
    Route::get('/identity-verification', [App\Http\Controllers\IdentityVerificationController::class, 'create'])->name('identity-verification.create');
    Route::post('/identity-verification', [App\Http\Controllers\IdentityVerificationController::class, 'store'])->name('identity-verification.store');
});