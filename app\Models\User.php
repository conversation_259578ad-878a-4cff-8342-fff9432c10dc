<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Enums\Gender;
use App\Traits\HasAvatar;
use App\Traits\HasNameSplit;
use App\Traits\HasUuids;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Lab404\Impersonate\Models\Impersonate;



use Illuminate\Database\Eloquent\Model;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids, HasAvatar, HasNameSplit, Impersonate;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_changed_at',
        'email',
        'national_id',
        'username',
        'mobile',
        'gender',
        'email_verification_token',
        'email_verified_at',
        'password',
        'address',
        'zip_code',
        'avatar',
        'country_id',
        'state_id',
        'city_id',
        'timezone_id',
        'is_active',
        'rank',
        'is_trusted',
        'Number_Ads',
        'is_broker',
        'points',
        'google_id',
        'last_successful_code_at',
        'broker_activated_at',
    ];
    
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'name_changed_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'gender' => Gender::class,
        'last_successful_code_at' => 'datetime',
        'broker_activated_at' => 'datetime',
    ];

    /**
     * Username Attribute.
     * 
     * @return Attribute
     */
    public function username(): Attribute
    {
        return Attribute::make(
            fn ($value) => strtolower($value),
            fn ($value) => strtolower($value)
        );
    }

    /**
     * Scope a query to only include active users.
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include inactive users.
     * 
     * @param \Illuminate\Database\Eloquent\Builder $query
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('is_active', false);
    }

    /**
     * Get the ads for the user.
     */
    public function ads(): HasMany
    {
        return $this->hasMany(Ad::class);
    }



    /**
     * Get the bids for the user.
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class);
    }

    /**
     * Get the payments the user paid
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'payer_id');
    }

    /**
     * Get the payments the user received
     */
    public function receivedPayments(): HasMany
    {
        return $this->hasMany(Payment::class, 'payee_id');
    }

    /**
     * Get payouts for the user.
     * 
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payouts(): HasMany
    {
        return $this->hasMany(Payout::class, 'user_id');
    }

    /**
     * Get the payout methods for the user.
     */
    public function payoutMethods(): HasMany
    {
        return $this->hasMany(PayoutMethod::class);
    }

    /**
     * Get the accepted bids for the user.
     */
    public function acceptedBids(): HasMany
    {
        return $this->hasMany(Bid::class)->where('is_accepted', true);
    }

    /**
     * Get the country the user belongs to.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the country the user belongs to.
     */
    public function timezone(): BelongsTo
    {
        return $this->belongsTo(Timezone::class);
    }


    /**
     * Get the state the user belongs to.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the city the user belongs to.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }
    public function brokerProfile()
{
    return $this->hasOne(BrokerProfile::class, 'user_id');
}

    /**
     * Get the broker requests made by this user.
     */
    public function adBrokerRequests(): HasMany
    {
        return $this->hasMany(AdBrokerRequest::class, 'user_id');
    }

    /**
     * Get the count of published ads for this user.
     */
    public function getPublishedAdsCountAttribute(): int
    {
        return $this->ads()->where('status', \App\Enums\AdStatus::PUBLISHED)->count();
    }

    /**
     * Recalculate and update the Number_Ads count.
     */
    public function recalculateAdsCount(): void
    {
        $this->update(['Number_Ads' => $this->published_ads_count]);
    }

    /**
     * Get user level based on number of published ads (1-50).
     */
    public function getLevelAttribute(): int
    {
        return min(floor($this->Number_Ads / 20) + 1, 50);
    }

    /**
     * Get user rating (1-5 stars) from received ratings.
     */
    public function getRatingAttribute(): float
    {
        return $this->average_rating ?? 0.0;
    }

    /**
     * Check if user can change their name.
     * Trusted users cannot change their name.
     * Regular users can change their name every 15 days.
     */
    public function canChangeName(): bool
    {
        // Trusted users cannot change their name
        if ($this->is_trusted) {
            return false;
        }

        // If name was never changed, allow change
        if (!$this->name_changed_at) {
            return true;
        }

        // Check if 15 days have passed since last name change
        return $this->name_changed_at->addDays(15)->isPast();
    }

    /**
     * Get days remaining until user can change name again.
     */
    public function getDaysUntilNameChangeAttribute(): int
    {
        if ($this->is_trusted || !$this->name_changed_at) {
            return 0;
        }

        $nextChangeDate = $this->name_changed_at->addDays(15);
        return $nextChangeDate->isFuture() ? now()->diffInDays($nextChangeDate, false) : 0;
    }

    /**
     * Update user name and set name_changed_at timestamp.
     */
    public function updateName(string $newName): bool
    {
        if (!$this->canChangeName()) {
            return false;
        }

        return $this->update([
            'name' => $newName,
            'name_changed_at' => now(),
        ]);
    }



    /**
     * Get formatted level display.
     */
    public function getFormattedLevelAttribute(): string
    {
        return "Level {$this->level}";
    }

    /**
     * Get formatted rating display with stars.
     */
    public function getFormattedRatingWithStarsAttribute(): string
    {
        return number_format($this->rating, 1) . " ★";
    }

    /**
     * Get the ratings given by this user.
     */
    public function givenRatings(): HasMany
    {
        return $this->hasMany(UserRating::class, 'rater_id');
    }

    /**
     * Get the ratings received by this user.
     */
    public function receivedRatings(): HasMany
    {
        return $this->hasMany(UserRating::class, 'rated_id');
    }

    /**
     * Check if user can rate another user.
     */
    public function canRate($userId): bool
    {
        if (!$this->is_trusted) {
            return false;
        }
        if ($this->id === $userId) {
            return false;
        }
        return !$this->givenRatings()->where('rated_id', $userId)->exists();
    }

    /**
     * Get the product reviews given by this user.
     */
    public function productReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class, 'reviewer_id');
    }

    /**
     * Get user warnings.
     */
    public function warnings(): HasMany
    {
        return $this->hasMany(UserWarning::class);
    }

    /**
     * Get active warnings.
     */
    public function activeWarnings(): HasMany
    {
        return $this->hasMany(UserWarning::class)->active();
    }

    /**
     * Check if user has active warning of specific type.
     */
    public function hasActiveWarning(string $type): bool
    {
        return $this->activeWarnings()->ofType($type)->exists();
    }

    /**
     * Get active warning of specific type.
     */
    public function getActiveWarning(string $type): ?UserWarning
    {
        return $this->activeWarnings()->ofType($type)->first();
    }

    /**
     * Check if user can review a product.
     */
    public function canReviewProduct($adId): bool
    {
        if (!$this->is_trusted) {
            return false;
        }

        // البائع لا يمكنه تقييم منتجه
        $ad = Ad::find($adId);
        if ($ad && $ad->user_id === $this->id) {
            return false;
        }

        // التحقق من عدم وجود تقييم سابق
        return !$this->productReviews()->where('ad_id', $adId)->exists();
    }

    /**
     * Get the average rating for this user.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->receivedRatings()->avg('rating') ?? 0.0;
    }

    /**
     * Get the total number of ratings for this user.
     */
    public function getTotalRatingsAttribute(): int
    {
        return $this->receivedRatings()->count();
    }

    /**
     * Get the formatted rating with stars.
     */
    public function getFormattedRatingAttribute(): string
    {
        return number_format($this->rating, 1);
    }

    /**
     * Get the rating as stars (1-5).
     */
    public function getStarsAttribute(): int
    {
        return round($this->rating);
    }

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new \App\Notifications\CustomVerifyEmail);
    }

    /**
     * Get broker interests for this user
     */
    public function brokerInterests(): HasMany
    {
        return $this->hasMany(BrokerInterest::class, 'broker_id');
    }

    /**
     * Get broker application for this user
     */
    public function brokerApplication(): HasOne
    {
        return $this->hasOne(BrokerApplication::class, 'user_id');
    }

    /**
     * Get identity verification for this user
     */
    public function identityVerification(): HasOne
    {
        return $this->hasOne(IdentityVerification::class, 'user_id');
    }

    /**
     * Get active broker interests
     */
    public function activeBrokerInterests(): HasMany
    {
        return $this->brokerInterests()->where('status', 'active');
    }

    /**
     * Check if user has enough points
     */
    public function hasEnoughPoints(int $requiredPoints): bool
    {
        return $this->points >= $requiredPoints;
    }

    /**
     * Deduct points from user
     */
    public function deductPoints(int $points): bool
    {
        if ($this->hasEnoughPoints($points)) {
            $this->decrement('points', $points);
            return true;
        }
        return false;
    }

    /**
     * Add points to user
     */
    public function addPoints(int $points): void
    {
        $this->increment('points', $points);
    }

    /**
     * Check if user has interest in specific ad
     */
    public function hasInterestInAd(string $adId): bool
    {
        return $this->brokerInterests()
                   ->where('ad_id', $adId)
                   ->where('status', 'active')
                   ->exists();
    }

    /**
     * Get interest in specific ad
     */
    public function getInterestInAd(string $adId): ?BrokerInterest
    {
        return $this->brokerInterests()
                   ->where('ad_id', $adId)
                   ->where('status', 'active')
                   ->first();
    }


}
