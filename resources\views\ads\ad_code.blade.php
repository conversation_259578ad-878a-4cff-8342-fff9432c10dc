@extends('partials.app')
@section('title', 'your AD')
@section('description', 'View your AD')
@section('content')

@include('layouts.breadcrumb', ['admin' => false, 'pageTitle' => 'Your Ads'])

<div class="live-auction-section pt-120 pb-120">
    <div class="container">
        <div class="row gy-4 mb-60 d-flex justify-content-center">
            @forelse ($ads as $ad)
                <div> 
                    <x-ad-item-card :ad="$ad" type="classic"/>
                    your code : {{$ad->unique_ad_code}}
                    <br>
                    <br>
                    <br>
                </div>
            @empty
                <div class="d-flex flex-column align-items-center justify-content-center">
                    <div class="text-center mb-4">
                        <img src="{{ asset('assets/images/icons/man.svg') }}" alt="empty" class="w-25">
                    </div>
                    <x-alert type="warning">
                        <p class="text-center mb-0"><strong>Sorry!</strong> No ads found.</p>
                    </x-alert>
                </div>
            @endforelse
        </div>
    </div>
</div>

@if (auth()->check() && auth()->user()->is_broker)
    

<H4>اعلانات التي اظهرت اهتمامك بها كمندوب</H4>
    <div class="container">
        <div class="row gy-4 mb-60 d-flex justify-content-center">
            @forelse ($ads_request as $ad)
                <div> 
                    <x-ad-item-card :ad="$ad" type="classic"/>
                    <br>
                    <br>
                    <br>
                </div>
            @empty
                <div class="d-flex flex-column align-items-center justify-content-center">
                    <div class="text-center mb-4">
                        <img src="{{ asset('assets/images/icons/man.svg') }}" alt="empty" class="w-25">
                    </div>
                    <x-alert type="warning">
                        <p class="text-center mb-0"><strong>Sorry!</strong> No ads found.</p>
                    </x-alert>
                </div>
            @endforelse
        </div>
    </div>

<H4>اعلانات والصفقات التي اتممتها </H4>
    <div class="container">
        <div class="row gy-4 mb-60 d-flex justify-content-center">
            @forelse ($deals_ads as $ad)
                <div> 
                    <x-ad-item-card :ad="$ad" type="classic"/>
                    <br>
                    <br>
                    <br>
                </div>
            @empty
                <div class="d-flex flex-column align-items-center justify-content-center">
                    <div class="text-center mb-4">
                        <img src="{{ asset('assets/images/icons/man.svg') }}" alt="empty" class="w-25">
                    </div>
                    <x-alert type="warning">
                        <p class="text-center mb-0"><strong>Sorry!</strong> No ads found.</p>
                    </x-alert>
                </div>
            @endforelse
        </div>
    </div>
@endif


        </div>
    </div>
</div>

@endsection