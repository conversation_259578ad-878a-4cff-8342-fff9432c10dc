<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الشات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار وظائف الشات</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار زر الحظر</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-danger" onclick="testBlockUser()">
                            <i class="fas fa-ban me-2"></i>اختبار حظر المستخدم
                        </button>
                        <div id="blockResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>اختبار الإبلاغ</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-warning" onclick="testReportMessage()">
                            <i class="fas fa-flag me-2"></i>اختبار الإبلاغ عن رسالة
                        </button>
                        <div id="reportResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testBlockUser() {
            const resultDiv = document.getElementById('blockResult');
            resultDiv.innerHTML = '<div class="alert alert-info">جاري اختبار الحظر...</div>';
            
            // محاكاة طلب الحظر
            fetch('/chat/block', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': 'test-token'
                },
                body: JSON.stringify({
                    user_id: 'test-user-id'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">' + data.message + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="alert alert-danger">خطأ في الاتصال: ' + error.message + '</div>';
            });
        }
        
        function testReportMessage() {
            const resultDiv = document.getElementById('reportResult');
            resultDiv.innerHTML = '<div class="alert alert-info">جاري اختبار الإبلاغ...</div>';
            
            const formData = new FormData();
            formData.append('message_id', 'test-message-id');
            formData.append('reason', 'spam');
            formData.append('description', 'رسالة اختبار للإبلاغ');
            formData.append('_token', 'test-token');
            
            fetch('/chat/report', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">' + data.message + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="alert alert-danger">خطأ في الاتصال: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
