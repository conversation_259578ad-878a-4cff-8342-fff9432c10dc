@extends('partials.admin')
@section('title', 'Admin Users Detail')

@php
    use Illuminate\Support\Facades\Storage;
@endphp

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'users'])

<div class="main-content app-content mt-0">
    <div class="side-app">

        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'User', 'hasBack' => true, 'backTitle' => 'All Users', 'backUrl' => route('admin.users.index')])

             <div class="row">
                <div class="col-12 col-sm-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title mb-0">All User Detail</h3>
                        </div>
                        
                    </div>
                </div>
            </div>

            <div class="row" id="user-profile">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="wideget-user mb-2">
                                <div class="row">
                                    <div class="col-lg-12 col-md-12">
                                        <div class="row">
                                            <div class="panel profile-cover">
                                                <div class="profile-cover__action bg-img"></div>
                                                <div class="profile-cover__img">
                                                    <div class="profile-img-1">
                                                        <img src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}" alt="profile-img1">
                                                    </div>
                                                    <div class="profile-img-content text-dark text-start">
                                                        <div class="text-dark">
                                                            <h3 class="h3 mb-2">{{ $user->name }}</h3>
                                                            <h5 class="text-muted">{{ '@'.$user->username }}</h5>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="px-0 px-sm-4">
                                                <div class="social social-profile-buttons mt-5 float-end">
                                                    <div class="mt-3 d-flex">
                                                        <a class="social-icon text-primary" href="mailto:{{ $user->email }}" target="_blank"><i class="fa-regular fa-envelope"></i></a>
                                                        <a class="social-icon text-primary" href="tel:{{ $user->mobile }}" target="_blank"><i class="fa-regular fa-phone"></i></a>
                                                        <a class="social-icon text-dark" href="{{ route('admin.users.edit', $user->id) }}"><i class="fa-regular fa-edit"></i></a>
                                                        <form action="{{ route('admin.users.destroy', $user->id)}}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button class="social-icon text-danger" type="submit"><i class="fa-regular fa-trash"></i></button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-md-flex main-profile-contact-list">
                                        <div class="me-5">
                                            <div class="media mb-4 d-flex">
                                                <div class="media-icon bg-secondary bradius me-3 mt-1">
                                                    <i class="fa-regular fa-cube fs-20 text-white"></i>
                                                </div>
                                        
                                            </div>
                                        </div>
                                        <div class="me-5 mt-5 mt-md-0">
                                            <div class="media mb-4 d-flex">
                                                <div class="media-icon bg-danger bradius text-white me-3 mt-1">
                                                    <span class="mt-3">
                                                        <i class="fa-regular fa-gavel fs-20"></i>
                                                    </span>
                                                </div>
                                                <div class="media-body">
                                                    <span class="text-muted">Total Bids</span>
                                                    <div class="fw-semibold fs-25">
                                                        {{$user->bids_count}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="me-5 mt-5 mt-md-0">
                                            <div class="media mb-4 d-flex">
                                                <div class="media-icon bg-dark bradius text-white me-3 mt-1">
                                                    <span class="mt-3">
                                                        <i class="fa-regular fa-credit-card fs-20"></i>
                                                    </span>
                                                </div>
                                                <div class="media-body">
                                                    <span class="text-muted">Total Methods</span>
                                                    <div class="fw-semibold fs-25">
                                                        {{$user->payout_methods_count}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="me-5 mt-5 mt-md-0">
                                            <div class="media mb-4 d-flex">
                                                <div class="media-icon bg-warning bradius text-white me-3 mt-1">
                                                    <span class="mt-3">
                                                        <i class="fa-regular fa-credit-card fs-20"></i>
                                                    </span>
                                                </div>
                                                <div class="media-body">
                                                    <span class="text-muted">Total Paid</span>
                                                    <div class="fw-semibold fs-25">
                                                        {{money($user->payments->sum('amount'), true)}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="me-0 mt-5 mt-md-0">
                                            <div class="media">
                                                <div class="media-icon bg-primary text-white bradius me-3 mt-1">
                                                    <span class="mt-3">
                                                        <i class="fa-regular fa-money-bill fs-20"></i>
                                                    </span>
                                                </div>
                                                <div class="media-body">
                                                    <span class="text-muted">Total Payouts</span>
                                                    <div class="fw-semibold fs-25">
                                                        {{money($user->payouts->sum('amount'), true)}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">About</div>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-solid fa-location-crosshairs"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->address }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-regular fa-map fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->city?->name }} {{ $user->state?->name . ', ' }} {{ $user->country?->name }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-regular fa-phone fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->mobile }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-regular fa-at fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->email }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-solid fa-venus fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->gender ? $user->gender->label() : 'غير محدد' }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-regular fa-mailbox fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->zip_code ?? 'Not Available' }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-light fa-earth-americas fs-20"></i></span>
                                        </div>
                                        <div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-regular fa-ban fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->is_active ? 'Not Banned' : 'Banned' }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-light fa-calendar-days fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->created_at->format('d M, Y h:i A') }}</strong>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-3 mt-3">
                                        <div class="me-4 text-center text-primary">
                                            <span><i class="fa-regular fa-badge-check fs-20"></i></span>
                                        </div>
                                        <div>
                                            <strong>{{ $user->email_verified_at->format('d M, Y h:i A') }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if($user->is_broker)
                            <!-- معلومات المندوب -->
                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <i class="fas fa-user-tie me-2"></i>
                                        معلومات المندوب
                                    </div>
                                </div>
                                <div class="card-body">
                                    @php
                                        $brokerStats = [
                                            'total_interests' => $user->brokerInterests()->count(),
                                            'successful_codes' => $user->brokerInterests()->where('code_entered', true)->count(),
                                            'total_points_paid' => $user->brokerInterests()->sum('points_paid'),
                                            'total_points_earned' => $user->brokerInterests()->sum('points_earned'),
                                            'success_rate' => $user->brokerInterests()->count() > 0 ?
                                                ($user->brokerInterests()->where('code_entered', true)->count() / $user->brokerInterests()->count()) * 100 : 0
                                        ];
                                    @endphp

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-coins fa-2x text-warning"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">النقاط الحالية</h6>
                                                    <h4 class="mb-0 {{ $user->points > 50000 ? 'text-danger' : ($user->points > 10000 ? 'text-warning' : 'text-success') }}">
                                                        {{ number_format($user->points) }} نقطة
                                                    </h4>
                                                    @if($user->points > 50000)
                                                        <small class="text-danger">⚠️ حساب مشبوه - نقاط عالية</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-heart fa-2x text-info"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">إجمالي الاهتمامات</h6>
                                                    <h4 class="mb-0 text-info">{{ number_format($brokerStats['total_interests']) }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-key fa-2x text-success"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">الأكواد الناجحة</h6>
                                                    <h4 class="mb-0 text-success">{{ number_format($brokerStats['successful_codes']) }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-percentage fa-2x text-primary"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">معدل النجاح</h6>
                                                    <h4 class="mb-0 text-primary">{{ number_format($brokerStats['success_rate'], 1) }}%</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-arrow-down fa-2x text-danger"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">النقاط المنفقة</h6>
                                                    <h4 class="mb-0 text-danger">{{ number_format($brokerStats['total_points_paid']) }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="me-3">
                                                    <i class="fas fa-arrow-up fa-2x text-success"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">النقاط المكتسبة</h6>
                                                    <h4 class="mb-0 text-success">{{ number_format($brokerStats['total_points_earned']) }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- أزرار إدارة المندوب -->
                                    <div class="mt-3">
                                        <a href="{{ route('admin.broker-management.interests', ['broker_id' => $user->id]) }}"
                                           class="btn btn-primary btn-sm me-2">
                                            <i class="fas fa-heart"></i> عرض الاهتمامات
                                        </a>
                                        <a href="{{ route('admin.broker-management.successful-codes', ['broker_id' => $user->id]) }}"
                                           class="btn btn-success btn-sm me-2">
                                            <i class="fas fa-key"></i> الأكواد الناجحة
                                        </a>
                                        <button type="button" class="btn btn-warning btn-sm"
                                                data-bs-toggle="modal" data-bs-target="#editPointsModal">
                                            <i class="fas fa-edit"></i> تعديل النقاط
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Modal تعديل النقاط -->
                            <div class="modal fade" id="editPointsModal" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form method="POST" action="{{ route('admin.broker-management.update-points', $user) }}">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-header">
                                                <h5 class="modal-title">تعديل نقاط {{ $user->name }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label class="form-label">النقاط الحالية</label>
                                                    <input type="text" class="form-control" value="{{ number_format($user->points) }}" readonly>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">النقاط الجديدة <span class="text-danger">*</span></label>
                                                    <input type="number" name="points" class="form-control"
                                                           value="{{ $user->points }}" min="0" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">سبب التعديل <span class="text-danger">*</span></label>
                                                    <textarea name="reason" class="form-control" rows="3"
                                                              placeholder="اذكر سبب تعديل النقاط..." required></textarea>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <button type="submit" class="btn btn-primary">حفظ التعديل</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">User Actions</div>
                                </div>
                                <div class="card-body">
                                    <div class="action-user">
                                        <a href="{{ route('admin.ads.index', ['search' => $user->id]) }}">See all user ads</a>
                                        <a href="{{ route('admin.bids.index', ['bid_id' => $user->id]) }}">See all user bids</a>
                                        <a href="{{ route('admin.payouts.index', ['pyt_token' => $user->id]) }}">See all user payouts</a>
                                        <a href="{{ route('admin.payments.index', ['txn_id' => $user->id]) }}">See all user payments</a>
                                        <a href="{{ route('admin.payout-methods.index', ['user_id' => $user->id] ) }}">See all user payout methods</a>
                                        <a href="{{ route('admin.support.index', ['search' => $user->id]) }}">See all user support tickets</a>
                                        <a href="{{ route('admin.media.index', ['search' => $user->id] ) }}">See all users media</a>
                                        <a href="{{ route('admin.comments.index', ['search' => $user->id] ) }}">See all user comments</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- COL-END -->
            </div>

        </div>
        <!-- CONTAINER END -->
    </div>
</div>


@endsection
@push('scripts')
<script src="/plugin/select2/select2.full.min.js"></script>
<script src="/assets/js/select2.js"></script>
    
@endpush