<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Ad;
use App\Models\SponsoredAd;
use App\Services\SponsoredAdService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SponsoredAdTrackingController extends Controller
{
    protected SponsoredAdService $sponsoredAdService;

    public function __construct(SponsoredAdService $sponsoredAdService)
    {
        $this->sponsoredAdService = $sponsoredAdService;
    }

    /**
     * تسجيل مشاهدة للإعلان الممول
     */
    public function trackView(Request $request, Ad $ad): JsonResponse
    {
        try {
            // التحقق من وجود الإعلان الممول وأنه نشط
            if (!$ad->sponsoredAd || !$ad->sponsoredAd->isActive()) {
                return response()->json(['success' => false, 'message' => 'Ad is not sponsored or not active'], 400);
            }

            // تسجيل المشاهدة
            $ad->sponsoredAd->incrementViews();

            return response()->json([
                'success' => true,
                'message' => 'View tracked successfully',
                'data' => [
                    'views_count' => $ad->sponsoredAd->views_count,
                    'ctr' => $ad->sponsoredAd->getCTRText()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to track view'
            ], 500);
        }
    }

    /**
     * تسجيل نقرة للإعلان الممول
     */
    public function trackClick(Request $request, Ad $ad): JsonResponse
    {
        try {
            // التحقق من وجود الإعلان الممول وأنه نشط
            if (!$ad->sponsoredAd || !$ad->sponsoredAd->isActive()) {
                return response()->json(['success' => false, 'message' => 'Ad is not sponsored or not active'], 400);
            }

            // تسجيل النقرة
            $ad->sponsoredAd->incrementClicks();

            return response()->json([
                'success' => true,
                'message' => 'Click tracked successfully',
                'data' => [
                    'clicks_count' => $ad->sponsoredAd->clicks_count,
                    'views_count' => $ad->sponsoredAd->views_count,
                    'ctr' => $ad->sponsoredAd->getCTRText()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to track click'
            ], 500);
        }
    }

    /**
     * الحصول على إحصائيات الإعلان الممول
     */
    public function getStats(Ad $ad): JsonResponse
    {
        try {
            if (!$ad->sponsoredAd) {
                return response()->json(['success' => false, 'message' => 'Ad is not sponsored'], 400);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'ad_id' => $ad->id,
                    'sponsored_ad_id' => $ad->sponsoredAd->id,
                    'views_count' => $ad->sponsoredAd->views_count,
                    'clicks_count' => $ad->sponsoredAd->clicks_count,
                    'ctr' => $ad->sponsoredAd->ctr,
                    'ctr_text' => $ad->sponsoredAd->getCTRText(),
                    'cost' => $ad->sponsoredAd->cost,
                    'status' => $ad->sponsoredAd->status,
                    'is_active' => $ad->sponsoredAd->isActive(),
                    'expires_at' => $ad->sponsoredAd->expires_at?->format('Y-m-d H:i:s'),
                    'remaining_minutes' => $ad->sponsoredAd->getRemainingMinutes(),
                    'remaining_time_text' => $ad->sponsoredAd->getRemainingTimeText()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get stats'
            ], 500);
        }
    }

    /**
     * الحصول على أفضل الإعلانات الممولة أداءً
     */
    public function getTopPerforming(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $topAds = $this->sponsoredAdService->getTopPerformingSponsoredAds($limit);

            return response()->json([
                'success' => true,
                'data' => $topAds->map(function ($sponsoredAd) {
                    return [
                        'ad_id' => $sponsoredAd->ad->id,
                        'ad_title' => $sponsoredAd->ad->title,
                        'ad_slug' => $sponsoredAd->ad->slug,
                        'views_count' => $sponsoredAd->views_count,
                        'clicks_count' => $sponsoredAd->clicks_count,
                        'ctr' => $sponsoredAd->ctr,
                        'cost' => $sponsoredAd->cost,
                        'user_name' => $sponsoredAd->ad->user->name ?? 'N/A',
                        'category_name' => $sponsoredAd->ad->category->name ?? 'N/A'
                    ];
                })
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get top performing ads'
            ], 500);
        }
    }
}
