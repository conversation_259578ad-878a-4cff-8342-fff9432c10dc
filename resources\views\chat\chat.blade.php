
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>ChatAPP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- استدعاء jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>

    <!-- استدعاء Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    @vite('resources/css/chat.css')
</head>
<body>
    <div class="container content">
        <div class="row justify-content-center">
            <div class="col-xl-8 col-lg-8 col-md-6 col-sm-12 col-12">
                <div class="card">
                    <div class="card-header">Chat</div>
                    <div class="card-body height3">
                        <ul class="chat-list" id="chat-section">
                        </ul>
                    </div>
                </div>
                <div class="row mt-3 justify-content-between">
                    <div class="col-lg-9">
                        @auth
                            <input type="hidden" id="username" value="{{ Auth::user()->name }}">
                        @endauth
                        <input class="form-control" type="text" id="chat_message" placeholder="Type your message">
                    </div> 
                    <div class="col-lg-3">
                        <button type="submit" class="btn btn-primary rounded w-100" onclick="broadcastMethod()">Send</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function broadcastMethod() {
            let message = $("#chat_message").val().trim();
            if (message === "") {
                alert("Message cannot be empty!");
                return;
            }

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                url: '{{ route("chat.broadcast") }}',
                type: 'POST',
                data: {
                    username: $("#username").val(),
                    msg: message
                },
                success: function(result) {
                    $("#chat_message").val(""); // مسح حقل الإدخال بعد الإرسال
                },
                error: function(error) {
                    console.log(error);
                }
            });
        }

        window.onload = function() {
            setTimeout(() => {
                window.Echo.channel('chat')
                    .listen('.chat.message', (data) => {
                        let newMessage = `
                            <li class="${data.username === $("#username").val() ? 'in' : 'out'}">
                                <div class="chat-img">
                                    <img alt="Avatar" src="https://bootdey.com/img/Content/avatar/avatar1.png">
                                </div>
                                <div class="chat-body">
                                    <div class="chat-message">
                                        <h5>${data.username}</h5>
                                        <p>${data.message}</p>
                                    </div>
                                </div>
                            </li>`;

                        $("#chat-section").append(newMessage);
                    });
            }, 100);
        };
    </script>
</body>
</html>
