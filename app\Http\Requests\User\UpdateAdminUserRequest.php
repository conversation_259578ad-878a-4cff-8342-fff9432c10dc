<?php

namespace App\Http\Requests\User;

use App\Enums\Gender;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateAdminUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name'  => ['required', 'string', 'max:255'],
            'username'  => [],
            'email'  => [],
            'mobile'  => [],
            'address'    => ['required', 'string', 'max:255'],
            'gender'     => ['required', new Enum(Gender::class)],
            'country_id' => ['required', 'string', 'exists:countries,id'],
            'is_active'  => ['required', 'boolean'],
            'is_broker'  => ['required', 'boolean'],
            'points'     => ['nullable', 'integer', 'min:0'],
            'zip_code' =>   [],
            'rank'=>   [ ],
            'is_trusted'=>   [],
            'Number_Ads'=>   [],
            'national_id' => ['nullable', 'string', 'max:20', 'unique:users,national_id,' . $this->route('user')],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif,webp', 'max:5120'], // 5MB max
        ];
    }
}
