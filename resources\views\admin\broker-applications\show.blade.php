@extends('partials.admin')

@section('title', 'تفاصيل طلب المندوب')

@section('content')
@include('layouts.header', ['admin' => true])

<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-tie me-2"></i>
            تفاصيل طلب المندوب
        </h1>
        <a href="{{ route('admin.broker-applications.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row">
        <!-- معلومات المتقدم -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات المتقدم</h6>
                </div>
                <div class="card-body text-center">
                    <img src="{{ $application->user->avatar_url }}" 
                         class="rounded-circle mb-3" 
                         width="100" height="100">
                    <h5 class="mb-1">{{ $application->full_name }}</h5>
                    <p class="text-muted mb-1">{{ $application->user->email }}</p>

                    <!-- الرقم القومي -->
                    <div class="mb-3">
                        @if($application->national_id)
                            <p class="mb-1">
                                <strong>الرقم القومي المقدم:</strong> {{ $application->national_id }}
                            </p>

                            @php
                                // التحقق من حالة توثيق المستخدم
                                $isUserVerified = $application->user->national_id &&
                                                 $application->user->identityVerification &&
                                                 $application->user->identityVerification->status === 'approved';

                                // التحقق من تكرار الرقم مع مستخدمين آخرين
                                $existingUser = \App\Models\User::where('national_id', $application->national_id)
                                                               ->where('id', '!=', $application->user->id)
                                                               ->first();
                            @endphp

                            @if($isUserVerified && $application->user->national_id === $application->national_id)
                                <!-- المستخدم موثق بنفس الرقم -->
                                <div class="mt-2">
                                    <span class="badge bg-success">
                                        <i class="fas fa-shield-check me-1"></i>
                                        مستخدم موثق مسبقاً بهذا الرقم
                                    </span>
                                </div>
                                <div class="alert alert-success mt-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>
                                        هذا المستخدم موثق مسبقاً بنفس الرقم القومي. يمكن قبول الطلب بأمان.
                                    </small>
                                </div>
                            @elseif($isUserVerified && $application->user->national_id !== $application->national_id)
                                <!-- المستخدم موثق برقم مختلف -->
                                <div class="mt-2">
                                    <span class="badge bg-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        مستخدم موثق برقم مختلف
                                    </span>
                                </div>
                                <div class="alert alert-warning mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <small>
                                        <strong>تحذير:</strong> المستخدم موثق مسبقاً بالرقم: {{ $application->user->national_id }}
                                        <br>لا يمكن تغيير الرقم القومي للمستخدمين الموثقين.
                                    </small>
                                </div>
                            @elseif($existingUser)
                                <!-- رقم مكرر مع مستخدم آخر -->
                                <div class="mt-2">
                                    <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        رقم مكرر - مستخدم: {{ $existingUser->name }}
                                    </span>
                                </div>
                            @else
                                <!-- رقم غير مكرر -->
                                <div class="mt-2">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        رقم غير مكرر
                                    </span>
                                </div>
                            @endif
                        @else
                            <p class="text-muted mb-1">الرقم القومي: غير محدد</p>
                        @endif

                        @if($application->user->national_id && $application->user->national_id !== $application->national_id)
                            <div class="mt-2">
                                <small class="text-info">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الرقم الحالي للمستخدم: {{ $application->user->national_id }}
                                </small>
                            </div>
                        @endif

                        <!-- نموذج تعديل الرقم القومي للأدمن -->
                        @if($application->status === 'second_stage_accepted' && !($isUserVerified ?? false))
                            <div class="mt-3">
                                <form action="{{ route('admin.broker-applications.update-national-id', $application) }}" method="POST" class="d-inline-flex align-items-center">
                                    @csrf
                                    <div class="input-group input-group-sm" style="max-width: 200px;">
                                        <input type="text"
                                               class="form-control"
                                               name="national_id"
                                               value="{{ $application->national_id }}"
                                               placeholder="الرقم القومي"
                                               maxlength="20"
                                               required>
                                        <button type="submit" class="btn btn-primary btn-sm">
                                            <i class="fas fa-save me-1"></i>
                                            تحديث
                                        </button>
                                    </div>
                                </form>
                            </div>
                        @elseif($application->status === 'second_stage_accepted' && ($isUserVerified ?? false))
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-lock me-1"></i>
                                    لا يمكن تعديل الرقم القومي للمستخدمين الموثقين
                                </small>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mb-3">
                        <span class="badge bg-{{ $application->status_color }} fs-6">
                            {{ $application->status_text }}
                        </span>
                    </div>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-muted">تاريخ التقديم</h6>
                                <p class="mb-0">{{ $application->created_at->format('Y-m-d') }}</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted">آخر تحديث</h6>
                            <p class="mb-0">{{ $application->updated_at->format('Y-m-d') }}</p>
                        </div>
                    </div>

                    <!-- رابط صفحة المستخدم -->
                    <div class="mt-3">
                        <a href="{{ route('admin.users.edit', $application->user->id) }}"
                           class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-user-edit me-2"></i>
                            عرض صفحة المستخدم
                        </a>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات</h6>
                </div>
                <div class="card-body">
                    @if($application->status === 'pending')
                        <button type="button" 
                                class="btn btn-success btn-block mb-2" 
                                onclick="acceptFirstStage({{ $application->id }})">
                            <i class="fas fa-check me-2"></i>
                            قبول في المرحلة الأولى
                        </button>
                        <button type="button" 
                                class="btn btn-danger btn-block" 
                                onclick="rejectApplication({{ $application->id }})">
                            <i class="fas fa-times me-2"></i>
                            رفض الطلب
                        </button>
                    @elseif($application->status === 'first_stage_accepted')
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            في انتظار رفع المستندات من المتقدم
                        </div>
                        <button type="button" 
                                class="btn btn-danger btn-block" 
                                onclick="rejectApplication({{ $application->id }})">
                            <i class="fas fa-times me-2"></i>
                            رفض الطلب
                        </button>
                    @elseif($application->status === 'second_stage_accepted')
                        <button type="button" 
                                class="btn btn-success btn-block mb-2" 
                                onclick="acceptFinal({{ $application->id }})">
                            <i class="fas fa-check-double me-2"></i>
                            قبول نهائي (تحويل لمندوب)
                        </button>
                        <button type="button" 
                                class="btn btn-danger btn-block" 
                                onclick="rejectApplication({{ $application->id }})">
                            <i class="fas fa-times me-2"></i>
                            رفض الطلب
                        </button>
                    @elseif($application->status === 'rejected')
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            تم رفض هذا الطلب
                        </div>
                        @if($application->canReapply())
                            <div class="alert alert-success">
                                <i class="fas fa-info-circle me-2"></i>
                                يمكن للمتقدم التقديم مرة أخرى
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-clock me-2"></i>
                                يمكن التقديم مرة أخرى بعد {{ $application->daysUntilReapply() }} يوم
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>

        <!-- تفاصيل الطلب -->
        <div class="col-lg-8">
            <!-- المعلومات الأساسية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">المعلومات الأساسية</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted">الاسم الكامل</h6>
                            <p>{{ $application->full_name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted">البريد الإلكتروني</h6>
                            <p>{{ $application->user->email }}</p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">المؤهل التعليمي</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $application->education }}
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">الخبرات والمهارات</h6>
                        <div class="bg-light p-3 rounded">
                            {{ $application->experience }}
                        </div>
                    </div>

                    @if($application->additional_notes)
                        <div class="mb-3">
                            <h6 class="text-muted">ملاحظات إضافية</h6>
                            <div class="bg-light p-3 rounded">
                                {{ $application->additional_notes }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- نقاط المستخدم (للمندوبين) -->
            @if($application->user->is_broker)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="fas fa-coins me-2"></i>
                            نقاط المندوب
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3>{{ number_format($application->user->points ?? 0) }}</h3>
                                        <p class="mb-0">النقاط الحالية</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3>{{ number_format($application->user->brokerInterests()->sum('points_earned') ?? 0) }}</h3>
                                        <p class="mb-0">النقاط المكتسبة</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 text-center">
                            <button type="button" class="btn btn-warning"
                                    data-bs-toggle="modal" data-bs-target="#editPointsModal">
                                <i class="fas fa-edit me-2"></i>
                                تعديل النقاط
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- المستندات (إذا كانت متوفرة) -->
            @if($application->documents && count($application->documents) > 0)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">المستندات المرفوعة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @if(isset($application->documents['education_certificate']))
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <i class="fas fa-graduation-cap me-2"></i>
                                            شهادة التعليم
                                        </div>
                                        <div class="card-body text-center">
                                            <a href="{{ \Illuminate\Support\Facades\Storage::url($application->documents['education_certificate']) }}" 
                                               target="_blank" 
                                               class="btn btn-primary">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض المستند
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if(isset($application->documents['id_card']))
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <i class="fas fa-id-card me-2"></i>
                                            البطاقة الشخصية
                                        </div>
                                        <div class="card-body text-center">
                                            <a href="{{ \Illuminate\Support\Facades\Storage::url($application->documents['id_card']) }}" 
                                               target="_blank" 
                                               class="btn btn-primary">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض المستند
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if(isset($application->documents['experience_proof']))
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <i class="fas fa-briefcase me-2"></i>
                                            إثبات الخبرة
                                        </div>
                                        <div class="card-body text-center">
                                            <a href="{{ \Illuminate\Support\Facades\Storage::url($application->documents['experience_proof']) }}" 
                                               target="_blank" 
                                               class="btn btn-primary">
                                                <i class="fas fa-eye me-2"></i>
                                                عرض المستند
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if(isset($application->documents['additional_documents']))
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-secondary text-white">
                                            <i class="fas fa-file-alt me-2"></i>
                                            مستندات إضافية
                                        </div>
                                        <div class="card-body">
                                            @foreach($application->documents['additional_documents'] as $index => $document)
                                                <a href="{{ \Illuminate\Support\Facades\Storage::url($document) }}" 
                                                   target="_blank" 
                                                   class="btn btn-sm btn-outline-primary mb-1 d-block">
                                                    <i class="fas fa-eye me-2"></i>
                                                    مستند إضافي {{ $index + 1 }}
                                                </a>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- سبب الرفض (إذا كان مرفوضاً) -->
            @if($application->status === 'rejected' && $application->rejection_reason)
                <div class="card shadow mb-4">
                    <div class="card-header py-3 bg-danger text-white">
                        <h6 class="m-0 font-weight-bold">سبب الرفض</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ $application->rejection_reason }}
                        </div>
                        <small class="text-muted">
                            تم الرفض في: {{ $application->rejected_at->format('Y-m-d H:i') }}
                        </small>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Reject Application Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض *</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4" required></textarea>
                        <div class="form-text">سيتم إرسال هذا السبب للمتقدم</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function acceptFirstStage(applicationId) {
    if (confirm('هل أنت متأكد من قبول هذا الطلب في المرحلة الأولى؟\nسيتم إشعار المتقدم برفع المستندات المطلوبة.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/broker-applications/${applicationId}/accept-first-stage`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function acceptFinal(applicationId) {
    if (confirm('هل أنت متأكد من قبول هذا الطلب نهائياً؟\nسيتم تحويل المستخدم إلى مندوب وإعطاؤه 5000 نقطة.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/broker-applications/${applicationId}/accept-final`;

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';

        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectApplication(applicationId) {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    const form = document.getElementById('rejectForm');
    form.action = `/admin/broker-applications/${applicationId}/reject`;
    modal.show();
}
</script>

<!-- Modal لتعديل النقاط -->
@if($application->user->is_broker)
<div class="modal fade" id="editPointsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.broker-management.update-points', $application->user) }}">
                @csrf
                @method('PUT')
                <div class="modal-header">
                    <h5 class="modal-title">تعديل نقاط {{ $application->user->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">النقاط الحالية</label>
                        <input type="text" class="form-control" value="{{ number_format($application->user->points ?? 0) }}" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">النقاط الجديدة <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="points"
                               value="{{ $application->user->points ?? 0 }}"
                               min="0" step="1" required>
                        <small class="form-text text-muted">أدخل عدد النقاط الجديد</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سبب التعديل <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="reason" rows="3"
                                  placeholder="اكتب سبب تعديل النقاط..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التعديل</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

@endsection
