<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('ad_id')->constrained('ads')->onDelete('cascade');
            $table->unsignedBigInteger('category_attribute_id');
            $table->string('attribute_name'); // e.g., 'brand', 'year', 'condition'
            $table->string('attribute_label'); // e.g., 'نوع السيارة', 'سنة الصنع'
            $table->text('attribute_value'); // The actual value entered by user
            $table->string('attribute_type')->default('select'); // select, input, textarea
            $table->timestamps();
            
            $table->foreign('category_attribute_id')->references('id')->on('category_attributes')->onDelete('cascade');
            $table->index(['ad_id', 'attribute_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_attributes');
    }
};
