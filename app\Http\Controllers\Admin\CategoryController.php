<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\CategoryAttribute;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index(): View
    {
        $categories = Category::with(['children', 'attributes'])
            ->whereIsRoot()
            ->orderBy('name')
            ->get();

        return view('admin.categories.index', compact('categories'));
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|string|max:255',
        ]);

        try {
            DB::transaction(function () use ($request) {
                $category = new Category();
                $category->name = $request->name;
                $category->slug = Str::slug($request->name);
                $category->description = $request->description;
                $category->icon = $request->icon;
                $category->image = $request->image;

                if ($request->parent_id) {
                    $parent = Category::findOrFail($request->parent_id);
                    $parent->appendNode($category);
                } else {
                    $category->saveAsRoot();
                }
            });

            return redirect()->route('admin.categories.index')
                ->with('success', 'Category created successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating category: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Error creating category: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, Category $category): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|string|max:255',
        ]);

        try {
            $category->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'icon' => $request->icon,
                'image' => $request->image,
            ]);

            return redirect()->route('admin.categories.index')
                ->with('success', 'Category updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating category: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Error updating category: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified category.
     */
    public function destroy(Category $category): RedirectResponse
    {
        try {
            // Check if category has ads
            if ($category->ads()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Cannot delete category that has ads. Please move or delete the ads first.');
            }

            // Check if category has children
            if ($category->children()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Cannot delete category that has subcategories. Please delete subcategories first.');
            }

            $category->delete();

            return redirect()->route('admin.categories.index')
                ->with('success', 'Category deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting category: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Error deleting category: ' . $e->getMessage());
        }
    }

    /**
     * Store a new attribute for a category.
     */
    public function storeAttribute(Request $request, Category $category)
    {
        $request->validate([
            'attribute_name' => 'required|string|max:255',
            'attribute_label' => 'required|string|max:255',
            'attribute_type' => 'required|in:select,input,textarea',
            'attribute_options' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_required' => 'boolean',
        ]);

        try {
            Log::info('Creating attribute', [
                'category_id' => $category->id,
                'request_data' => $request->all()
            ]);

            // Parse attribute options if provided
            $options = null;
            if ($request->attribute_options) {
                $options = json_decode($request->attribute_options, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON in attribute_options: ' . json_last_error_msg());
                }
            }

            $attribute = CategoryAttribute::create([
                'category_id' => $category->id,
                'attribute_name' => $request->attribute_name,
                'attribute_label' => $request->attribute_label,
                'attribute_type' => $request->attribute_type,
                'attribute_options' => $options,
                'sort_order' => $request->sort_order ?? 0,
                'is_required' => $request->boolean('is_required'),
            ]);

            Log::info('Attribute created successfully', ['attribute_id' => $attribute->id]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Attribute added successfully.',
                    'attribute' => $attribute
                ]);
            }

            return redirect()->route('admin.categories.index')
                ->with('success', 'Attribute added successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating attribute', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating attribute: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error creating attribute: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update a category attribute.
     */
    public function updateAttribute(Request $request, CategoryAttribute $attribute)
    {
        $request->validate([
            'attribute_name' => 'required|string|max:255',
            'attribute_label' => 'required|string|max:255',
            'attribute_type' => 'required|in:select,input,textarea',
            'attribute_options' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_required' => 'boolean',
        ]);

        try {
            // Parse attribute options if provided
            $options = null;
            if ($request->attribute_options) {
                $options = json_decode($request->attribute_options, true);
            }

            $attribute->update([
                'attribute_name' => $request->attribute_name,
                'attribute_label' => $request->attribute_label,
                'attribute_type' => $request->attribute_type,
                'attribute_options' => $options,
                'sort_order' => $request->sort_order ?? 0,
                'is_required' => $request->boolean('is_required'),
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Attribute updated successfully.',
                    'attribute' => $attribute
                ]);
            }

            return redirect()->route('admin.categories.index')
                ->with('success', 'Attribute updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating attribute: ' . $e->getMessage());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating attribute: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error updating attribute: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove a category attribute.
     */
    public function destroyAttribute(Request $request, CategoryAttribute $attribute)
    {
        try {
            // Check if any ads are using this attribute
            $adAttributesCount = \App\Models\AdAttribute::where('category_attribute_id', $attribute->id)->count();

            if ($adAttributesCount > 0) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => "Cannot delete attribute. It is being used by {$adAttributesCount} ads."
                    ], 400);
                }

                return redirect()->back()
                    ->with('error', "Cannot delete attribute. It is being used by {$adAttributesCount} ads.");
            }

            $attribute->delete();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Attribute deleted successfully.'
                ]);
            }

            return redirect()->route('admin.categories.index')
                ->with('success', 'Attribute deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting attribute: ' . $e->getMessage(), [
                'attribute_id' => $attribute->id,
                'attribute_name' => $attribute->attribute_name,
                'stack_trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error deleting attribute: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error deleting attribute: ' . $e->getMessage());
        }
    }

    /**
     * Get subcategories for a parent category (AJAX).
     */
    public function getSubcategories(Category $category)
    {
        return response()->json([
            'success' => true,
            'subcategories' => $category->children()->select('id', 'name', 'slug')->get()
        ]);
    }

    /**
     * Get attributes for a category (AJAX).
     */
    public function getAttributes(Category $category)
    {
        $attributes = $category->attributes()->orderBy('sort_order')->get();

        return response()->json([
            'success' => true,
            'attributes' => $attributes
        ]);
    }
}
