<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\ChMessage as Message;

class CleanSelfMessages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chatify:clean-self-messages {--dry-run : عرض الرسائل بدون حذفها}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تنظيف الرسائل المرسلة للنفس في نظام المحادثات';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('بدء تنظيف الرسائل المرسلة للنفس...');
        
        // البحث عن الرسائل المرسلة للنفس
        $selfMessages = Message::where('from_id', '=', DB::raw('to_id'))->get();
        
        $this->info("تم العثور على " . $selfMessages->count() . " رسالة مرسلة للنفس");
        
        if ($selfMessages->count() == 0) {
            $this->info('لا توجد رسائل مرسلة للنفس');
            return 0;
        }
        
        // عرض تفاصيل الرسائل إذا كان dry-run
        if ($this->option('dry-run')) {
            $this->warn('وضع المعاينة - لن يتم حذف أي رسائل');
            
            $headers = ['ID', 'من', 'إلى', 'الرسالة', 'التاريخ'];
            $rows = [];
            
            foreach ($selfMessages as $message) {
                $rows[] = [
                    $message->id,
                    $message->from_id,
                    $message->to_id,
                    substr($message->body, 0, 50) . '...',
                    $message->created_at->format('Y-m-d H:i:s')
                ];
            }
            
            $this->table($headers, $rows);
            return 0;
        }
        
        // تأكيد الحذف
        if (!$this->confirm('هل أنت متأكد من حذف جميع الرسائل المرسلة للنفس؟')) {
            $this->info('تم إلغاء العملية');
            return 0;
        }
        
        // حذف الرسائل
        $deleted = Message::where('from_id', '=', DB::raw('to_id'))->delete();
        
        $this->info("تم حذف " . $deleted . " رسالة بنجاح");
        
        // عرض إحصائيات
        $remainingMessages = Message::count();
        $this->info("إجمالي الرسائل المتبقية: " . $remainingMessages);
        
        $this->info('تم الانتهاء من تنظيف قاعدة البيانات بنجاح');
        
        return 0;
    }
} 