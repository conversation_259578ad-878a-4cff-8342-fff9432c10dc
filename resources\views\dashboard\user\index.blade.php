@extends('partials.app')
@section('title', 'Dashboard')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('content')

@php
    $user = auth()->user();
    $averageRating = $user->average_rating ?? 0; // التقييم من 1-5 نجوم
    $level = min(floor($user->Number_Ads / 20) + 1, 50); // المستوى من 1-50 حسب عدد الإعلانات
    $totalAds = $user->Number_Ads; // عدد الإعلانات المنشورة فقط
    $recentAds = $user->ads()->with('latestAdminNote.admin')->latest()->take(3)->get();
    $recentRatings = $user->receivedRatings()->with('rater')->latest()->take(3)->get();
    $brokerRequests = collect(); // Will be implemented later when broker system is ready
    $interestedAds = $user->brokerInterests()->where('status', 'active')->with('ad')->latest()->take(5)->get();

    // بيانات المندوب
    if ($user->is_broker) {
        $brokerPoints = $user->points ?? 0;
        $activeBrokerInterests = $user->activeBrokerInterests()->count();

        // الأكواد الناجحة من جدول BrokerInterest
        $totalSuccessfulCodes = \App\Models\BrokerInterest::where('broker_id', $user->id)
            ->where('code_entered', true)
            ->count();

        $recentSuccessfulCodes = \App\Models\BrokerInterest::where('broker_id', $user->id)
            ->where('code_entered', true)
            ->where('code_entered_at', '>=', now()->subDays(30))
            ->with('ad')
            ->orderBy('code_entered_at', 'desc')
            ->take(5)
            ->get();

        // آخر كود ناجح لحساب العداد
        // ملاحظة مهمة: النشاط يتجدد فقط بالأكواد الناجحة (code_entered = true)
        // إظهار الاهتمام بالإعلانات لا يجدد فترة النشاط
        $lastSuccessfulCode = \App\Models\BrokerInterest::where('broker_id', $user->id)
            ->where('code_entered', true)
            ->orderBy('code_entered_at', 'desc')
            ->first();

        // حساب الأيام المتبقية للنشاط (30 يوم من آخر كود ناجح أو فترة سماح للمندوب الجديد)
        $daysUntilInactive = 0;
        $isActiveBroker = true;
        $isNewBroker = false;

        if ($user->last_successful_code_at) {
            // لديه أكواد ناجحة - نحسب من آخر كود ناجح في جدول المستخدمين
            $daysSinceLastCode = $user->last_successful_code_at->diffInDays(now());
            $daysUntilInactive = max(0, 30 - $daysSinceLastCode);
            $isActiveBroker = $daysSinceLastCode < 30;
            $isNewBroker = false; // ليس مندوب جديد لأن لديه أكواد ناجحة
        } else {
            // لا يوجد أكواد ناجحة - نتحقق من تاريخ تفعيل المندوب
            // المندوب الجديد له فترة سماح 30 يوم من تاريخ التفعيل
            // هذه الفترة لا تتجدد إلا بكود ناجح
            $brokerActivationDate = $user->broker_activated_at ?? $user->updated_at;
            $daysSinceBecameBroker = $brokerActivationDate->diffInDays(now());

            if ($daysSinceBecameBroker <= 30) {
                // مندوب جديد - فترة سماح (لا تتجدد بالاهتمام)
                $isActiveBroker = true;
                $isNewBroker = true;
                $daysUntilInactive = max(0, 30 - $daysSinceBecameBroker);
            } else {
                // مندوب قديم بدون أكواد ناجحة - غير نشط
                $isActiveBroker = false;
                $isNewBroker = false;
                $daysUntilInactive = 0;
            }
        }
    }
@endphp

<!-- Admin Impersonation Alert -->
@if(auth()->user() && auth()->user()->isImpersonated())
<div class="admin-impersonation-alert">
    <div class="container">
        <div class="alert alert-warning d-flex align-items-center justify-content-between mb-0" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-user-secret me-3 fs-4"></i>
                <div>
                    <strong>Admin Mode:</strong> You are currently logged in as <strong>{{ auth()->user()->name }}</strong>
                    <br><small class="text-muted">You are viewing this account as an administrator</small>
                </div>
            </div>
            <a href="{{ route('admin.leave-impersonate') }}" class="btn btn-outline-dark btn-sm">
                <i class="fas fa-sign-out-alt me-2"></i>Return to Admin
            </a>
        </div>
    </div>
</div>
@endif

<!-- Hero Section -->
<div class="profile-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="hero-content">
                    <nav aria-label="breadcrumb" class="hero-breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item active">Dashboard</li>
                        </ol>
                    </nav>
                    <h1 class="hero-title">Welcome back, {{ $user->name }}!</h1>
                    <p class="hero-subtitle">Manage your account and track your activity</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="dashboard-nav-section">
    <div class="container">
        <div class="nav-tabs-wrapper">
            <ul class="nav nav-tabs dashboard-tabs" id="dashboardTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="{{ route('user.profile') }}">
                        <i class="fas fa-user"></i>
                        <span>My Profile</span>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <form method="POST" action="{{ route('user.logout.handle') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="profile-main-section">
    <div class="container">
        <div class="tab-content" id="dashboardTabsContent">
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row">
                    <!-- Active Warnings Alert -->
                    @if($user->activeWarnings->count() > 0)
                        <div class="col-12 mb-4">
                            @foreach($user->activeWarnings as $warning)
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-exclamation-triangle fa-2x me-3 mt-1"></i>
                                        <div class="flex-grow-1">
                                            <h5 class="alert-heading mb-2">
                                                <i class="fas fa-flag me-2"></i>{{ $warning->type_label }}
                                            </h5>
                                            <h6 class="fw-bold">{{ $warning->title }}</h6>
                                            <p class="mb-2">{{ $warning->reason }}</p>

                                            @if($warning->expires_at)
                                                <div class="mb-2">
                                                    <small class="fw-semibold">
                                                        <i class="fas fa-clock me-1"></i>
                                                        ينتهي في: {{ $warning->remaining_time }}
                                                    </small>
                                                </div>
                                            @else
                                                <div class="mb-2">
                                                    <small class="fw-semibold text-danger">
                                                        <i class="fas fa-infinity me-1"></i>
                                                        تحذير دائم
                                                    </small>
                                                </div>
                                            @endif

                                            @if($warning->admin_notes)
                                                <div class="border-top pt-2 mt-2">
                                                    <small class="text-muted">
                                                        <strong>ملاحظات الإدارة:</strong> {{ $warning->admin_notes }}
                                                    </small>
                                                </div>
                                            @endif

                                            <div class="border-top pt-2 mt-2">
                                                <small class="text-muted">
                                                    تم التوجيه في: {{ $warning->created_at->format('d/m/Y H:i') }}
                                                    بواسطة: {{ $warning->admin->name ?? 'الإدارة' }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    <!-- User Info Card -->
                    <div class="col-lg-4">
                        <div class="user-info-card">
                            <div class="user-avatar">
                                <img src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}" alt="{{ $user->name }}">
                                @if($user->is_trusted)
                                    <div class="verified-badge-blue">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                @else
                                    <div class="unverified-badge">
                                        <i class="fas fa-shield-exclamation"></i>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="user-details">
                                <h3 class="user-name">{{ $user->name }}</h3>
                                
                                <!-- User Badges -->
                                <div class="user-badges">
                                    @if($user->is_broker ?? false)
                                        <span class="badge badge-broker">
                                            <i class="fas fa-handshake"></i>
                                            Representative
                                        </span>
                                    @endif

                                    <span class="badge {{ $user->email_verified_at ? 'badge-verified' : 'badge-unverified' }}">
                                        <i class="fas {{ $user->email_verified_at ? 'fa-check-circle' : 'fa-exclamation-triangle' }}"></i>
                                        {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                                    </span>

                                    <span class="badge badge-level">Level {{ $level }}</span>
                                </div>

                                <!-- Premium Rank Display -->
                                @switch($user->rank ?? 0)
                                    @case(1)
                                        <div class="premium-rank-card vip-member">
                                            <div class="rank-header">
                                                <i class="fas fa-crown rank-icon"></i>
                                                <span class="rank-title">VIP MEMBER</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-sparkles"></i>
                                                    <i class="fas fa-sparkles"></i>
                                                    <i class="fas fa-sparkles"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Elite Status • Verified Excellence</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Priority Support</span>
                                                    <span><i class="fas fa-check"></i> Exclusive Deals</span>
                                                    <span><i class="fas fa-check"></i> Premium Features</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @case(2)
                                        <div class="premium-rank-card trader">
                                            <div class="rank-header">
                                                <i class="fas fa-chart-line rank-icon"></i>
                                                <span class="rank-title">TRADER</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Professional Trader • Market Expert</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Advanced Analytics</span>
                                                    <span><i class="fas fa-check"></i> Market Insights</span>
                                                    <span><i class="fas fa-check"></i> Trading Tools</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @case(3)
                                        <div class="premium-rank-card company">
                                            <div class="rank-header">
                                                <i class="fas fa-building rank-icon"></i>
                                                <span class="rank-title">COMPANY</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-certificate"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Business Account • Corporate Solutions</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Bulk Operations</span>
                                                    <span><i class="fas fa-check"></i> Business Support</span>
                                                    <span><i class="fas fa-check"></i> Corporate Features</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @case(4)
                                        <div class="premium-rank-card premium">
                                            <div class="rank-header">
                                                <i class="fas fa-gem rank-icon"></i>
                                                <span class="rank-title">PREMIUM</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-diamond"></i>
                                                    <i class="fas fa-diamond"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Premium Account • Ultimate Experience</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> All Features</span>
                                                    <span><i class="fas fa-check"></i> Premium Support</span>
                                                    <span><i class="fas fa-check"></i> Exclusive Access</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @default
                                        <div class="premium-rank-card regular">
                                            <div class="rank-header">
                                                <i class="fas fa-user rank-icon"></i>
                                                <span class="rank-title">REGULAR MEMBER</span>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Standard Account • Getting Started</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Basic Features</span>
                                                    <span><i class="fas fa-check"></i> Community Access</span>
                                                    <span><i class="fas fa-check"></i> Standard Support</span>
                                                </div>
                                            </div>
                                        </div>
                                @endswitch
                                
                                <!-- User Stats -->
                                <div class="user-stats">
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="rating-display">
                                                <span class="stat-value">{{ number_format($averageRating, 1) }}</span>
                                                <div class="stars">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star {{ $i <= round($averageRating) ? 'filled' : '' }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <span class="stat-label">{{ $user->total_ratings ?? 0 }} reviews</span>
                                        </div>
                                    </div>
                                    
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-bullhorn"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $totalAds }}</span>
                                            <span class="stat-label">Published Ads</span>
                                        </div>
                                    </div>

                                    @if($user->is_broker)
                                    <!-- Broker Points -->
                                    <div class="stat-item broker-points">
                                        <div class="stat-icon">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ number_format($brokerPoints) }}</span>
                                            <span class="stat-label">Broker Points</span>
                                        </div>
                                    </div>

                                    @if($isActiveBroker)
                                    <!-- Active Interests -->
                                    <div class="stat-item broker-interests">
                                        <div class="stat-icon">
                                            <i class="fas fa-heart"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $activeBrokerInterests }}</span>
                                            <span class="stat-label">Active Interests</span>
                                        </div>
                                    </div>

                                    <!-- Successful Codes -->
                                    <div class="stat-item broker-codes">
                                        <div class="stat-icon">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $totalSuccessfulCodes }}</span>
                                            <span class="stat-label">Successful Codes</span>
                                        </div>
                                    </div>

                                    <!-- Activity Countdown -->
                                    <div class="stat-item broker-countdown {{ $daysUntilInactive <= 7 ? 'warning' : '' }} {{ $isNewBroker ? 'new-broker' : '' }}">
                                        <div class="stat-icon">
                                            <i class="fas {{ $isNewBroker ? 'fa-seedling' : 'fa-clock' }}"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $daysUntilInactive }}</span>
                                            <span class="stat-label">
                                                @if($isNewBroker)
                                                    Days Grace Period
                                                @else
                                                    Days Until Inactive
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    @else
                                    <!-- Inactive Broker Status -->
                                    <div class="stat-item broker-inactive">
                                        <div class="stat-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">Inactive</span>
                                            <span class="stat-label">Broker Status</span>
                                        </div>
                                    </div>
                                    @endif
                                    @endif
                                    
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $user->created_at ? $user->created_at->format('F Y') : 'N/A' }}</span>
                                            <span class="stat-label">Member since</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Area -->
                    <div class="col-lg-8">
                        <!-- Email Verification Alert -->
                        @if(!$user->hasVerifiedEmail())
                        <div class="verification-alert">
                            <div class="alert-icon">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div class="alert-content">
                                <h4>Verify Your Email</h4>
                                <p>Please verify your email address to access all features and start selling.</p>
                                <a href="{{ route('user.verification.notice') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>Verify Email
                                </a>
                            </div>
                        </div>
                        @endif

                        @if($user->is_broker)
                        @if($isActiveBroker)
                        @if($isNewBroker)
                        <!-- New Broker Welcome -->
                        <div class="new-broker-welcome mb-4">
                            <div class="card border-success bg-light">
                                <div class="card-body">
                                    <div class="text-center">
                                        <div class="welcome-icon mb-3">
                                            <i class="fas fa-star text-success" style="font-size: 3rem;"></i>
                                        </div>
                                        <h4 class="text-success mb-3">🎉 Welcome New Representative!</h4>
                                        <p class="text-muted mb-3">
                                            <strong>مرحباً بك كمندوب جديد!</strong> لديك فترة سماح <strong>{{ $daysUntilInactive }} يوم</strong> لتتعلم النظام وتحقق أول عملية بيع ناجحة.
                                        </p>
                                        <div class="alert alert-warning mb-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>مهم:</strong> فترة السماح هذه <strong>لا تتجدد</strong> إلا عند إدخال كود ناجح (إتمام عملية بيع). إظهار الاهتمام بالإعلانات لا يجدد الفترة.
                                        </div>
                                        <div class="alert alert-info mb-3">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <strong>نصيحة:</strong> ابدأ بتصفح المنتجات وإظهار الاهتمام بالإعلانات، ثم اعمل على إقناع العملاء لإتمام عمليات الشراء!
                                        </div>
                                        <div class="welcome-actions">
                                            <a href="{{ route('representative') }}" class="btn btn-success me-2">
                                                <i class="fas fa-rocket me-2"></i>
                                                ابدأ الآن
                                            </a>
                                            <a href="{{ route('how-it-works') }}" class="btn btn-outline-info">
                                                <i class="fas fa-question-circle me-2"></i>
                                                كيف يعمل النظام؟
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Active Broker Quick Access -->
                        <div class="broker-quick-access mb-4">
                            <div class="card border-success">
                                <div class="card-body">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="broker-access-info">
                                            <h5 class="mb-1">
                                                <i class="fas fa-handshake text-success me-2"></i>
                                                Active Representative Dashboard
                                            </h5>
                                            <p class="text-muted mb-0">Access your broker tools and manage your interests</p>
                                            @if($daysUntilInactive <= 7)
                                            <div class="alert {{ $isNewBroker ? 'alert-info' : 'alert-warning' }} mt-2 mb-0 py-2">
                                                <i class="fas {{ $isNewBroker ? 'fa-info-circle' : 'fa-exclamation-triangle' }} me-2"></i>
                                                @if($isNewBroker)
                                                    <strong>New Broker:</strong> Your grace period expires in {{ $daysUntilInactive }} days. You must complete your first successful sale (enter a valid code) to remain active!
                                                @else
                                                    <strong>Warning:</strong> Your activity expires in {{ $daysUntilInactive }} days. Complete a sale to maintain active status.
                                                @endif
                                            </div>
                                            @endif
                                        </div>
                                        <div class="broker-access-actions">
                                            <a href="{{ route('representative') }}" class="btn btn-success">
                                                <i class="fas fa-external-link-alt me-2"></i>
                                                View Products
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @else
                        <!-- Inactive Broker Alert -->
                        <div class="broker-inactive-alert mb-4">
                            <div class="card border-danger">
                                <div class="card-body">
                                    <div class="text-center">
                                        <div class="inactive-icon mb-3">
                                            <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                                        </div>
                                        <h4 class="text-danger mb-3">Broker Account Inactive</h4>
                                        <p class="text-muted mb-3">
                                            لقد قمت بعدم النشاط لمدة 30 يوم ولم تقوم ببيع أي منتج (إدخال كود ناجح) وبالتالي تحولت إلى مندوب غير نشط.
                                            <br><strong>يجب عليك تجديد النشاط للوصول إلى أدوات المندوب.</strong>
                                        </p>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>لتجديد النشاط:</strong> قم بإدخال كود ناجح (إتمام عملية بيع واحدة) لاستعادة حالة المندوب النشط.
                                        </div>
                                        <div class="inactive-actions">
                                            <a href="{{ route('home') }}" class="btn btn-primary me-2">
                                                <i class="fas fa-home me-2"></i>
                                                العودة للرئيسية
                                            </a>
                                            <a href="{{ route('user.profile') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-user me-2"></i>
                                                عرض الملف الشخصي
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                        @endif

                        <!-- Identity Verification Card -->
                        @if(!$user->is_trusted)
                        <div class="verification-alert">
                            <div class="alert-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <div class="alert-content">
                                <h4>توثيق الهوية</h4>
                                <p>وثق هويتك للاستفادة من خدمات البرايم والمندوبين لتسويق منتجاتك</p>
                                <a href="{{ route('identity-verification.create') }}" class="btn btn-success">
                                    <i class="fas fa-id-card me-2"></i>توثيق الهوية
                                </a>
                            </div>
                        </div>
                        @endif

                        <!-- Recent Listings Card -->
                        <div class="listings-card">
                            <div class="card-header">
                                <h4 class="card-title">
                                    <i class="fas fa-bullhorn"></i>
                                    Recent Listings (Last 30 Days)
                                </h4>
                                <a href="{{ route('user.ads') }}" class="view-all-btn">View All</a>
                            </div>
                            <div class="card-body">
                                @if($recentAds->count() > 0)
                                    <div class="listings-grid">
                                        @foreach($recentAds as $ad)
                                        <div class="listing-item">
                                            <div class="listing-image">
                                                @if($ad->status && $ad->status->value == 1)
                                                    <a href="{{ route('auction-details', $ad->slug) }}" class="listing-link">
                                                        <img src="{{ $ad->featured_image ?? '/assets/images/placeholder.jpg' }}" alt="{{ $ad->title }}">
                                                        <div class="listing-overlay">
                                                            <i class="fas fa-eye"></i>
                                                            <span>View Listing</span>
                                                        </div>
                                                    </a>
                                                @else
                                                    <img src="{{ $ad->featured_image ?? '/assets/images/placeholder.jpg' }}" alt="{{ $ad->title }}">
                                                @endif
                                                <div class="listing-status {{ $ad->status->color() }}">
                                                    {{ $ad->status->label() }}
                                                </div>
                                            </div>
                                            <div class="listing-content">
                                                @if($ad->status && $ad->status->value == 1)
                                                    <h5 class="listing-title">
                                                        <a href="{{ route('auction-details', $ad->slug) }}" class="title-link">
                                                            {{  \Illuminate\Support\Str::limit($ad->title, 30) }}
                                                        </a>
                                                    </h5>
                                                @else
                                                    <h5 class="listing-title">{{  \Illuminate\Support\Str::limit($ad->title, 30) }}</h5>
                                                @endif
                                                <p class="listing-price">${{ number_format($ad->price) }}</p>
                                                <p class="listing-date">{{ $ad->created_at->diffForHumans() }}</p>

                                                <!-- Admin Note -->
                                                @if($ad->latestAdminNote)
                                                    <div class="admin-note-card mt-2">
                                                        <div class="admin-note-header">
                                                            <i class="fas fa-user-shield me-1"></i>
                                                            <span class="note-type-badge {{ $ad->latestAdminNote->type_color }}">
                                                                {{ $ad->latestAdminNote->type_name }}
                                                            </span>
                                                        </div>
                                                        <div class="admin-note-content">
                                                            <p class="note-text">{{ \Illuminate\Support\Str::limit($ad->latestAdminNote->note, 80) }}</p>
                                                            <small class="note-date">
                                                                {{ $ad->latestAdminNote->created_at->diffForHumans() }}
                                                            </small>
                                                        </div>
                                                    </div>
                                                @endif

                                                <!-- Ad Actions -->
                                                <div class="ad-actions mt-2">
                                                    @php
                                                        $isPublished = $ad->status && $ad->status->value == 1; // 1 = published
                                                        $canBoost = $ad->canBeBosted();
                                                        $boostTime = $ad->boost_available_in;
                                                    @endphp

                                                    <!-- Edit Button (for all ads) -->
                                                    <a href="{{ route('user.ads.edit.listing', $ad->slug) }}" class="btn btn-sm btn-primary edit-btn">
                                                        <i class="fas fa-edit me-1"></i>Edit
                                                    </a>

                                                    @if($isPublished)
                                                        @if($canBoost)
                                                            <form action="{{ route('user.ads.boost', $ad->slug) }}" method="POST" class="d-inline boost-form">
                                                                @csrf
                                                                <button type="submit" class="btn btn-sm btn-success boost-btn">
                                                                    <i class="fas fa-rocket me-1"></i>Boost Now
                                                                </button>
                                                            </form>
                                                        @else
                                                            <button class="btn btn-sm btn-secondary boost-btn" disabled
                                                                    title="Next boost available in {{ $boostTime }}">
                                                                <i class="fas fa-clock me-1"></i>Boost in {{ $boostTime }}
                                                            </button>
                                                        @endif
                                                    @endif

                                                    <form action="{{ route('user.ads.delete', $ad->slug) }}" method="POST" class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger delete-btn">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-bullhorn"></i>
                                        </div>
                                        <h5>No Recent Listings</h5>
                                        <p>You haven't posted any listings in the last 30 days.</p>
                                        <a href="{{ route('add-listing') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Create Your First Listing
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Recent Reviews Card -->
                        <div class="reviews-card">
                            <div class="card-header">
                                <h4 class="card-title">
                                    <i class="fas fa-star"></i>
                                    Recent Reviews
                                </h4>
                                @if($recentRatings->count() > 0)
                                    <a href="{{ route('users.rate', $user->id) }}" class="view-all-btn">View All</a>
                                @endif
                            </div>
                            <div class="card-body">
                                @if($recentRatings->count() > 0)
                                    <div class="reviews-list">
                                        @foreach($recentRatings as $rating)
                                        <div class="review-item">
                                            <div class="reviewer-info">
                                                <div class="reviewer-avatar-wrapper">
                                                    <img src="{{ $rating->rater->avatar ?? get_random_avatar() }}" alt="{{ $rating->rater->name }}" class="reviewer-avatar">
                                                    @if($rating->rater->email_verified_at)
                                                        <div class="verified-badge">
                                                            <i class="fas fa-check"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="reviewer-details">
                                                    <h6 class="reviewer-name">{{ $rating->rater->name }}</h6>
                                                    <div class="review-rating">
                                                        @for($i = 1; $i <= 5; $i++)
                                                            <i class="fas fa-star {{ $i <= $rating->rating ? 'filled' : '' }}"></i>
                                                        @endfor
                                                        <span class="rating-value">({{ $rating->rating }}/5)</span>
                                                    </div>
                                                </div>
                                                <div class="review-date">
                                                    <span>{{ $rating->created_at->diffForHumans() }}</span>
                                                </div>
                                            </div>
                                            @if($rating->comment)
                                            <div class="review-content">
                                                <p>"{{ $rating->comment }}"</p>
                                            </div>
                                            @endif
                                        </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <h5>No Reviews Yet</h5>
                                        <p>You haven't received any reviews yet. Start selling to get your first review!</p>
                                        <a href="{{ route('add-listing') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus me-2"></i>Create Your First Ad
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        @if($user->is_broker && $isActiveBroker)
                        <!-- Broker Interests Section - Moved to top -->
                        <div class="broker-interests-section mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">
                                        <i class="fas fa-heart text-danger"></i>
                                        My Interests
                                        <span class="badge bg-primary ms-2">{{ $interestedAds->count() }}</span>
                                    </h4>
                                </div>
                                <div class="card-body">
                                    @if($interestedAds->count() > 0)
                                        <div class="interests-list">
                                            @foreach($interestedAds as $interest)
                                                <div class="interest-item">
                                                    <div class="interest-image">
                                                        @if($interest->ad && $interest->ad->media->first())
                                                            <img src="{{ $interest->ad->media->first()->url }}" alt="{{ $interest->ad->title ?? 'Deleted Ad' }}">
                                                        @else
                                                            <div class="no-image">
                                                                <i class="fas fa-image"></i>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div class="interest-info">
                                                        @if($interest->ad)
                                                            <h6 class="interest-title">
                                                                <a href="{{ route('auction-details', $interest->ad->slug) }}" target="_blank">
                                                                    {{ \Illuminate\Support\Str::limit($interest->ad->title, 40) }}
                                                                </a>
                                                            </h6>
                                                            <p class="interest-price">{{ money($interest->ad->price) }}</p>
                                                            <div class="interest-meta">
                                                                <span class="points-paid">
                                                                    <i class="fas fa-coins text-warning"></i>
                                                                    {{ $interest->points_paid }} points paid
                                                                </span>
                                                                <span class="interest-date">{{ $interest->created_at->diffForHumans() }}</span>
                                                            </div>
                                                        @else
                                                            <div class="deleted-ad-notice">
                                                                <h6 class="text-muted">
                                                                    <i class="fas fa-trash text-danger me-2"></i>
                                                                    Ad has been deleted
                                                                </h6>
                                                                <p class="text-muted mb-1">Your points will be refunded</p>
                                                                <div class="interest-meta">
                                                                    <span class="points-paid">
                                                                        <i class="fas fa-coins text-warning"></i>
                                                                        {{ $interest->points_paid }} points (refunding)
                                                                    </span>
                                                                    <span class="interest-date">{{ $interest->created_at->diffForHumans() }}</span>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div class="interest-actions">
                                                        @if($interest->ad)
                                                            <span class="badge bg-success">Active</span>
                                                        @else
                                                            <span class="badge bg-danger">Deleted</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="empty-state">
                                            <div class="empty-icon">
                                                <i class="fas fa-heart"></i>
                                            </div>
                                            <h5>No Interested Ads</h5>
                                            <p>You haven't shown interest in any ads yet.</p>
                                            <a href="{{ route('representative') }}" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>Browse Products
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Successful Codes Section -->
                        <div class="successful-codes-section mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">
                                        <i class="fas fa-check-circle text-success"></i>
                                        Recent Successful Codes (Last 30 Days)
                                        <span class="badge bg-success ms-2">{{ $recentSuccessfulCodes->count() }}</span>
                                    </h4>
                                </div>
                                <div class="card-body">
                                    @if($recentSuccessfulCodes->count() > 0)
                                        <div class="codes-list">
                                            @foreach($recentSuccessfulCodes as $brokerInterest)
                                                <div class="code-item">
                                                    <div class="code-icon">
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    </div>
                                                    <div class="code-info">
                                                        @if($brokerInterest->ad)
                                                            <h6 class="code-title">{{ \Illuminate\Support\Str::limit($brokerInterest->ad->title, 50) }}</h6>
                                                            <p class="code-price">{{ money($brokerInterest->ad->price) }}</p>
                                                        @else
                                                            <h6 class="code-title text-muted">Ad no longer available</h6>
                                                            <p class="code-price text-muted">Price not available</p>
                                                        @endif
                                                        <div class="code-meta">
                                                            <span class="code-date">
                                                                <i class="fas fa-calendar"></i>
                                                                {{ $brokerInterest->code_entered_at->format('M d, Y') }}
                                                            </span>
                                                            <span class="code-time">{{ $brokerInterest->code_entered_at->diffForHumans() }}</span>
                                                            <span class="points-earned">
                                                                <i class="fas fa-coins text-warning"></i>
                                                                {{ $brokerInterest->points_earned }} points earned
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="code-status">
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-trophy me-1"></i>
                                                            Successful
                                                        </span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="empty-state">
                                            <div class="empty-icon">
                                                <i class="fas fa-code"></i>
                                            </div>
                                            <h5>No Successful Codes Yet</h5>
                                            <p>You haven't entered any successful codes in the last 30 days.</p>
                                            <a href="{{ route('representative') }}" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>Find Opportunities
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Broker Section -->
                        @if($user->is_broker)


                        @else
                            <!-- Become Broker Card -->
                            <div class="become-broker-card">
                                <div class="broker-hero">
                                    <div class="broker-icon">
                                        <i class="fas fa-handshake"></i>
                                    </div>
                                    <h4 class="broker-title">Become a Representative</h4>
                                    <p class="broker-subtitle">Start earning money by helping others buy and sell</p>
                                </div>
                                <div class="broker-benefits">
                                    <div class="benefit-item">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>Earn commission on every deal</span>
                                    </div>
                                    <div class="benefit-item">
                                        <i class="fas fa-network-wired"></i>
                                        <span>Build your professional network</span>
                                    </div>
                                    <div class="benefit-item">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Grow your business</span>
                                    </div>
                                </div>
                                <div class="broker-explanation">
                                    <h5>How it works:</h5>
                                    <ol>
                                        <li>Submit your representative application</li>
                                        <li>Get verified by our team</li>
                                        <li>Start connecting buyers and sellers</li>
                                        <li>Earn commission on successful deals</li>
                                    </ol>
                                </div>
                                <div class="broker-action">
                                    <a href="{{ route('broker-application.create') }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-file-alt me-2"></i>
                                        Apply to Become Representative
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Hero Section */
.profile-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0 2rem;
    position: relative;
    overflow: hidden;
}

.profile-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.hero-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.hero-breadcrumb .breadcrumb-item.active {
    color: white;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Dashboard Navigation Tabs */
.dashboard-nav-section {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.nav-tabs-wrapper {
    background: white;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dashboard-tabs {
    border: none;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.dashboard-tabs .nav-item {
    margin: 0;
}

.dashboard-tabs .nav-link {
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    color: #6c757d;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.dashboard-tabs .nav-link:hover {
    background: #f8f9fa;
    color: #495057;
}

.dashboard-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.dashboard-tabs .logout-btn {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
}

.dashboard-tabs .logout-btn:hover {
    background: #dc3545;
    color: white;
}

/* Main Section */
.profile-main-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

/* User Info Card */
.user-info-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    text-align: center;
}

.user-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.user-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.verified-badge-blue {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
}

.unverified-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
}

.user-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.user-badges {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.badge-broker {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.badge-rank {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
}

.badge-verified {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.badge-unverified {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}

.badge-level {
    background: linear-gradient(135deg, #17a2b8, #6610f2);
    color: white;
}

.user-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-content {
    flex: 1;
    text-align: left;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    display: block;
}

.stat-label {
    font-size: 0.85rem;
    color: #718096;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars .fa-star {
    font-size: 0.8rem;
    color: #e2e8f0;
}

.stars .fa-star.filled {
    color: #fbbf24;
}

/* Premium Rank Cards */
.premium-rank-card {
    margin: 1.5rem 0;
    border-radius: 16px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 2px solid transparent;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.premium-rank-card.vip-member {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #f59e0b 100%);
    border-color: #d97706;
    color: #92400e;
}

.premium-rank-card.trader {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #1d4ed8 100%);
    border-color: #1e40af;
    color: white;
}

.premium-rank-card.company {
    background: linear-gradient(135deg, #10b981 0%, #34d399 50%, #**********%);
    border-color: #047857;
    color: white;
}

.premium-rank-card.premium {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 50%, #7c3aed 100%);
    border-color: #6d28d9;
    color: white;
}

.premium-rank-card.regular {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 50%, #4b5563 100%);
    border-color: #374151;
    color: white;
}

.rank-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.rank-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.rank-title {
    font-size: 1rem;
    font-weight: 700;
    letter-spacing: 1px;
    flex: 1;
}

.rank-sparkles {
    display: flex;
    gap: 0.25rem;
    opacity: 0.8;
}

.rank-sparkles i {
    font-size: 0.8rem;
    animation: sparkle 2s ease-in-out infinite;
}

.rank-sparkles i:nth-child(2) {
    animation-delay: 0.5s;
}

.rank-sparkles i:nth-child(3) {
    animation-delay: 1s;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.rank-description {
    text-align: left;
}

.rank-subtitle {
    font-size: 0.85rem;
    opacity: 0.9;
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.rank-features {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.rank-features span {
    font-size: 0.75rem;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rank-features i {
    font-size: 0.7rem;
    width: 12px;
}

/* VIP Member specific styles */
.premium-rank-card.vip-member .rank-header {
    position: relative;
}

.premium-rank-card.vip-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706, #f59e0b, #fbbf24);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Content Cards */
.listings-card,
.reviews-card,
.broker-deals-card,
.interested-ads-card,
.become-broker-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-all-btn {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
}

.view-all-btn:hover {
    color: #764ba2;
}

.card-body {
    padding: 2rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.empty-state h5 {
    color: #2d3748;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.empty-state p {
    color: #718096;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.empty-state .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

/* Listings Grid */
.listings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.listing-item {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.listing-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.listing-image {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.listing-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.listing-status {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.listing-status.info {
    background: #17a2b8;
    color: white;
}

.listing-status.warning {
    background: #ffc107;
    color: #212529;
}

.listing-status.danger {
    background: #dc3545;
    color: white;
}

.listing-status.secondary {
    background: #6c757d;
    color: white;
}

.listing-status.dark {
    background: #343a40;
    color: white;
}

.listing-content {
    padding: 1rem;
}

.listing-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.listing-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.listing-date {
    font-size: 0.85rem;
    color: #718096;
    margin: 0;
}

/* Reviews List */
.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.review-item {
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    background: #fafbfc;
    transition: all 0.3s ease;
}

.review-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.reviewer-info {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.reviewer-avatar-wrapper {
    position: relative;
    flex-shrink: 0;
}

.reviewer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.verified-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
}

.verified-badge i {
    font-size: 0.6rem;
    color: white;
}

.reviewer-details {
    flex: 1;
}

.reviewer-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.review-rating .fa-star {
    font-size: 0.85rem;
    color: #e2e8f0;
}

.review-rating .fa-star.filled {
    color: #fbbf24;
}

.rating-value {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.review-date {
    margin-left: auto;
    text-align: right;
    flex-shrink: 0;
}

.review-date span {
    font-size: 0.8rem;
    color: #718096;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.review-content {
    margin-top: 0.5rem;
    padding-left: 66px; /* Align with reviewer details */
}

.review-content p {
    color: #4a5568;
    font-style: italic;
    margin: 0;
    line-height: 1.5;
    background: white;
    padding: 0.75rem;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

/* Broker Cards */
.deals-list,
.interested-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.deal-item,
.interested-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.deal-title,
.interested-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.deal-price,
.interested-price {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.deal-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.deal-status.pending {
    background: #ffc107;
    color: #212529;
}

.deal-status.completed {
    background: #28a745;
    color: white;
}

.deal-status.interested {
    background: #17a2b8;
    color: white;
}

.deal-date,
.interested-date {
    font-size: 0.85rem;
    color: #718096;
}

.interested-item {
    align-items: flex-start;
}

.interested-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 1rem;
}

.interested-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.interested-info {
    flex: 1;
}

/* Become Broker Card */
.become-broker-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.broker-hero {
    text-align: center;
    padding: 2rem;
}

.broker-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
}

.broker-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.broker-subtitle {
    opacity: 0.9;
    margin: 0;
}

.broker-benefits {
    padding: 0 2rem;
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    opacity: 0.9;
}

.broker-explanation {
    padding: 0 2rem;
    margin-bottom: 2rem;
}

.broker-explanation h5 {
    margin-bottom: 1rem;
}

.broker-explanation ol {
    padding-left: 1.5rem;
}

.broker-explanation li {
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.broker-action {
    padding: 0 2rem 2rem;
    text-align: center;
}

.btn-primary {
    background: white;
    color: #667eea;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    color: #667eea;
}

/* Verification Alert */
.verification-alert {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #d63031;
}

.alert-content h4 {
    color: #2d3436;
    margin-bottom: 0.5rem;
}

.alert-content p {
    color: #636e72;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-tabs {
        flex-direction: column;
    }

    .dashboard-tabs .nav-link {
        justify-content: center;
    }

    .user-info-card {
        padding: 1.5rem;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
    }

    .listings-grid {
        grid-template-columns: 1fr;
    }

    .deal-item,
    .interested-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .profile-main-section {
        padding: 1rem 0;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .user-badges {
        gap: 0.25rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
    }

    .verification-alert {
        flex-direction: column;
        text-align: center;
    }

    .nav-tabs-wrapper {
        padding: 0.25rem;
    }

    .dashboard-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .review-item {
        padding: 1rem;
    }

    .reviewer-avatar {
        width: 40px;
        height: 40px;
    }

    .verified-badge {
        width: 16px;
        height: 16px;
    }

    .verified-badge i {
        font-size: 0.5rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .reviewer-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 0.75rem;
    }

    .review-date {
        margin-left: 0;
        text-align: center;
    }

    .review-content {
        padding-left: 0;
        margin-top: 1rem;
    }

    .reviewer-details {
        text-align: center;
    }
}

/* Admin Impersonation Alert */
.admin-impersonation-alert {
    background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
    border-bottom: 3px solid #e55a2b;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
}

.admin-impersonation-alert .alert {
    background: transparent !important;
    border: none !important;
    color: white !important;
    margin: 0 !important;
    padding: 1rem 0 !important;
}

.admin-impersonation-alert .alert strong {
    color: #fff3e0;
}

.admin-impersonation-alert .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

.admin-impersonation-alert .btn-outline-dark {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.admin-impersonation-alert .btn-outline-dark:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Admin Notes Styles */
.admin-note-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.admin-note-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.note-type-badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
}

.note-type-badge.info {
    background: #d1ecf1;
    color: #0c5460;
}

.note-type-badge.danger {
    background: #f8d7da;
    color: #721c24;
}

.note-type-badge.secondary {
    background: #e2e3e5;
    color: #383d41;
}

.admin-note-content .note-text {
    margin: 0;
    font-size: 0.85rem;
    color: #495057;
    line-height: 1.4;
}

.admin-note-content .note-date {
    color: #6c757d;
    font-size: 0.75rem;
}

.listing-item:has(.admin-note-card) {
    border-left: 3px solid #007bff;
}

/* Ad Actions Styles */
.ad-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.boost-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
}

.boost-btn:not(:disabled) {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.boost-btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.boost-btn:disabled {
    background: #6c757d;
    border: none;
    color: white;
    cursor: not-allowed;
}

.delete-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.delete-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    background: linear-gradient(45deg, #c82333, #bd2130);
}

.listing-item {
    position: relative;
    transition: all 0.3s ease;
}

.listing-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Listing Link Styles */
.listing-link {
    position: relative;
    display: block;
    text-decoration: none;
}

.listing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.listing-link:hover .listing-overlay {
    opacity: 1;
}

.listing-overlay i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.listing-overlay span {
    font-weight: 600;
    font-size: 0.9rem;
}

.title-link {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.title-link:hover {
    color: #3498db;
    text-decoration: none;
}

/* Edit Button Styles */
.edit-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.edit-btn:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    color: white;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation
    const deleteForms = document.querySelectorAll('.delete-form');
    deleteForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this ad? This action cannot be undone.')) {
                this.submit();
            }
        });
    });

    // Boost countdown timer
    function updateBoostTimers() {
        const disabledBoostBtns = document.querySelectorAll('.boost-btn:disabled');

        disabledBoostBtns.forEach(btn => {
            const title = btn.getAttribute('title');
            if (title && title.includes('Next boost available in')) {
                // Extract time from title and update countdown
                // This is a simplified version - you might want to implement a more sophisticated countdown
                const timeMatch = title.match(/(\d+)h (\d+)m/);
                if (timeMatch) {
                    let hours = parseInt(timeMatch[1]);
                    let minutes = parseInt(timeMatch[2]);

                    // Update every minute
                    const interval = setInterval(() => {
                        minutes--;
                        if (minutes < 0) {
                            hours--;
                            minutes = 59;
                        }

                        if (hours <= 0 && minutes <= 0) {
                            // Time's up - reload page to show boost button
                            location.reload();
                            clearInterval(interval);
                        } else {
                            // Update button text
                            const newText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
                            btn.innerHTML = `<i class="fas fa-clock me-1"></i>Boost in ${newText}`;
                            btn.setAttribute('title', `Next boost available in ${newText}`);
                        }
                    }, 60000); // Update every minute
                }
            }
        });
    }

    // Initialize boost timers
    updateBoostTimers();

    // Boost form submission
    const boostForms = document.querySelectorAll('.boost-form');
    boostForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Boost this ad? It will appear at the top of listings and you can boost again in 48 hours.')) {
                const btn = this.querySelector('.boost-btn');
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Boosting...';
                btn.disabled = true;

                // Submit the form
                this.submit();
            }
        });
    });
});
</script>
@endpush

@endsection

@push('styles')
<style>
    /* Broker Dashboard Styles */
    .broker-points .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .broker-interests .stat-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .broker-codes .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .broker-countdown .stat-icon {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }

    .broker-countdown.warning .stat-icon {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        animation: pulse 2s infinite;
    }

    .broker-countdown.new-broker .stat-icon {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        animation: glow 3s ease-in-out infinite alternate;
    }

    .broker-inactive .stat-icon {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(168, 237, 234, 0.5); }
        100% { box-shadow: 0 0 20px rgba(254, 214, 227, 0.8), 0 0 30px rgba(168, 237, 234, 0.6); }
    }

    .broker-quick-access .card {
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-radius: 15px;
    }

    .broker-interests-section .card,
    .successful-codes-section .card {
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-radius: 15px;
    }

    .interest-item,
    .code-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .interest-item:hover,
    .code-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .interest-image,
    .code-icon {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        overflow: hidden;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .interest-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .interest-image .no-image {
        background: #f8f9fa;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .code-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        font-size: 24px;
    }

    .interest-info,
    .code-info {
        flex: 1;
    }

    .interest-title,
    .code-title {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .interest-title a {
        color: #2c3e50;
        text-decoration: none;
    }

    .interest-title a:hover {
        color: #3498db;
    }

    .interest-price,
    .code-price {
        color: #27ae60;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .interest-meta,
    .code-meta {
        display: flex;
        gap: 15px;
        font-size: 0.85rem;
        color: #6c757d;
    }

    .points-paid {
        color: #f39c12 !important;
    }

    .deleted-ad-notice {
        background: #fff3cd;
        padding: 10px;
        border-radius: 8px;
        border-left: 4px solid #ffc107;
    }

    .interest-actions,
    .code-status {
        margin-left: 15px;
    }
</style>
@endpush
