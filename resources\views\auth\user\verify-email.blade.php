@extends('partials.app')

@section('content')
<div class="verify-email-container">
    <div class="container">
        <div class="row justify-content-center min-vh-100 align-items-center">
            <div class="col-lg-6 col-md-8">
                <div class="verify-email-card">
                    <!-- Header Section -->
                    <div class="verify-header">
                        <div class="verify-icon-container">
                            <div class="verify-icon">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div class="verify-checkmark">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <h2 class="verify-title">تحقق من بريدك الإلكتروني</h2>
                        <p class="verify-subtitle">لقد أرسلنا رابط التحقق إلى بريدك الإلكتروني</p>
                    </div>

                    <!-- Success Message -->
                    @if (session('success'))
                        <div class="success-message">
                            <div class="success-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="success-text">
                                {{ session('success') }}
                            </div>
                        </div>
                    @endif

                    <!-- Email Info -->
                    <div class="email-info">
                        <div class="email-address">
                            <i class="fas fa-at"></i>
                            <span>{{ auth()->user()->email }}</span>
                        </div>
                        <p class="email-instruction">
                            افتح بريدك الإلكتروني واضغط على رابط التحقق لتفعيل حسابك
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="verify-actions">
                        <div class="resend-section">
                            <p class="resend-text">لم تستلم الرسالة؟</p>
                            <form method="POST" action="{{ route('user.verification.send') }}" class="resend-form">
                                @csrf
                                <button type="submit" class="btn-resend">
                                    <i class="fas fa-paper-plane"></i>
                                    <span>إعادة إرسال الرسالة</span>
                                </button>
                            </form>
                        </div>

                        <div class="navigation-section">
                            <a href="{{ route('user.dashboard') }}" class="btn-dashboard">
                                <i class="fas fa-home"></i>
                                <span>الذهاب للوحة التحكم</span>
                            </a>
                        </div>
                    </div>

                    <!-- Tips Section -->
                    <div class="tips-section">
                        <div class="tip-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>تحقق من مجلد الرسائل المزعجة (Spam)</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-clock"></i>
                            <span>قد تستغرق الرسالة بضع دقائق للوصول</span>
                        </div>
                        <div class="tip-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>التحقق مطلوب لضمان أمان حسابك</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@push('styles')
<style>
.verify-email-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.verify-email-card {
    background: white;
    border-radius: 24px;
    padding: 3rem 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.verify-email-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 300% 100%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Header Section */
.verify-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.verify-icon-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.verify-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    animation: float 3s ease-in-out infinite;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.verify-checkmark {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 35px;
    height: 35px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    border: 3px solid white;
    animation: bounce 2s infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.verify-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.verify-subtitle {
    font-size: 1.1rem;
    color: #718096;
    margin: 0;
}

/* Success Message */
.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.success-icon {
    color: #155724;
    font-size: 1.2rem;
}

.success-text {
    color: #155724;
    font-weight: 500;
}

/* Email Info */
.email-info {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

.email-address {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.email-address i {
    color: #667eea;
}

.email-instruction {
    color: #718096;
    margin: 0;
    line-height: 1.6;
}

/* Action Buttons */
.verify-actions {
    margin-bottom: 2rem;
}

.resend-section {
    text-align: center;
    margin-bottom: 1.5rem;
}

.resend-text {
    color: #718096;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.resend-form {
    margin-bottom: 1rem;
}

.btn-resend {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
}

.btn-resend:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.navigation-section {
    text-align: center;
}

.btn-dashboard {
    background: #6c757d;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-dashboard:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.4);
    color: white;
}

/* Tips Section */
.tips-section {
    border-top: 1px solid #e2e8f0;
    padding-top: 1.5rem;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #718096;
    font-size: 0.9rem;
}

.tip-item i {
    color: #667eea;
    width: 16px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .verify-email-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }

    .verify-title {
        font-size: 1.5rem;
    }

    .verify-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .verify-checkmark {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }

    .btn-resend,
    .btn-dashboard {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }
}
</style>
@endpush

@endsection
