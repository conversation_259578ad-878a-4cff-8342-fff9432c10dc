<?php

namespace App\Http\Controllers\User\Auth;

use App\Contracts\Repositories\AuthenticateRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;

class LoginController extends Controller
{
    protected const GUARD = 'web';

    /** 
     * Instantiate a new controller instance.
     * @param AuthenticateRepositoryInterface $repository
     */
    public function __construct(protected AuthenticateRepositoryInterface $repository)
    {
        $this->repository = $repository;
        $this->middleware('guest')->except('logout');
    }

    /**
     * Login a user.
     *
     * @param LoginRequest $request
     * @return RedirectResponse
     */
    public function login(LoginRequest $request): RedirectResponse
    {
        $this->repository->login($request->validated(), self::GUARD);

        // التحقق من الإيميل باستخدام النظام الرسمي
        $user = $this->repository->user();
        if ($user && !$user->hasVerifiedEmail()) {
            return redirect()->route('user.verification.notice');
        }

        return redirect()->intended(route('user.dashboard'));
    }

    /**
     * Logout a user.
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function logout(Request $request): RedirectResponse
    {
        $this->repository->logout($request, self::GUARD);

        return redirect()->route('user.login');
    }
}
