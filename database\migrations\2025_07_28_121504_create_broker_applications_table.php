<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('broker_applications', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_id');
            $table->string('full_name');
            $table->text('education');
            $table->text('experience');
            $table->text('additional_notes')->nullable();
            $table->enum('status', ['pending', 'first_stage_accepted', 'second_stage_accepted', 'rejected'])->default('pending');
            $table->text('rejection_reason')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->json('documents')->nullable(); // للمستندات في المرحلة الثانية
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('broker_applications');
    }
};
