<?php

namespace App\Http\Controllers;

use App\Models\IdentityVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\SimpleFileCompressionService;

class IdentityVerificationController extends Controller
{
    /**
     * عرض صفحة توثيق الهوية
     */
    public function create(): View
    {
        $user = auth()->user();

        // التحقق من وجود طلب سابق
        $existingVerification = IdentityVerification::where('user_id', $user->id)->first();

        return view('identity-verification.create', compact('existingVerification'));
    }

    /**
     * حفظ طلب توثيق الهوية
     */
    public function store(Request $request): RedirectResponse
    {
        $user = auth()->user();

        // التحقق من عدم وجود طلب سابق أو إمكانية التقديم مرة أخرى
        $existingVerification = IdentityVerification::where('user_id', $user->id)->first();

        if ($existingVerification && !$existingVerification->canReapply()) {
            $waitDays = 5 - now()->diffInDays($existingVerification->rejected_at);
            return back()->with('error', "لا يمكنك التقديم مرة أخرى إلا بعد {$waitDays} أيام");
        }

        $request->validate([
            'national_id' => 'required|string|size:14|regex:/^[0-9]{14}$/',
            'front_image' => 'required|image|mimes:jpeg,png,jpg|max:5120',
            'back_image' => 'required|image|mimes:jpeg,png,jpg|max:5120',
        ], [
            'national_id.required' => 'الرقم القومي مطلوب',
            'national_id.size' => 'الرقم القومي يجب أن يكون 14 رقم',
            'national_id.regex' => 'الرقم القومي يجب أن يحتوي على أرقام فقط',
            'front_image.required' => 'صورة البطاقة الأمامية مطلوبة',
            'front_image.image' => 'يجب أن تكون صورة صحيحة',
            'front_image.max' => 'حجم الصورة يجب ألا يزيد عن 5 ميجابايت',
            'back_image.required' => 'صورة البطاقة الخلفية مطلوبة',
            'back_image.image' => 'يجب أن تكون صورة صحيحة',
            'back_image.max' => 'حجم الصورة يجب ألا يزيد عن 5 ميجابايت',
        ]);

        // حذف الطلب السابق إذا كان مرفوضاً ويمكن التقديم مرة أخرى
        if ($existingVerification && $existingVerification->canReapply()) {
            // حذف الصور القديمة
            if ($existingVerification->front_image) {
                Storage::disk('public')->delete($existingVerification->front_image);
            }
            if ($existingVerification->back_image) {
                Storage::disk('public')->delete($existingVerification->back_image);
            }
            $existingVerification->delete();
        }

        // ضغط ورفع الصور
        $compressionService = new SimpleFileCompressionService();
        $settings = $compressionService->getOptimizedSettings('identity_documents');

        $frontResult = $compressionService->compressAndStore($request->file('front_image'), 'identity-verification', $settings);
        $backResult = $compressionService->compressAndStore($request->file('back_image'), 'identity-verification', $settings);

        if (!$frontResult['success'] || !$backResult['success']) {
            return back()->withErrors(['images' => 'فشل في معالجة الصور. يرجى المحاولة مرة أخرى.']);
        }

        $frontImage = $frontResult['path'];
        $backImage = $backResult['path'];

        IdentityVerification::create([
            'user_id' => $user->id,
            'national_id' => $request->national_id,
            'front_image' => $frontImage,
            'back_image' => $backImage,
            'status' => IdentityVerification::STATUS_PENDING,
        ]);

        return redirect()->route('identity-verification.create')
                        ->with('success', 'تم إرسال طلب توثيق الهوية بنجاح! سيتم مراجعته قريباً.');
    }
}
