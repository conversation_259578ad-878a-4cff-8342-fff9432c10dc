<?php

namespace App\Http\Controllers;

use App\Models\IdentityVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\SimpleFileCompressionService;

class IdentityVerificationController extends Controller
{
    /**
     * عرض صفحة توثيق الهوية
     */
    public function create(): View
    {
        $user = auth()->user();

        // التحقق من وجود طلب سابق
        $existingVerification = IdentityVerification::where('user_id', $user->id)->first();

        return view('identity-verification.create', compact('existingVerification'));
    }

    /**
     * حفظ طلب توثيق الهوية
     */
    public function store(Request $request): RedirectResponse
    {
        $user = auth()->user();

        // التحقق من عدم وجود طلب سابق أو إمكانية التقديم مرة أخرى
        $existingVerification = IdentityVerification::where('user_id', $user->id)->first();

        if ($existingVerification && !$existingVerification->canReapply()) {
            $waitDays = 5 - now()->diffInDays($existingVerification->rejected_at);
            return back()->with('error', "لا يمكنك التقديم مرة أخرى إلا بعد {$waitDays} أيام");
        }

        $request->validate([
            'national_id' => 'required|string|size:14|regex:/^[0-9]{14}$/',
            'front_image' => 'required|image|mimes:jpeg,png,jpg|max:5120',
            'back_image' => 'required|image|mimes:jpeg,png,jpg|max:5120',
        ], [
            'national_id.required' => 'الرقم القومي مطلوب',
            'national_id.size' => 'الرقم القومي يجب أن يكون 14 رقم',
            'national_id.regex' => 'الرقم القومي يجب أن يحتوي على أرقام فقط',
            'front_image.required' => 'صورة البطاقة الأمامية مطلوبة',
            'front_image.image' => 'يجب أن تكون صورة صحيحة',
            'front_image.max' => 'حجم الصورة يجب ألا يزيد عن 5 ميجابايت',
            'back_image.required' => 'صورة البطاقة الخلفية مطلوبة',
            'back_image.image' => 'يجب أن تكون صورة صحيحة',
            'back_image.max' => 'حجم الصورة يجب ألا يزيد عن 5 ميجابايت',
        ]);

        // حذف الطلب السابق إذا كان مرفوضاً ويمكن التقديم مرة أخرى
        if ($existingVerification && $existingVerification->canReapply()) {
            // حذف الصور القديمة
            if ($existingVerification->front_image) {
                Storage::disk('public')->delete($existingVerification->front_image);
            }
            if ($existingVerification->back_image) {
                Storage::disk('public')->delete($existingVerification->back_image);
            }
            $existingVerification->delete();
        }

        // Ultra compress identity documents for maximum size reduction while maintaining readability
        $compressionService = new SimpleFileCompressionService();
        $advancedOptimizer = new \App\Services\AdvancedImageOptimizer();

        // Ultra compression settings for identity documents
        $ultraSettings = [
            'target_size_kb' => 200,  // 200KB max for identity documents
            'min_quality' => 50,      // Higher quality for document readability
            'max_width' => 1200,
            'max_height' => 900,
            'format' => 'webp',
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => false, // Keep colors for document clarity
            'blur_threshold' => 0.1     // Minimal blur for text clarity
        ];

        // Compress front image
        $frontResult = $advancedOptimizer->ultraCompress($request->file('front_image'), $ultraSettings);
        if (!$frontResult['success']) {
            return back()->withErrors(['front_image' => 'فشل في معالجة صورة البطاقة الأمامية. يرجى المحاولة مرة أخرى.']);
        }

        // Compress back image
        $backResult = $advancedOptimizer->ultraCompress($request->file('back_image'), $ultraSettings);
        if (!$backResult['success']) {
            return back()->withErrors(['back_image' => 'فشل في معالجة صورة البطاقة الخلفية. يرجى المحاولة مرة أخرى.']);
        }

        // Store compressed images
        $frontUuid = \Illuminate\Support\Str::uuid();
        $backUuid = \Illuminate\Support\Str::uuid();
        $frontFilename = $frontUuid . '.webp';
        $backFilename = $backUuid . '.webp';
        $frontPath = 'identity-verification/' . $frontFilename;
        $backPath = 'identity-verification/' . $backFilename;

        Storage::disk('public')->put($frontPath, $frontResult['compressed_data']);
        Storage::disk('public')->put($backPath, $backResult['compressed_data']);

        $frontImage = $frontPath;
        $backImage = $backPath;

        // Log compression results
        \Illuminate\Support\Facades\Log::info('Identity documents ultra compressed successfully', [
            'user_id' => $user->id,
            'front_image' => [
                'original_size' => $compressionService->formatFileSize($frontResult['original_size']),
                'compressed_size' => $compressionService->formatFileSize($frontResult['compressed_size']),
                'compression_ratio' => $frontResult['compression_ratio'] . '%',
                'final_dimensions' => $frontResult['final_dimensions']
            ],
            'back_image' => [
                'original_size' => $compressionService->formatFileSize($backResult['original_size']),
                'compressed_size' => $compressionService->formatFileSize($backResult['compressed_size']),
                'compression_ratio' => $backResult['compression_ratio'] . '%',
                'final_dimensions' => $backResult['final_dimensions']
            ]
        ]);

        IdentityVerification::create([
            'user_id' => $user->id,
            'national_id' => $request->national_id,
            'front_image' => $frontImage,
            'back_image' => $backImage,
            'status' => IdentityVerification::STATUS_PENDING,
        ]);

        return redirect()->route('identity-verification.create')
                        ->with('success', 'تم إرسال طلب توثيق الهوية بنجاح! سيتم مراجعته قريباً.');
    }
}
