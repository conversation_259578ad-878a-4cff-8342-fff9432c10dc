<?php

namespace App\Repositories\Ad\Admin;

use Illuminate\Support\Facades\Log;
use App\Abstracts\BaseCrudRepository;
use App\Models\Ad;
use App\Models\User;
use App\Contracts\Repositories\AdminAdRepositoryInterface;
use App\Enums\AdStatus;
use App\Exceptions\AdException;
use App\Models\ReportAd;
use App\Repositories\Category\CategoryRepository;
use App\Helpers\SecurityHelper;
use App\Traits\MediaHandler;
use App\Models\AdAttribute;
use App\Models\AdminNote;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AdminAdRepository extends BaseCrudRepository implements AdminAdRepositoryInterface
{
    use MediaHandler;

    public function __construct(Ad $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all ads (excluding sponsored ads)
     *
     * @param int $limit
     * @param string $type = 'all' <all|active|upcoming|pending|expired|rejected>
     * @param array $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAds(int $limit = 10, string $type, array $filters = null): LengthAwarePaginator
    {
        return $this->model->query()->with(['user:id,name,avatar,username,rank', 'media', 'category:id,name', 'attributes:id,ad_id,attribute_name,attribute_label,attribute_value'])
            // استبعاد الإعلانات المرعية
            ->whereDoesntHave('sponsoredAd')
            ->when(
                match ($type) {
                    'active' => fn ($query) => $query->active(),
                    'upcoming' => fn ($query) => $query->upcoming(),
                    'pending' => fn ($query) => $query->pending(),
                    'expired' => fn ($query) => $query->expired(),
                    'rejected' => fn ($query) => $query->rejected(),
                    'all' => fn ($query) => $query,
                }
            )
            ->when($filters, function ($query) use ($filters) {
                $query->when(isset($filters['search']), function ($query) use ($filters) {
                    $query->where('title', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('id', $filters['search'])
                        ->orWhere('user_id', $filters['search']);
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit)
            ->appends(['search' => $filters['search'] ?? null]);
    }

    /**
     * Get ads by user rank and status (excluding sponsored ads)
     *
     * @param string $userType = 'regular' <regular|vip>
     * @param string $status = 'all' <all|pending>
     * @param int $limit
     * @param array $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAdsByUserType(string $userType, string $status = 'all', int $limit = 10, array $filters = null): LengthAwarePaginator
    {
        return $this->model->query()
            ->with(['user:id,name,avatar,username,rank', 'media', 'category:id,name', 'attributes:id,ad_id,attribute_name,attribute_label,attribute_value'])
            // استبعاد الإعلانات المرعية
            ->whereDoesntHave('sponsoredAd')
            ->whereHas('user', function ($query) use ($userType) {
                if ($userType === 'regular') {
                    $query->where('rank', 0);
                } elseif ($userType === 'regular_with_broker') {
                    // الأشخاص العاديين الذين يطلبون مندوب
                    $query->where('rank', 0);
                } elseif ($userType === 'regular_without_broker') {
                    // الأشخاص العاديين الذين لا يطلبون مندوب
                    $query->where('rank', 0);
                } else { // vip
                    $query->where('rank', '>', 0);
                }
            })
            ->when(in_array($userType, ['regular_with_broker', 'regular_without_broker']), function ($query) use ($userType) {
                if ($userType === 'regular_with_broker') {
                    $query->where('needs_brokering', true);
                } elseif ($userType === 'regular_without_broker') {
                    $query->where('needs_brokering', false);
                }
            })
            ->when($status === 'pending', function ($query) {
                $query->pending();
            })
            ->when($filters, function ($query) use ($filters) {
                $query->when(isset($filters['search']), function ($query) use ($filters) {
                    $query->where('title', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('id', $filters['search'])
                        ->orWhere('user_id', $filters['search']);
                });
            })
            ->orderBy('created_at', 'asc') // من الأقدم للأحدث
            ->paginate($limit)
            ->appends(['search' => $filters['search'] ?? null]);
    }

    /**
     * Get ads count by user type and status
     *
     * @param string $userType = 'regular' <regular|vip>
     * @param string $status = 'all' <all|pending>
     * @return int
     */
    public function getAdsCountByUserType(string $userType, string $status = 'all'): int
    {
        return $this->model->query()
            ->whereHas('user', function ($query) use ($userType) {
                if ($userType === 'regular') {
                    $query->where('rank', 0);
                } elseif ($userType === 'regular_with_broker') {
                    // الأشخاص العاديين الذين يطلبون مندوب
                    $query->where('rank', 0);
                } elseif ($userType === 'regular_without_broker') {
                    // الأشخاص العاديين الذين لا يطلبون مندوب
                    $query->where('rank', 0);
                } else { // vip
                    $query->where('rank', '>', 0);
                }
            })
            ->when(in_array($userType, ['regular_with_broker', 'regular_without_broker']), function ($query) use ($userType) {
                if ($userType === 'regular_with_broker') {
                    $query->where('needs_brokering', true);
                } elseif ($userType === 'regular_without_broker') {
                    $query->where('needs_brokering', false);
                }
            })
            ->when($status === 'pending', function ($query) {
                $query->pending();
            })
            ->count();
    }

    /**
     * Get ad by slug
     * 
     * @param string $slug
     * @return \App\Models\Ad
     */
    public function getAdBySlug(string $adSlug): Ad
    {
        return $this->model->query()->with(['user:id,name,avatar,username', 'media', 'category:id,name,slug', 'subCategory:parent_id,id,name,slug', 'attributes.categoryAttribute', 'adminNotes.admin:id,name', 'bids', 'bids.user:id,name,avatar,username', 'country:id,name,iso2', 'state:id,name,code', 'city:id,name', 'relatedAds:id,title,slug,price', 'relatedAds.media',])
            ->where('slug', $adSlug)
            ->firstOr(function () {
                throw new AdException('Ad not found.');
            });
    }

    /**
     * Update ad by slug
     * 
     * @param string $slug
     * @param array $data
     * @return void
     */
    public function updateAd(string $slug, array $data): void
    {          
        $ad = $this->model->where('slug', $slug)->firstOr(function () {
            throw new AdException('Ad not found.');
        });

        $user = User::where('email', $data['seller_email'])->first();

        // تحديث عدد الإعلانات عند تغيير الحالة
        if (isset($data['status'])) {
            $this->updateUserAdsCount($ad, $data['status'], $user);

            // بدء الرعاية عند الموافقة والنشر
            if ($data['status'] == \App\Enums\AdStatus::PUBLISHED->value) {
                $ad->startSponsorship();
            }
        }
        $category = (isset($data['main-category']) && !empty($data['main-category'])) ? app(CategoryRepository::class)->findBySlug($data['main-category']) : null;
        $subcategory = (isset($data['sub-category']) && !empty($data['sub-category'])) ? app(CategoryRepository::class)->findBySlug($data['sub-category']) : null;

        Log::info('Category update data', [
            'main-category' => $data['main-category'] ?? 'not set',
            'sub-category' => $data['sub-category'] ?? 'not set',
            'category_id' => $category?->id,
            'subcategory_id' => $subcategory?->id,
            'current_category_id' => $ad->category_id,
            'current_sub_category_id' => $ad->sub_category_id
        ]);
        
        DB::transaction(function () use ($data, $ad, $category, $subcategory) {
            $updateData = [
                'title' => $data['title'],
                'description' => SecurityHelper::cleanAdDescription($data['description']),
                'category_id' => $category?->id ?? $ad->category_id,
                'sub_category_id' => $subcategory?->id ?? $ad->sub_category_id,
                'price' => $data['price'],
                'seller_name' => $data['seller_name'],
                'seller_email' => $data['seller_email'],
                'seller_mobile' => $data['seller_mobile'] ?? $ad->seller_mobile,
                'seller_address' => $data['seller_address'] ?? $ad->seller_address,
                'needs_brokering' => $data['needs_brokering'] ?? $ad->needs_brokering,
                'broker_commission' => $data['broker_commission'] ?? $ad->broker_commission,
                'is_sponsored' => $data['is_sponsored'] ?? $ad->is_sponsored,
                'sponsorship_cost' => $data['sponsorship_cost'] ?? $ad->sponsorship_cost,
                'expires_at' => $data['expires_at'] ?? $ad->expires_at,
                'views' => $data['views'] ?? $ad->views,
                'country_id' => $data['country_id'] ?? $ad->country_id,
                'status' => $data['status'],
            ];

            Log::info('Update data prepared', $updateData);
            $ad->update($updateData);

            // Update attributes if provided
            if (isset($data['attributes']) && is_array($data['attributes'])) {
                Log::info('Updating ad attributes', ['ad_id' => $ad->id, 'attributes' => $data['attributes']]);
                $this->updateAdAttributes($ad, $data['attributes']);
            } else {
                Log::info('No attributes to update', ['data_keys' => array_keys($data)]);
            }

            // Add admin note if provided
            if (isset($data['admin_note']) && !empty($data['admin_note']) && isset($data['note_type']) && !empty($data['note_type'])) {
                $this->addAdminNote($ad, $data['admin_note'], $data['note_type']);
            }
        });
    }

    /**
     * Update ad attributes.
     *
     * @param Ad $ad
     * @param array $attributes
     * @return void
     */
    private function updateAdAttributes(Ad $ad, array $attributes): void
    {
        foreach ($attributes as $attributeName => $attributeValue) {
            Log::info('Processing attribute', ['name' => $attributeName, 'value' => $attributeValue]);

            // Find existing attribute
            $existingAttribute = $ad->attributes()->where('attribute_name', $attributeName)->first();

            if ($existingAttribute) {
                if (!empty($attributeValue)) {
                    // Update existing attribute
                    $existingAttribute->update([
                        'attribute_value' => $attributeValue
                    ]);
                    Log::info('Updated attribute', ['id' => $existingAttribute->id, 'new_value' => $attributeValue]);
                } else {
                    // If value is empty, we could delete the attribute or keep it
                    // For now, let's keep it but log it
                    Log::info('Empty value for attribute', ['name' => $attributeName]);
                }
            } else {
                Log::info('Attribute not found', ['name' => $attributeName]);
            }
        }
    }

    /**
     * Add admin note to ad.
     *
     * @param Ad $ad
     * @param string $note
     * @param string $type
     * @return void
     */
    private function addAdminNote(Ad $ad, string $note, string $type): void
    {
        AdminNote::create([
            'ad_id' => $ad->id,
            'admin_id' => Auth::guard('admin_web')->id(),
            'note' => $note,
            'type' => $type,
        ]);

        Log::info('Admin note added', [
            'ad_id' => $ad->id,
            'admin_id' => Auth::guard('admin_web')->id(),
            'type' => $type
        ]);
    }

    /**
     * Get reported all ads
     * 
     * @param int $limit
     * @param array $filters
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getReportedAds(int $limit = 10, array $filters = null): LengthAwarePaginator
    {
        return ReportAd::query()->with('ad:id,slug,title,status', 'ad.media')
            ->when($filters, function ($query) use ($filters) {
                $query->when(isset($filters['search']), function ($query) use ($filters) {
                    $query->whereHas('ad', function ($query) use ($filters) {
                        $query->where('title', 'like', '%' . $filters['search'] . '%')
                            ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                            ->orWhere('id', $filters['search']);
                    });
                });
            })
            ->orderBy('created_at','desc')
            ->paginate($limit)
            ->appends(['search' => $filters['search'] ?? null]);
    }


    /**
     * Get reported all ads
     * 
     * @param string $slug
     * @return \App\Models\ReportAd
     */
    public function getReportedAd(string $slug): \App\Models\ReportAd
    {
        return ReportAd::query()->with('ad:id,slug,title,status', 'ad.media', 'ad.user:id,name,avatar,username')
            ->whereHas('ad', function ($query) use ($slug) {
                $query->where('slug', $slug);
            })
            ->firstOr(function () {
                throw new AdException('Ad not found.');
            });
    }

    /**
     * Delete ad by status
     * 
     * @param string $status
     * @param int $limit
     * @return void
     */
    public function deleteAd(string $adSlug): void
    {
        $ad = $this->model->where('slug', $adSlug)->firstOr(function () {
            throw new AdException('Ad not found.');
        });

        // تقليل عدد الإعلانات إذا كان الإعلان منشوراً
        if ($ad->status === \App\Enums\AdStatus::PUBLISHED) {
            $user = User::where('email', $ad->seller_email)->first();
            if ($user && $user->Number_Ads > 0) {
                $user->decrement('Number_Ads');
            }
        }

        $ad->media->each(function ($media) {
            $this->deleteMediaFile($media);
        });
        $ad->delete();
    }

    /**
     * Update user ads count when ad status changes
     *
     * @param Ad $ad
     * @param int $newStatus
     * @param User|null $user
     * @return void
     */
    private function updateUserAdsCount(Ad $ad, int $newStatus, ?User $user): void
    {
        if (!$user) {
            return;
        }

        $oldStatus = $ad->status;
        $newStatusEnum = \App\Enums\AdStatus::from($newStatus);

        // إذا تم تغيير الحالة من غير منشور إلى منشور
        if ($oldStatus !== \App\Enums\AdStatus::PUBLISHED && $newStatusEnum === \App\Enums\AdStatus::PUBLISHED) {
            $user->increment('Number_Ads');
        }

        // إذا تم تغيير الحالة من منشور إلى غير منشور
        if ($oldStatus === \App\Enums\AdStatus::PUBLISHED && $newStatusEnum !== \App\Enums\AdStatus::PUBLISHED) {
            if ($user->Number_Ads > 0) {
                $user->decrement('Number_Ads');
            }
        }
    }
}
