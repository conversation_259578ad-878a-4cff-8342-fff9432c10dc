<?php

namespace Tests\Feature;

use App\Helpers\SecurityHelper;
use App\Rules\CleanHtmlRule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class SecurityTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_removes_html_tags_from_description()
    {
        $maliciousInput = '<p>This is a test description with enough content</p><script>alert("XSS")</script>';
        $cleaned = SecurityHelper::cleanAdDescription($maliciousInput);

        $this->assertStringContainsString('This is a test description', $cleaned);
        $this->assertStringNotContainsString('<script>', $cleaned);
        $this->assertStringNotContainsString('<p>', $cleaned);
        $this->assertStringNotContainsString('alert("XSS")', $cleaned);
    }

    /** @test */
    public function it_prevents_javascript_injection()
    {
        $maliciousInputs = [
            '<script>alert("XSS")</script>This is a long enough description to pass validation',
            '<img src="x" onerror="alert(\'XSS\')">This is a long enough description to pass validation',
            '<div onclick="alert(\'XSS\')">This is a long enough description to pass validation</div>',
            'This is a long enough description with javascript:alert("XSS") injection',
            '<iframe src="javascript:alert(\'XSS\')">This is a long enough description</iframe>',
        ];

        foreach ($maliciousInputs as $input) {
            $cleaned = SecurityHelper::cleanAdDescription($input);

            $this->assertStringNotContainsString('<script>', $cleaned);
            $this->assertStringNotContainsString('javascript:', $cleaned);
            $this->assertStringNotContainsString('onerror=', $cleaned);
            $this->assertStringNotContainsString('onclick=', $cleaned);
            $this->assertStringNotContainsString('<iframe>', $cleaned);
        }
    }

    /** @test */
    public function it_validates_clean_html_rule()
    {
        $rule = new CleanHtmlRule();
        
        // Test valid input
        $validator = Validator::make(
            ['description' => 'This is a clean description'],
            ['description' => $rule]
        );
        $this->assertTrue($validator->passes());

        // Test invalid input with HTML
        $validator = Validator::make(
            ['description' => '<p>This contains HTML</p>'],
            ['description' => $rule]
        );
        $this->assertFalse($validator->passes());

        // Test invalid input with JavaScript
        $validator = Validator::make(
            ['description' => '<script>alert("XSS")</script>'],
            ['description' => $rule]
        );
        $this->assertFalse($validator->passes());
    }

    /** @test */
    public function it_cleans_various_input_types()
    {
        // Test URL cleaning
        $cleanUrl = SecurityHelper::cleanUrl('https://example.com');
        $this->assertEquals('https://example.com', $cleanUrl);

        // Test email cleaning
        $cleanEmail = SecurityHelper::cleanEmail('<EMAIL>');
        $this->assertEquals('<EMAIL>', $cleanEmail);

        // Test phone cleaning
        $cleanPhone = SecurityHelper::cleanPhone('+****************');
        $this->assertEquals('+****************', $cleanPhone);

        // Test text input cleaning
        $cleanText = SecurityHelper::cleanTextInput('<b>Bold text</b>');
        $this->assertEquals('Bold text', $cleanText);
    }

    /** @test */
    public function it_handles_empty_and_null_inputs()
    {
        $this->assertEquals('', SecurityHelper::cleanAdDescription(''));
        $this->assertEquals('', SecurityHelper::cleanAdDescription(null));
        $this->assertEquals('', SecurityHelper::cleanHtml(''));
        $this->assertEquals('', SecurityHelper::cleanHtml(null));
    }

    /** @test */
    public function it_preserves_safe_content()
    {
        $safeContent = 'This is a safe description with numbers 123 and symbols !@#$%';
        $cleaned = SecurityHelper::cleanAdDescription($safeContent);
        
        $this->assertStringContainsString('This is a safe description', $cleaned);
        $this->assertStringContainsString('123', $cleaned);
    }

    /** @test */
    public function it_enforces_minimum_length_after_cleaning()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Description must be at least 10 characters after cleaning.');
        
        // Short content that becomes even shorter after cleaning
        SecurityHelper::cleanAdDescription('<p>Hi</p>');
    }

    /** @test */
    public function it_removes_dangerous_attributes()
    {
        $dangerousInputs = [
            '<div onload="alert(\'XSS\')">This is a long enough content description</div>',
            '<img src="x" onerror="alert(\'XSS\')">This is a long enough content description',
            '<a href="javascript:alert(\'XSS\')">This is a long enough content description</a>',
            '<form action="malicious.com">This is a long enough content description</form>',
            '<object data="malicious.swf">This is a long enough content description</object>',
            '<embed src="malicious.swf">This is a long enough content description',
        ];

        foreach ($dangerousInputs as $input) {
            $cleaned = SecurityHelper::cleanAdDescription($input);

            $this->assertStringNotContainsString('onload=', $cleaned);
            $this->assertStringNotContainsString('onerror=', $cleaned);
            $this->assertStringNotContainsString('javascript:', $cleaned);
            $this->assertStringNotContainsString('<form', $cleaned);
            $this->assertStringNotContainsString('<object', $cleaned);
            $this->assertStringNotContainsString('<embed', $cleaned);
        }
    }

    /** @test */
    public function it_handles_nested_html_tags()
    {
        $nestedHtml = '<div><p><span><b>Nested content</b></span></p></div>';
        $cleaned = SecurityHelper::cleanAdDescription($nestedHtml);
        
        $this->assertEquals('Nested content', $cleaned);
        $this->assertStringNotContainsString('<div>', $cleaned);
        $this->assertStringNotContainsString('<p>', $cleaned);
        $this->assertStringNotContainsString('<span>', $cleaned);
        $this->assertStringNotContainsString('<b>', $cleaned);
    }

    /** @test */
    public function it_prevents_data_uri_attacks()
    {
        $dataUriAttack = '<img src="data:text/html,<script>alert(\'XSS\')</script>">This is a long enough description content';
        $cleaned = SecurityHelper::cleanAdDescription($dataUriAttack);

        $this->assertStringNotContainsString('data:', $cleaned);
        $this->assertStringNotContainsString('<img', $cleaned);
        $this->assertStringNotContainsString('<script>', $cleaned);
    }
}
