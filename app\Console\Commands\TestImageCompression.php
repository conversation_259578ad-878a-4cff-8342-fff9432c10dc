<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Http\UploadedFile;
use App\Services\SimpleFileCompressionService;
use App\Services\AdvancedImageOptimizer;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class TestImageCompression extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'image:test-compression {--path=} {--type=ad_images}';

    /**
     * The console command description.
     */
    protected $description = 'Test image compression with different settings and show results';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing Advanced Image Compression System');
        $this->info('==========================================');

        $path = $this->option('path');
        $imageType = $this->option('type');

        if (!$path) {
            $this->error('Please provide an image path using --path option');
            return 1;
        }

        if (!File::exists($path)) {
            $this->error("File not found: {$path}");
            return 1;
        }

        // Create a fake UploadedFile for testing
        $file = new UploadedFile(
            $path,
            basename($path),
            mime_content_type($path),
            null,
            true
        );

        $this->info("Testing image: " . basename($path));
        $this->info("Original size: " . $this->formatFileSize(filesize($path)));
        $this->info("Image type: {$imageType}");
        $this->newLine();

        // Test 1: Basic compression
        $this->testBasicCompression($file, $imageType);

        // Test 2: Advanced compression
        $this->testAdvancedCompression($file, $imageType);

        // Test 3: Ultra compression
        $this->testUltraCompression($file, $imageType);

        // Test 4: Different settings comparison
        $this->testDifferentSettings($file);

        $this->newLine();
        $this->info('✅ Testing completed!');

        return 0;
    }

    private function testBasicCompression(UploadedFile $file, string $imageType)
    {
        $this->info('📊 Testing Basic Compression:');
        $this->info('-----------------------------');

        try {
            $compressionService = new SimpleFileCompressionService();
            $settings = $compressionService->getOptimizedSettings($imageType);
            
            $result = $compressionService->compressAndStore($file, 'test-compression', $settings);

            if ($result['success']) {
                $this->line("✅ Success!");
                $this->line("   Compressed size: " . $this->formatFileSize($result['compressed_size']));
                $this->line("   Compression ratio: {$result['compression_ratio']}%");
                $this->line("   Format: {$result['format']}");
                
                // Clean up test file
                Storage::disk('public')->delete($result['path']);
            } else {
                $this->error("❌ Failed: " . ($result['error'] ?? 'Unknown error'));
            }
        } catch (\Exception $e) {
            $this->error("❌ Exception: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function testAdvancedCompression(UploadedFile $file, string $imageType)
    {
        $this->info('🚀 Testing Advanced Compression:');
        $this->info('--------------------------------');

        try {
            $compressionService = new SimpleFileCompressionService();
            $settings = $compressionService->getOptimizedSettings($imageType);
            
            $result = $compressionService->compressAndStore($file, 'test-compression', $settings);

            if ($result['success']) {
                $this->line("✅ Success!");
                $this->line("   Compressed size: " . $this->formatFileSize($result['compressed_size']));
                $this->line("   Compression ratio: {$result['compression_ratio']}%");
                $this->line("   Format: {$result['format']}");
                if (isset($result['techniques_used'])) {
                    $this->line("   Techniques used: " . implode(', ', $result['techniques_used']));
                }
                
                // Clean up test file
                Storage::disk('public')->delete($result['path']);
            } else {
                $this->error("❌ Failed: " . ($result['error'] ?? 'Unknown error'));
            }
        } catch (\Exception $e) {
            $this->error("❌ Exception: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function testUltraCompression(UploadedFile $file, string $imageType)
    {
        $this->info('⚡ Testing Ultra Compression:');
        $this->info('----------------------------');

        try {
            $advancedOptimizer = new AdvancedImageOptimizer();
            
            // Get ultra settings based on image type
            $ultraSettings = $this->getUltraSettings($imageType);
            
            $result = $advancedOptimizer->ultraCompress($file, $ultraSettings);

            if ($result['success']) {
                $this->line("✅ Success!");
                $this->line("   Compressed size: " . $this->formatFileSize($result['compressed_size']));
                $this->line("   Compression ratio: {$result['compression_ratio']}%");
                $this->line("   Final dimensions: {$result['final_dimensions']['width']}x{$result['final_dimensions']['height']}");
                $this->line("   Techniques applied: " . implode(', ', $result['techniques_applied']));
            } else {
                $this->error("❌ Failed: " . ($result['error'] ?? 'Unknown error'));
            }
        } catch (\Exception $e) {
            $this->error("❌ Exception: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function testDifferentSettings(UploadedFile $file)
    {
        $this->info('🔬 Testing Different Settings:');
        $this->info('------------------------------');

        $testSettings = [
            'Conservative' => [
                'target_size_kb' => 500,
                'min_quality' => 60,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            'Balanced' => [
                'target_size_kb' => 200,
                'min_quality' => 40,
                'max_width' => 1000,
                'max_height' => 1000,
                'format' => 'webp'
            ],
            'Aggressive' => [
                'target_size_kb' => 100,
                'min_quality' => 25,
                'max_width' => 800,
                'max_height' => 800,
                'format' => 'webp'
            ],
            'Ultra Aggressive' => [
                'target_size_kb' => 50,
                'min_quality' => 20,
                'max_width' => 600,
                'max_height' => 600,
                'format' => 'webp'
            ]
        ];

        $advancedOptimizer = new AdvancedImageOptimizer();

        foreach ($testSettings as $settingName => $settings) {
            try {
                $result = $advancedOptimizer->ultraCompress($file, $settings);
                
                if ($result['success']) {
                    $this->line("✅ {$settingName}:");
                    $this->line("   Size: " . $this->formatFileSize($result['compressed_size']));
                    $this->line("   Ratio: {$result['compression_ratio']}%");
                    $this->line("   Dimensions: {$result['final_dimensions']['width']}x{$result['final_dimensions']['height']}");
                } else {
                    $this->line("❌ {$settingName}: Failed");
                }
            } catch (\Exception $e) {
                $this->line("❌ {$settingName}: Exception - " . $e->getMessage());
            }
        }

        $this->newLine();
    }

    private function getUltraSettings(string $imageType): array
    {
        return match ($imageType) {
            'ad_images' => [
                'target_size_kb' => 120,
                'min_quality' => 25,
                'max_width' => 900,
                'max_height' => 900,
                'format' => 'webp'
            ],
            'profile_avatar' => [
                'target_size_kb' => 35,
                'min_quality' => 35,
                'max_width' => 250,
                'max_height' => 250,
                'format' => 'webp'
            ],
            'identity_documents' => [
                'target_size_kb' => 250,
                'min_quality' => 45,
                'max_width' => 1200,
                'max_height' => 900,
                'format' => 'webp'
            ],
            'broker_documents' => [
                'target_size_kb' => 250,
                'min_quality' => 45,
                'max_width' => 1200,
                'max_height' => 900,
                'format' => 'webp'
            ],
            default => [
                'target_size_kb' => 150,
                'min_quality' => 30,
                'max_width' => 800,
                'max_height' => 800,
                'format' => 'webp'
            ]
        };
    }

    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
