@extends('partials.app')
@section('title', 'Premium Classified Marketplace')
@section('description', 'Discover amazing deals on cars, real estate, electronics and more. Your trusted marketplace for buying and selling.')
@section('content')

<!-- Hero Section -->
<div class="hero-section-modern">
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-8 col-xl-7">
                <div class="hero-content">
                    <div class="hero-badge mb-4">
                        <span class="badge-text">🏆 #1 Classified Platform</span>
                    </div>
                    <h1 class="hero-title wow fadeInUp" data-wow-duration="1s">
                        Find Everything You Need in One Place
                    </h1>
                    <p class="hero-description wow fadeInUp" data-wow-duration="1.2s" data-wow-delay="0.2s">
                        Discover thousands of verified listings for cars, real estate, electronics, and more.
                        Buy, sell, and connect with confidence on our premium marketplace.
                    </p>
                    <div class="hero-actions wow fadeInUp" data-wow-duration="1.4s" data-wow-delay="0.4s">
                        <a href="{{ route('live-auction') }}" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-search me-2"></i>Browse Listings
                        </a>
                        <a href="{{ route('add-listing') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-plus me-2"></i>Post Ad
                        </a>
                    </div>
                    <div class="hero-stats mt-5">
                        <div class="row">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number">50K+</h3>
                                    <p class="stat-label">Active Listings</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number">25K+</h3>
                                    <p class="stat-label">Happy Users</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number">99%</h3>
                                    <p class="stat-label">Satisfaction</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Categories Section -->
<section class="categories-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Browse by Category</h2>
                <p class="section-subtitle">Find exactly what you're looking for</p>
            </div>
        </div>
        <x-categories-card />
    </div>
</section>

<!-- Featured Listings Section -->
<section class="featured-listings py-5 bg-light">
    <div class="container">
        <div class="row align-items-center mb-5">
            <div class="col-lg-8">
                <h2 class="section-title mb-2">Featured Listings</h2>
                <p class="section-subtitle mb-0">Handpicked premium listings just for you</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{{ route('live-auction') }}" class="btn btn-primary">
                    <i class="fas fa-th-large me-2"></i>View All Listings
                </a>
            </div>
        </div>

        <div class="row gy-4">
            @forelse ( $ads as $ad )
                <div class="col-lg-4 col-md-6">
                    <x-ad-item-card :ad="$ad" type="classic" />
                </div>
            @empty
                <div class="col-12">
                    <div class="empty-state text-center py-5">
                        <div class="empty-icon mb-4">
                            <i class="fas fa-search fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted">No Listings Available</h4>
                        <p class="text-muted">Be the first to post a listing in our marketplace!</p>
                        <a href="{{ route('add-listing') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Post Your Ad
                        </a>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</section>


<!-- Why Choose Us Section -->
<section class="why-choose-us py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Why Choose Our Platform?</h2>
                <p class="section-subtitle">Experience the difference with our premium features</p>
            </div>
        </div>
        <div class="row gy-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mb-4">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title">Secure & Trusted</h4>
                    <p class="feature-description">All listings are verified and users are authenticated for your safety</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mb-4">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h4 class="feature-title">Lightning Fast</h4>
                    <p class="feature-description">Find what you need quickly with our advanced search and filters</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center">
                    <div class="feature-icon mb-4">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h4 class="feature-title">Easy Transactions</h4>
                    <p class="feature-description">Seamless buying and selling experience with built-in communication tools</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Trusted Partners Section -->
<section class="partners-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Trusted by Leading Brands</h2>
                <p class="section-subtitle">Join thousands of satisfied customers</p>
            </div>
        </div>
        <div class="partners-slider">
            <div class="partner-item"><img alt="Partner 1" src="assets/images/bg/sponsor1.png" class="partner-logo"></div>
            <div class="partner-item"><img alt="Partner 2" src="assets/images/bg/sponsor2.png" class="partner-logo"></div>
            <div class="partner-item"><img alt="Partner 3" src="assets/images/bg/sponsor3.png" class="partner-logo"></div>
            <div class="partner-item"><img alt="Partner 4" src="assets/images/bg/sponsor4.png" class="partner-logo"></div>
            <div class="partner-item"><img alt="Partner 5" src="assets/images/bg/sponsor5.png" class="partner-logo"></div>
            <div class="partner-item"><img alt="Partner 6" src="assets/images/bg/sponsor6.png" class="partner-logo"></div>
        </div>
    </div>
</section>

<!-- Latest News & Updates -->
<section class="news-section py-5">
    <div class="container">
        <div class="row align-items-center mb-5">
            <div class="col-lg-8">
                <h2 class="section-title mb-2">Latest News & Updates</h2>
                <p class="section-subtitle mb-0">Stay informed with our latest marketplace insights</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{{ route('blog.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-newspaper me-2"></i>View All News
                </a>
            </div>
        </div>

        <div class="row gy-4">
            @forelse ( $posts as $post )
            <div class="col-lg-4 col-md-6">
                <article class="news-card">
                    <div class="news-image">
                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="img-fluid">
                        <div class="news-date">
                            <span class="date-day">{{ $post->created_at->format('d') }}</span>
                            <span class="date-month">{{ $post->created_at->format('M') }}</span>
                        </div>
                    </div>
                    <div class="news-content">
                        <h5 class="news-title">
                            <a href="{{ route('blog.show', $post->slug) }}">{{ $post->title }}</a>
                        </h5>
                        <p class="news-excerpt">{{ shorten_chars($post->content, 120, true) }}</p>
                        <a href="{{ route('blog.show', $post->slug) }}" class="read-more">
                            Read More <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </article>
            </div>
            @empty
            <div class="col-12">
                <div class="empty-state text-center py-5">
                    <div class="empty-icon mb-4">
                        <i class="fas fa-newspaper fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">No News Available</h4>
                    <p class="text-muted">Check back later for the latest updates and insights.</p>
                </div>
            </div>
            @endforelse
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="stats-section py-5 bg-primary text-white">
    <div class="container">
        <x-metric-card />
    </div>
</section>

@push('styles')
<style>
/* Modern Hero Section */
.hero-section-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}

.hero-section-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
}

.hero-badge {
    display: inline-block;
}

.badge-text {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-actions .btn {
    padding: 15px 30px;
    font-weight: 600;
    border-radius: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-stats {
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

/* Section Styling */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* Feature Cards */
.feature-card {
    background: white;
    padding: 2.5rem 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* News Cards */
.news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-date {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    min-width: 60px;
}

.date-day {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
}

.date-month {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.news-content {
    padding: 1.5rem;
}

.news-title a {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 600;
    line-height: 1.4;
}

.news-title a:hover {
    color: #667eea;
}

.news-excerpt {
    color: #6c757d;
    line-height: 1.6;
    margin: 1rem 0;
}

.read-more {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.read-more:hover {
    color: #764ba2;
}

/* Partners Section */
.partners-slider {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 2rem;
}

.partner-item {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.partner-item:hover {
    opacity: 1;
}

.partner-logo {
    max-height: 60px;
    width: auto;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.partner-item:hover .partner-logo {
    filter: grayscale(0%);
}

/* Empty State */
.empty-state {
    background: white;
    border-radius: 15px;
    padding: 3rem;
}

.empty-icon i {
    opacity: 0.3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* Custom Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: #667eea;
    transition: width 0.3s ease;
    z-index: -1;
}

.btn-outline-primary:hover::before {
    width: 100%;
}

.btn-outline-primary:hover {
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.wow {
    visibility: hidden;
}

/* Section Spacing */
section {
    position: relative;
}

.categories-section {
    background: #f8f9fa;
}

.categories-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to bottom, white, transparent);
}

/* Enhanced Shadows */
.feature-card,
.news-card {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover,
.news-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

/* Improved Typography */
.section-title {
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Floating Elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.feature-icon {
    animation: float 3s ease-in-out infinite;
}

.feature-card:nth-child(2) .feature-icon {
    animation-delay: 1s;
}

.feature-card:nth-child(3) .feature-icon {
    animation-delay: 2s;
}

/* Gradient Text */
.hero-title {
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Stats */
.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stats-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23stats-pattern)"/></svg>');
}

/* Responsive Improvements */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 3rem;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }

    .hero-actions .btn:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .feature-card {
        padding: 2rem 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="/assets/js/countdown.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Counter animation for stats
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);

        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start).toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toLocaleString();
            }
        }

        updateCounter();
    }

    // Trigger counter animation when stats section is visible
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counters = entry.target.querySelectorAll('.stat-number');
                counters.forEach(counter => {
                    const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
                    if (target) {
                        animateCounter(counter, target);
                    }
                });
                statsObserver.unobserve(entry.target);
            }
        });
    });

    const statsSection = document.querySelector('.hero-stats');
    if (statsSection) {
        statsObserver.observe(statsSection);
    }

    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero-section-modern');
        if (hero) {
            hero.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Add loading animation to cards
    const cards = document.querySelectorAll('.feature-card, .news-card, .category-card-modern');
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });

    // Enhanced button hover effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add ripple effect to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Add ripple CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);
</script>
@endpush
@endsection