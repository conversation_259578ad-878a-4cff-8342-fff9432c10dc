# 🚀 Bazaar Features Documentation
## 📖 Table of Contents
- [🚀 Live Demo](#live-demo)
- [🗒️ Description](#description)
- [🔖 Features](#features)
  - [📝 Ad Listing](#ad-listing)
  - [🎯 Bidding](#bidding)
  - [📚 Blog CMS](#blog-cms)
  - [💬 Comment Management](#comment-management)
  - [💵 Payment](#payment)
  - [💳 Payout Method](#payout-method)
  - [🤑 Payout](#payout)
  - [📝 User Management (by Admin)](#user-management-by-admin)
  - [👥 Account Management](#account-management)
  - [📊 Analytics](#analytics)
  - [📤 Support Ticket](#support-ticket)
  - [🔎 Multi-Model Search](#multi-model-search)

## 🚀 Live Demo <a name="live-demo"></a>
> You can watch a live demo of the application below.

https://github.com/hendurhance/bazaar/assets/********/3191f5a3-8048-4ded-8f41-f5a1197a349e


## 📝 Description <a name="description"></a>
> Bazaar Auction Platform is an online auction system that allows users to list their ads for auction. It provides a user-friendly interface for both ad owners and bidders, with features such as ad listing, bidding, automated workflows, payment processing, and payout requests.

## 🔖 Features <a name="features"></a>
### 📝 Ad Listing <a name="ad-listing"></a>
> Users can list their ads for auction. They can create and mange their ads from the user dashboard. Admins can also manage ads from the admin dashboard.

![Web](/docs/images/ad/one.png)
The ad listing page allows users to create an ad for auction. They can specify the starting bid, the minimum bid increment, and the auction end date. They can also upload images of the item they are selling.

![User Dashboard](/docs/images/ad/two.png)
The user dashboard allows users to view their ads and their current status. They can also edit or delete their ads.

![Admin Dashboard](/docs/images/ad/three.png)
The admin dashboard allows admins to view all ads and their current status. They can also edit or delete ads.

### 🎯 Bidding <a name="bidding"></a>
> Users can bid on ads, and the highest bidder will win the auction. The auction will end when the auction end date is reached. The winner will be notified via email.

![Web](/docs/images/bid/one.png)
The page allows users to bid on an ad. They can specify the amount they want to bid. They can also view the current highest bid and the minimum bid increment.

![User Dashboard](/docs/images/bid/two.png)
The user dashboard allows users to view their bids and their current status. They can see if their bid got outbid or if they won the auction.

![Admin Dashboard](/docs/images/bid/three.png)
The admin dashboard allows admins to view all bids and their current status.

### 📚 Blog CMS <a name="blog-cms"></a>
> Users can comment on blog posts. Admins can manage blog posts from the admin dashboard and can also manage comments.

![Web](/docs/images/blog/one.png)

![Web](/docs/images/blog/two.png)

![Admin Dashboard](/docs/images/blog/three.png)

### 💬 Comment Management <a name="comment-management"></a>
> Users can comment on blog posts. Admins can manage blog posts from the admin dashboard and can also manage comments.

![Web](/docs/images/comment/one.png)

![Web](/docs/images/comment/two.png)

![Admin Dashboard](/docs/images/comment/three.png)

### 💵 Payment <a name="payment"></a>
> Users can pay for their winning bids using Paystack or Flutterwave. Admins can manage payments from the admin dashboard.

![User Dashboard](/docs/images/payment/one.png)

![User Dashboard](/docs/images/payment/two.png)

![Admin Dashboard](/docs/images/payment/three.png)

### 💳 Payout Method <a name="payout-method"></a>
> Users can add their payout method from the user dashboard. Admins can manage payout methods from the admin dashboard.

![User Dashboard](/docs/images/method/one.png)

![User Dashboard](/docs/images/method/two.png)

![Admin Dashboard](/docs/images/method/three.png)

### 🤑 Payout <a name="payout"></a>
> Users can request a payout for their winning bids. Admins can manage payouts from the admin dashboard.

![User Dashboard](/docs/images/payout/one.png)

![User Dashboard](/docs/images/payout/two.png)

![Admin Dashboard](/docs/images/payout/three.png)

### 📝 User Management (by Admin) <a name="user-management-by-admin"></a>
> Admins can manage users from the admin dashboard. Request password reset, delete users, and view user details.

![Admin Dashboard](/docs/images/user/one.png)

![Admin Dashboard](/docs/images/user/two.png)

![Admin Dashboard](/docs/images/user/three.png)

### 👥 Account Management <a name="account-management"></a>
> Users can manage their account from the user dashboard. They can change their password, update their profile, and delete their account, likewise, admins can manage their account from the admin dashboard.

![User Dashboard](/docs/images/account/one.png)

![Admin Dashboard](/docs/images/account/two.png)

### 📊 Analytics <a name="analytics"></a>
> Admins can view analytics from the admin dashboard. They can view the number of users, ads, bids, and comments. Users can also view their analytics from the user dashboard.

![User Dashboard](/docs/images/analytics/one.png)

![Admin Dashboard](/docs/images/analytics/two.png)

### 📤 Support Ticket <a name="support-ticket"></a>
> Users can create support tickets from the user dashboard. Admins can manage support tickets from the admin dashboard.

![Web](/docs/images/ticket/one.png)

![Admin Dashboard](/docs/images/ticket/two.png)

### 🔎 Multi-Model Search <a name="multi-model-search"></a>
> Users can search for ads, bids, media, and users, all from one search bar.

![Admin Dashboard](/docs/images/search/one.png)

![Admin Dashboard](/docs/images/search/two.png)

And many more features... that you can discover by installing the application. 🚀.