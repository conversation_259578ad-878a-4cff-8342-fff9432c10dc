<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sponsored_ads', function (Blueprint $table) {
            // إضافة حقل الأولوية (1 = أعلى أولوية، 5 = أقل أولوية)
            $table->tinyInteger('priority')->default(3)->after('status')->comment('1=Highest, 5=Lowest priority');

            // إضافة حقول الموقع للفلترة الجغرافية
            $table->foreignId('country_id')->nullable()->after('priority')->constrained('countries')->onDelete('set null');
            $table->foreignId('state_id')->nullable()->after('country_id');
            $table->string('city_id')->nullable()->after('state_id');

            // إضافة حقول إضافية للتحكم المتقدم
            $table->boolean('show_in_search')->default(true)->after('city_id')->comment('Show in search results');
            $table->boolean('show_in_details')->default(true)->after('show_in_search')->comment('Show in ad details page');
            $table->json('target_categories')->nullable()->after('show_in_details')->comment('Specific categories to target');

            // إضافة فهارس للأداء
            $table->index(['priority', 'status', 'is_active']);
            $table->index(['country_id', 'state_id', 'city_id']);
            $table->index(['show_in_search', 'show_in_details']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sponsored_ads', function (Blueprint $table) {
            // حذف الفهارس
            $table->dropIndex(['priority', 'status', 'is_active']);
            $table->dropIndex(['country_id', 'state_id', 'city_id']);
            $table->dropIndex(['show_in_search', 'show_in_details']);

            // حذف الأعمدة
            $table->dropForeign(['country_id']);
            $table->dropColumn([
                'priority',
                'country_id',
                'state_id',
                'city_id',
                'show_in_search',
                'show_in_details',
                'target_categories'
            ]);
        });
    }
};
