<?php

namespace App\Http\Middleware;

use App\Models\UserWarning;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserWarnings
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $warningType): Response
    {
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Check if user has active warning of this type
        if ($user->hasActiveWarning($warningType)) {
            $warning = $user->getActiveWarning($warningType);

            $message = $this->getWarningMessage($warningType, $warning);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'warning' => [
                        'type' => $warning->type_label,
                        'title' => $warning->title,
                        'reason' => $warning->reason,
                        'expires_at' => $warning->expires_at?->format('d/m/Y H:i'),
                        'remaining_time' => $warning->remaining_time
                    ]
                ], 403);
            }

            // Redirect to dashboard for posting ban, back for others
            if ($warningType === UserWarning::TYPE_POSTING_BAN) {
                return redirect()->route('user.dashboard')->withErrors(['warning' => $message]);
            }

            return redirect()->back()->withErrors(['warning' => $message]);
        }

        return $next($request);
    }

    /**
     * Get warning message based on type.
     */
    private function getWarningMessage(string $type, UserWarning $warning): string
    {
        $baseMessage = match($type) {
            UserWarning::TYPE_POSTING_BAN => 'تم منعك من نشر الإعلانات',
            UserWarning::TYPE_CHAT_BAN => 'تم منعك من استخدام الشات',
            UserWarning::TYPE_BIDDING_BAN => 'تم منعك من المزايدة',
            UserWarning::TYPE_COMMENT_BAN => 'تم منعك من التعليق',
            default => 'تم توجيه تحذير لك'
        };

        $timeMessage = $warning->expires_at ?
            " حتى {$warning->expires_at->format('d/m/Y H:i')}" :
            " بشكل دائم";

        return $baseMessage . $timeMessage . ". السبب: {$warning->reason}";
    }
}
