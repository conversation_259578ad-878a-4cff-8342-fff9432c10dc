<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class BasicImageService
{
    /**
     * Store image without compression (fallback when GD/ImageMagick not available)
     */
    public function compressAndStore(UploadedFile $file, string $directory, array $options = []): array
    {
        // Generate unique filename
        $uuid = Str::uuid();
        $extension = $file->getClientOriginalExtension();
        $filename = $uuid . '.' . $extension;
        $path = $directory . '/' . $filename;

        // Get original file size
        $originalSize = $file->getSize();

        try {
            // Store the file directly (no compression)
            $storedPath = $file->storeAs($directory, $filename, 'public');
            
            if (!$storedPath) {
                return [
                    'success' => false,
                    'error' => 'Failed to store file'
                ];
            }

            // Get stored file size (same as original since no compression)
            $compressedSize = Storage::disk('public')->size($storedPath);
            $compressionRatio = 0; // No compression applied

            // Log storage results
            Log::info('Image stored successfully (Basic - No Compression)', [
                'original_filename' => $file->getClientOriginalName(),
                'new_filename' => $filename,
                'original_size' => $originalSize,
                'stored_size' => $compressedSize,
                'compression_ratio' => $compressionRatio . '%',
                'format' => $extension,
                'path' => $storedPath
            ]);

            return [
                'success' => true,
                'path' => $storedPath,
                'url' => Storage::disk('public')->url($storedPath),
                'filename' => $filename,
                'original_size' => $originalSize,
                'compressed_size' => $compressedSize,
                'compression_ratio' => $compressionRatio,
                'format' => $extension,
                'dimensions' => [
                    'width' => 'unknown',
                    'height' => 'unknown'
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Basic image storage failed', [
                'filename' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get optimized settings for different image types (basic version)
     */
    public function getOptimizedSettings(string $type): array
    {
        // Return basic settings (no actual compression will be applied)
        return match ($type) {
            'ad_images' => [
                'quality' => 80,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            'profile_avatar' => [
                'quality' => 85,
                'max_width' => 400,
                'max_height' => 400,
                'format' => 'jpg'
            ],
            'identity_documents' => [
                'quality' => 90,
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            'broker_documents' => [
                'quality' => 90,
                'max_width' => 1600,
                'max_height' => 1200,
                'format' => 'jpg'
            ],
            default => [
                'quality' => 75,
                'max_width' => 1200,
                'max_height' => 1200,
                'format' => 'jpg'
            ]
        };
    }

    /**
     * Validate image file
     */
    public function validateImage(UploadedFile $file, int $maxSizeMB = 10): array
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        // Check MIME type
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'
            ];
        }

        // Check extension
        if (!in_array(strtolower($file->getClientOriginalExtension()), $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Invalid file extension.'
            ];
        }

        // Check file size
        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'error' => "File size exceeds {$maxSizeMB}MB limit."
            ];
        }

        // Basic file validation
        if (!$file->isValid()) {
            return [
                'valid' => false,
                'error' => 'Invalid file upload.'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Format file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Delete image file from storage
     */
    public function deleteImage(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                Log::info('Image deleted successfully', ['path' => $path]);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Failed to delete image', [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
