@extends('partials.app')
@section('title', 'Register')
@section('content')

@include('layouts.breadcrumb', ['admin' => false, 'pageTitle' => 'Register'])

<div class="signup-section pt-120 pb-120">
    <div class="container">
        <div class="row d-flex justify-content-center">
            <div class="col-xl-6 col-lg-8 col-md-10">
                <div class="form-wrapper wow fadeInUp" data-wow-duration="1.5s" data-wow-delay=".2s"
                    style="visibility: visible; animation-duration: 1.5s; animation-delay: 0.2s; animation-name: fadeInUp;">
                    <div class="form-title">
                        <h3>Sign Up</h3>
                        <p>Do you already have an account? <a href="{{ route('user.login') }}">Log in here</a></p>
                    </div>
                    <form class="w-100" action="{{ route('user.register.handle') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <x-input-field name="first_name" type="text" label="First Name" placeholder="First Name" />
                            </div>
                            <div class="col-md-6">
                                <x-input-field name="last_name" type="text" label="Last Name" placeholder="Last Name" />
                            </div>
                            <div class="col-md-12">
                                <x-input-field name="username" type="text" label="Username" placeholder="Enter Your Username" />
                            </div>
                            <div class="col-md-12">
                                <x-input-field name="email" type="email" label="Enter Your Email" placeholder="Enter Your Email" />
                            </div>
                            <div class="col-md-12">
                                <x-input-field name="password" type="password" label="Password" placeholder="Create A Password" />
                            </div>
                            <div class="col-md-12">
                                <x-agree-checkbox class="form-agreement form-inner d-flex justify-content-between flex-wrap" id="html" name="terms" label="I agree to the Terms & Policy" />
                            </div>
                        </div>
                        <button class="account-btn">Create Account</button>
                    </form>

                    <!-- Social Login Section -->
                    <div class="social-login-section">
                        <div class="divider">
                            <span>Or continue with</span>
                        </div>
                        <a href="{{ route('user.auth.google') }}" class="google-login-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                            </svg>
                            Continue with Google
                        </a>
                    </div>

                    <div class="form-poicy-area">
                        <p>By clicking the "signup" button, you create a Bazaar account, and you agree to Bazaar's <a
                                href="#">Terms &amp; Conditions</a> &amp; <a href="#">Privacy Policy.</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<x-metric-card />

@endsection

@push('styles')
<style>
.social-login-section {
    margin: 20px 0;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 14px;
}

.google-login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.google-login-btn:hover {
    border-color: #4285F4;
    box-shadow: 0 2px 10px rgba(66, 133, 244, 0.1);
    color: #333;
    text-decoration: none;
}

.google-login-btn:focus {
    outline: none;
    border-color: #4285F4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}
</style>
@endpush