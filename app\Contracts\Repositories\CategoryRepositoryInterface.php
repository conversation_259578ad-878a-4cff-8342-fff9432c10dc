<?php

namespace App\Contracts\Repositories;

use Illuminate\Database\Eloquent\Collection;

interface CategoryRepositoryInterface
{
    /**
     * Get the categories with sub categories.
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */

    /**
     * Get primary categories.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPrimaryCategories(): Collection;

    /**
     * Get children of a specific category.
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getChildren(string $slug): Collection;

    /**
     * Get all descendants of a category.
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDescendants(string $slug): Collection;

    /**
     * Get category tree structure.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCategoryTree(): Collection;

    /**
     * Get category breadcrumb.
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getBreadcrumb(string $slug): Collection;
}