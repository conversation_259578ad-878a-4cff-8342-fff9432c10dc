<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Ad;
use App\Services\RelatedAdsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RelatedAdsController extends Controller
{
    public function __construct(
        private RelatedAdsService $relatedAdsService
    ) {}

    /**
     * جلب المزيد من الإعلانات ذات الصلة
     */
    public function loadMore(Request $request): JsonResponse
    {
        $request->validate([
            'ad_id' => 'required|string',
            'offset' => 'required|integer|min:0',
            'limit' => 'integer|min:1|max:12'
        ]);

        try {
            // البحث بالـ ID أو الـ slug
            $ad = Ad::where('id', $request->ad_id)
                    ->orWhere('slug', $request->ad_id)
                    ->firstOrFail();
            $offset = $request->offset;
            $limit = $request->limit ?? 8;

            // جلب إعلانات إضافية مع تخطي المحملة مسبقاً
            $relatedAds = $this->relatedAdsService->getRelatedAdsWithSponsored($ad, $limit + $offset);
            $newAds = $relatedAds->skip($offset)->take($limit);

            if ($newAds->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'has_more' => false,
                    'message' => 'لا توجد إعلانات إضافية'
                ]);
            }

            $adsData = $newAds->map(function ($ad) {
                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'slug' => $ad->slug,
                    'price' => $ad->price,
                    'currency' => $ad->currency ?? 'ج.م',
                    'city' => $ad->city ?? 'غير محدد',
                    'created_at' => $ad->created_at->diffForHumans(),
                    'image' => $ad->media->first()?->url ?? asset('assets/images/no-image.svg'),
                    'is_sponsored' => $ad->sponsoredAd && $ad->sponsoredAd->isActive(),
                    'url' => route('auction-details', $ad->slug)
                ];
            });

            // تحديد ما إذا كان هناك المزيد
            $totalAvailable = $this->relatedAdsService->getRelatedAdsWithSponsored($ad, 100)->count();
            $hasMore = ($offset + $limit) < $totalAvailable;

            return response()->json([
                'success' => true,
                'data' => $adsData,
                'has_more' => $hasMore,
                'total_loaded' => $offset + $newAds->count(),
                'message' => 'تم تحميل ' . $newAds->count() . ' إعلان إضافي'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحميل الإعلانات',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * البحث في الإعلانات ذات الصلة
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'ad_id' => 'required|string',
            'query' => 'required|string|min:2',
            'limit' => 'integer|min:1|max:20'
        ]);

        try {
            // البحث بالـ ID أو الـ slug
            $ad = Ad::where('id', $request->ad_id)
                    ->orWhere('slug', $request->ad_id)
                    ->firstOrFail();
            $searchQuery = $request->query;
            $limit = $request->limit ?? 12;

            // البحث في الإعلانات المرتبطة
            $relatedAds = $this->relatedAdsService->getRelatedAds($ad, 50);
            
            $filteredAds = $relatedAds->filter(function ($relatedAd) use ($searchQuery) {
                return stripos($relatedAd->title, $searchQuery) !== false ||
                       stripos($relatedAd->description, $searchQuery) !== false;
            })->take($limit);

            $adsData = $filteredAds->map(function ($ad) {
                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'slug' => $ad->slug,
                    'price' => $ad->price,
                    'currency' => $ad->currency ?? 'ج.م',
                    'city' => $ad->city ?? 'غير محدد',
                    'created_at' => $ad->created_at->diffForHumans(),
                    'image' => $ad->media->first()?->url ?? asset('assets/images/no-image.svg'),
                    'is_sponsored' => $ad->sponsoredAd && $ad->sponsoredAd->isActive(),
                    'url' => route('auction-details', $ad->slug)
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $adsData,
                'total_found' => $filteredAds->count(),
                'message' => 'تم العثور على ' . $filteredAds->count() . ' إعلان'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء البحث',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }
}
