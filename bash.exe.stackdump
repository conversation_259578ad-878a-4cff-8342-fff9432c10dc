Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBFF5D0000 ntdll.dll
7FFBFE7C0000 KERNEL32.DLL
7FFBFD020000 KERNELBASE.dll
7FFBFF010000 USER32.dll
7FFBFCDA0000 win32u.dll
7FFBFF300000 GDI32.dll
7FFBFCA90000 gdi32full.dll
7FFBFC9F0000 msvcp_win.dll
7FFBFCE40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBFD570000 advapi32.dll
7FFBFDB40000 msvcrt.dll
7FFBFEF60000 sechost.dll
7FFBFF330000 RPCRT4.dll
7FFBFC250000 CRYPTBASE.DLL
7FFBFCD20000 bcryptPrimitives.dll
7FFBFF2C0000 IMM32.DLL
