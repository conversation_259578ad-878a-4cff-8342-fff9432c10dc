@extends('partials.admin')
@section('title', 'Admin Ads Edit - ' . $ad->title)
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'ads.all'])

<div class="main-content app-content mt-0">
    <div class="side-app">

        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Ads Edit', 'hasBack' => true, 'backTitle' => 'Ads Listing', 'backUrl' => route('admin.ads.index')])
            <div class="row">
                <div class="col-lg-12">
                    <form class="card" method="POST" action="{{ route('admin.ads.update', $ad->slug) }}">
                        @method('PUT')
                        @csrf
                        <div class="card-header">
                            <div class="card-title">Edit Ad Listing</div>
                        </div>
                        <div class="card-body">
                            <x-input-item-field name="title" type="text" label="Ad Title" placeholder="Enter Ad Title" :value="$ad->title" />
                            <x-input-item-field name="price" type="number" label="Starting Price" placeholder="Enter Starting Price" value="{{ $ad->price }}" />
                            <!-- Row -->
                            <x-text-area-field name="description" label="Ad Description" placeholder="Enter Description" :value="$ad->description" :admin="true" />
                            <!--End Row-->
                            <!--Row-->
                            <div class="row">
                                <label class="col-md-3 form-label mb-4">Current Images :</label>
                                <div class="col-md-9">
                                    @if($ad->media->count() > 0)
                                        <div class="current-images-gallery">
                                            @foreach($ad->media as $media)
                                                <div class="current-image-item" data-media-id="{{ $media->id }}">
                                                    <img src="{{ $media->url }}" alt="Ad Image {{ $loop->iteration }}" class="current-image">
                                                    <div class="image-overlay">
                                                        <button type="button" class="btn btn-danger btn-sm delete-image-btn"
                                                                onclick="deleteImage({{ $media->id }}, this)"
                                                                title="Delete this image">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-primary btn-sm view-image-btn"
                                                                onclick="viewImage('{{ $media->url }}')"
                                                                title="View full size">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                    <div class="image-number">{{ $loop->iteration }}</div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            No images uploaded for this ad yet.
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <br>

                            <!--Row-->
                            <div class="row">
                                <label class="col-md-3 form-label mb-4">Upload New Images :</label>
                                <div class="col-md-9">
                                    <input id="demo" type="file" name="images[]" accept=".jpg, .png, .webp, .gif, .bmp, image/jpeg, image/png, image/webp, image/gif, image/bmp" multiple>
                                    <small class="form-text text-muted">You can upload up to 7 images. Supported formats: JPG, PNG, WebP, GIF, BMP (Max 10MB each)</small>
                                </div>
                            </div>
                            <br>
                            <x-input-item-field name="seller_name" type="text" label="Seller Name" placeholder="Enter Seller's Name" :value="$ad->seller_name"  />
                            <x-input-item-field name="seller_email" type="email" label="Seller Email" placeholder="Enter Seller's Email" value="{{ $ad->seller_email }}" />
                            <x-input-item-field name="seller_mobile" type="text" label="Seller Phone" placeholder="Enter Seller's Phone" value="{{ $ad->seller_mobile }}" />
                            <x-input-item-field name="seller_address" type="text" label="Seller Address" placeholder="Enter Seller's Address" value="{{ $ad->seller_address }}" />
                                <div class="form-group">
                                    <label class="form-label">هل يحتاج لمندوب</label>
                                    <div class="row">
                                        <div class="col-md-12 mb-2">
                                            <select name="needs_brokering" id="needs_brokering" class="form-control select2 form-select">
                                                <option value="1" @selected($ad->needs_brokering == 1)>yes</option>
                                                <option value="0" @selected($ad->needs_brokering == 0)>no</option>
                                            </select>
                                            <span class="text-danger">{{ $errors->first('country_id') }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label for="broker_commission">Seller Broker Commission:</label>
                                        <input type="number" 
                                               class="form-control" 
                                               name="broker_commission" 
                                               id="broker_commission" 
                                               value="{{ $ad->broker_commission }}" 
                                               step="any" 
                                               min="0" 
                                               placeholder="Enter broker commission" 
                                               required>
                                        <span class="text-danger">{{ $errors->first('broker_commission') }}</span>
                                    </div>
                                </div>

                        <!-- Categories and Attributes Section -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title"><i class="fas fa-tags me-2"></i>Categories & Attributes</h4>
                            </div>
                            <div class="card-body">
                                <!-- Current Category Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label fw-bold">Current Main Category:</label>
                                            <div class="p-3 bg-light rounded mb-3">
                                                <span class="badge bg-primary-transparent text-primary p-2 px-3">
                                                    <i class="fas fa-tag me-1"></i>
                                                    {{ $ad->category->name ?? 'N/A' }}
                                                </span>
                                            </div>
                                            <label class="form-label">Change Main Category:</label>
                                            <select name="category" id="main-category" class="form-control select2 form-select">
                                                <option value="">-- Keep Current --</option>
                                                @foreach(\App\Models\Category::whereIsRoot()->get() as $category)
                                                    <option value="{{ $category->slug }}" @selected($ad->category && $ad->category->slug == $category->slug)>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label fw-bold">Current Sub Category:</label>
                                            <div class="p-3 bg-light rounded mb-3">
                                                <span class="badge bg-secondary-transparent text-secondary p-2 px-3">
                                                    <i class="fas fa-tag me-1"></i>
                                                    {{ $ad->subCategory->name ?? 'N/A' }}
                                                </span>
                                            </div>
                                            <label class="form-label">Change Sub Category:</label>
                                            <select name="sub-category" id="sub-category" class="form-control select2 form-select">
                                                <option value="">-- Keep Current --</option>
                                                @if($ad->category)
                                                    @foreach($ad->category->children as $subCategory)
                                                        <option value="{{ $subCategory->slug }}" @selected($ad->subCategory && $ad->subCategory->slug == $subCategory->slug)>
                                                            {{ $subCategory->name }}
                                                        </option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Current Attributes -->
                                @if($ad->attributes && $ad->attributes->count() > 0)
                                    <div class="form-group">
                                        <label class="form-label fw-bold"><i class="fas fa-cogs me-2"></i>Current Attributes:</label>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead class="table-primary">
                                                    <tr>
                                                        <th style="width: 30%;">Attribute</th>
                                                        <th style="width: 50%;">Current Value</th>
                                                        <th style="width: 20%;">New Value</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($ad->attributes as $attribute)
                                                        <tr>
                                                            <td class="fw-bold">{{ $attribute->attribute_label }}</td>
                                                            <td>
                                                                <span class="badge bg-info-transparent text-info p-2 px-3">
                                                                    {{ $attribute->formatted_value }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                @if($attribute->attribute_type === 'select' && $attribute->categoryAttribute && $attribute->categoryAttribute->attribute_options)
                                                                    <select name="attributes[{{ $attribute->attribute_name }}]" class="form-control form-select">
                                                                        <option value="">-- Select --</option>
                                                                        @foreach($attribute->categoryAttribute->attribute_options as $key => $value)
                                                                            <option value="{{ $key }}" @selected($attribute->attribute_value == $key)>
                                                                                {{ $value }}
                                                                            </option>
                                                                        @endforeach
                                                                    </select>
                                                                @else
                                                                    <input type="text"
                                                                           name="attributes[{{ $attribute->attribute_name }}]"
                                                                           class="form-control"
                                                                           value="{{ $attribute->attribute_value }}"
                                                                           placeholder="Enter {{ $attribute->attribute_label }}">
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @else
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No attributes found for this ad. This might be because the ad was created before the attributes system was implemented.
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="">
                            <x-location-selectable :selected-country="$ad->country" /></div>
                            <x-input-item-field name="views" type="text" label="views" placeholder="views Ad" value="{{ $ad->views }}" />
                            <x-input-item-field name="expires_at" type="text" label="expires_at" placeholder="expires_at Ad" value="{{ $ad->expires_at }}" />
                            <x-ad-status-selectable :selected-status="$ad->status" />
                            <!--End Row-->
                        </div>

                        <!-- Admin Notes Section -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title"><i class="fas fa-sticky-note me-2"></i>Admin Notes & Suggestions</h4>
                            </div>
                            <div class="card-body">
                                <!-- Previous Notes -->
                                @if($ad->adminNotes && $ad->adminNotes->count() > 0)
                                    <div class="mb-4">
                                        <h6 class="fw-bold mb-3"><i class="fas fa-history me-2"></i>Previous Notes:</h6>
                                        <div class="notes-history">
                                            @foreach($ad->adminNotes->sortByDesc('created_at') as $note)
                                                <div class="alert alert-{{ $note->type_color }} alert-dismissible fade show">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div class="flex-grow-1">
                                                            <h6 class="alert-heading mb-2">
                                                                <i class="fas fa-user-shield me-1"></i>
                                                                {{ $note->admin->name ?? 'Admin' }} - {{ $note->type_name }}
                                                            </h6>
                                                            <p class="mb-1">{{ $note->note }}</p>
                                                            <small class="text-muted">
                                                                <i class="fas fa-clock me-1"></i>
                                                                {{ $note->created_at->format('M d, Y \a\t h:i A') }}
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif

                                <!-- Add New Note -->
                                <div class="form-group">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-comment-dots me-2"></i>Add Note/Suggestion for User:
                                    </label>
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <select name="note_type" class="form-control form-select">
                                                <option value="">-- No Note --</option>
                                                <option value="general">General Note</option>
                                                <option value="suggestion">Suggestion for Improvement</option>
                                                <option value="rejection">Rejection Reason</option>
                                            </select>
                                        </div>
                                    </div>
                                    <textarea name="admin_note"
                                              class="form-control"
                                              rows="4"
                                              placeholder="Write your note, suggestion, or rejection reason here. This will be visible to the user in their dashboard next to this ad.&#10;&#10;Examples:&#10;- For better visibility, consider choosing 'Electronics > Mobile Phones' category&#10;- Please add more detailed description about the product condition&#10;- Ad rejected due to inappropriate content"></textarea>
                                    <small class="form-text text-muted mt-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        This note will be visible to the user in their dashboard. Leave empty if no note is needed.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer">
                            <!--Row-->
                            <div class="row">
                                <div class="col-md-3"></div>
                                <div class="col-md-9">
                                    <button type="submit" class="btn btn-primary">Update Ad Listing</button>
                                    <a href="{{ route('admin.ads.show', $ad->slug) }}" class="btn btn-default float-end">Discard</a>
                                </div>
                            </div>
                            <!--End Row-->
                        </div>
                    </form>
                </div>
            </div>

        </div>
        <!-- CONTAINER END -->
    </div>
</div>


<!-- Image Viewer Modal -->
<div class="modal fade" id="imageViewerModal" tabindex="-1" aria-labelledby="imageViewerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageViewerModalLabel">View Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Full Size Image" class="img-fluid" style="max-height: 70vh;">
            </div>
        </div>
    </div>
</div>

<style>
/* Current Images Gallery */
.current-images-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.current-image-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    background: #f8f9fa;
    aspect-ratio: 1;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.current-image-item:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.current-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.current-image-item:hover .image-overlay {
    opacity: 1;
}

.image-overlay .btn {
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-number {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
}

.delete-image-btn:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.view-image-btn:hover {
    background-color: #0056b3 !important;
    border-color: #0056b3 !important;
}

/* Loading state */
.current-image-item.deleting {
    opacity: 0.5;
    pointer-events: none;
}

.current-image-item.deleting::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>

<script>
// View image in modal
function viewImage(imageUrl) {
    document.getElementById('modalImage').src = imageUrl;
    const modal = new bootstrap.Modal(document.getElementById('imageViewerModal'));
    modal.show();
}

// Delete image function
function deleteImage(mediaId, buttonElement) {
    if (!confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
        return;
    }

    const imageItem = buttonElement.closest('.current-image-item');
    imageItem.classList.add('deleting');

    // Send AJAX request to delete image
    fetch(`/admin/media/${mediaId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the image item from DOM
            imageItem.remove();

            // Show success message
            showAlert('success', 'Image deleted successfully');

            // Check if no images left
            const remainingImages = document.querySelectorAll('.current-image-item');
            if (remainingImages.length === 0) {
                const gallery = document.querySelector('.current-images-gallery');
                gallery.innerHTML = `
                    <div class="alert alert-info col-span-full">
                        <i class="fas fa-info-circle me-2"></i>
                        No images remaining for this ad.
                    </div>
                `;
            } else {
                // Update image numbers
                remainingImages.forEach((item, index) => {
                    const numberElement = item.querySelector('.image-number');
                    if (numberElement) {
                        numberElement.textContent = index + 1;
                    }
                });
            }
        } else {
            imageItem.classList.remove('deleting');
            showAlert('error', data.message || 'Failed to delete image');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        imageItem.classList.remove('deleting');
        showAlert('error', 'An error occurred while deleting the image');
    });
}

// Show alert function
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Insert alert at the top of the card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertAdjacentHTML('afterbegin', alertHtml);

    // Auto remove after 5 seconds
    setTimeout(() => {
        const alert = cardBody.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>

@endsection
@push('scripts')
<!-- INTERNAL File-Uploads Js-->
<script src="/plugin/fancyuploader/jquery.ui.widget.js"></script>
<script src="/plugin/fancyuploader/jquery.fileupload.js"></script>
<script src="/plugin/fancyuploader/jquery.iframe-transport.js"></script>
<script src="/plugin/fancyuploader/jquery.fancy-fileupload.js"></script>
<script src="/plugin/fancyuploader/fancy-uploader.js"></script>
@endpush
@push('styles')
<style>
    .ck .ck-powered-by {
        display: none !important;
    }
</style>
<script>
let subcategoryCache = {}; // تخزين البيانات مؤقتًا

    function getSubCategory(slug, selectedSubcategory = null) {
        if (subcategoryCache[slug]) {
            updateSubcategoryDropdown(subcategoryCache[slug], selectedSubcategory);
            return;
        }

        fetch(`/api/subcategories/${slug}`)
            .then(response => response.json())
            .then(data => {
                const subcategories = data.data;
                subcategoryCache[slug] = subcategories;
                updateSubcategoryDropdown(subcategories, selectedSubcategory);
            })
            .catch(error => console.error('Error fetching subcategories:', error));
    }

    function updateSubcategoryDropdown(subcategories, selectedSubcategory) {
        let options = `<option value="">Select Subcategory</option>`;
        subcategories.forEach(subcategory => {
            options += `<option value="${subcategory.slug}" ${selectedSubcategory == subcategory.slug ? 'selected' : ''}>
                            ${subcategory.name}
                        </option>`;
        });
        document.getElementById('subcategory').innerHTML = options;
        $('#subcategory').select2('destroy').select2();
    }

    document.addEventListener("DOMContentLoaded", function () {
        const selectedCategory = document.getElementById("category").value;
        const selectedSubcategory = "{{ old('subcategory', $selectedSubcategory ?? '') }}";

        if (selectedCategory) {
            getSubCategory(selectedCategory, selectedSubcategory);
        }
    });

    $('#category').on('change', function () {
        const category = this.value;
        getSubCategory(category);
    });

    // Admin Category Management for new selects
    document.addEventListener("DOMContentLoaded", function () {
        // Handle main category change for admin edit
        $('#main-category').on('change', function () {
            const selectedCategory = this.value;
            const subCategorySelect = $('#sub-category');

            console.log('Main category changed to:', selectedCategory);
            console.log('Sub category select element:', subCategorySelect);

            if (selectedCategory && selectedCategory !== '') {
                console.log('Fetching subcategories for:', selectedCategory);

                // Show loading
                subCategorySelect.empty();
                subCategorySelect.append('<option value="">Loading...</option>');

                // Fetch subcategories for the selected main category
                fetch(`/api/subcategories/${selectedCategory}`)
                    .then(response => {
                        console.log('Response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Subcategories response:', data);

                        // Clear current options
                        subCategorySelect.empty();
                        subCategorySelect.append('<option value="">-- Select Sub Category --</option>');

                        // Add new options
                        if (data.success && data.data && data.data.length > 0) {
                            data.data.forEach(subcategory => {
                                console.log('Adding subcategory:', subcategory.name);
                                subCategorySelect.append(`<option value="${subcategory.slug}">${subcategory.name}</option>`);
                            });
                            console.log(`Added ${data.data.length} subcategories`);
                        } else {
                            console.log('No subcategories found for:', selectedCategory);
                            subCategorySelect.append('<option value="">No subcategories available</option>');
                        }

                        // Refresh Select2 if available
                        if (typeof $.fn.select2 !== 'undefined') {
                            subCategorySelect.select2();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching subcategories:', error);
                        subCategorySelect.empty();
                        subCategorySelect.append('<option value="">Error loading subcategories</option>');
                        if (typeof $.fn.select2 !== 'undefined') {
                            subCategorySelect.select2();
                        }
                    });
            } else {
                // Clear subcategories if no main category selected
                subCategorySelect.empty();
                subCategorySelect.append('<option value="">-- Keep Current --</option>');
                if (typeof $.fn.select2 !== 'undefined') {
                    subCategorySelect.select2();
                }
            }
        });
    });
</script>
@endpush