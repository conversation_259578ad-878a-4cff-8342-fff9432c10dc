<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_warnings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('admin_id');
            $table->uuid('report_id')->nullable(); // إذا كان التحذير بسبب إبلاغ
            $table->enum('type', [
                'posting_ban',      // منع النشر
                'chat_ban',         // منع الشات
                'bidding_ban',      // منع المزايدة
                'comment_ban',      // منع التعليق
                'general_warning'   // تحذير عام
            ]);
            $table->string('title'); // عنوان التحذير
            $table->text('reason'); // سبب التحذير
            $table->text('admin_notes')->nullable(); // ملاحظات الأدمن
            $table->timestamp('expires_at')->nullable(); // تاريخ انتهاء التحذير
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('admin_id')->references('id')->on('admins')->onDelete('cascade');
            $table->foreign('report_id')->references('id')->on('reports')->onDelete('set null');

            $table->index(['user_id', 'is_active']);
            $table->index(['type', 'is_active']);
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_warnings');
    }
};
