<?php

namespace App\Helpers;

use HTMLPurifier;
use HTMLPurifier_Config;

class SecurityHelper
{
    /**
     * Clean and sanitize HTML content to prevent XSS attacks
     * 
     * @param string|null $content
     * @return string
     */
    public static function cleanHtml(?string $content): string
    {
        if (empty($content)) {
            return '';
        }

        // إزالة جميع HTML tags
        $cleaned = strip_tags($content);
        
        // إزالة المسافات الزائدة
        $cleaned = trim($cleaned);
        
        // تحويل الأحرف الخاصة إلى HTML entities
        $cleaned = htmlspecialchars($cleaned, ENT_QUOTES, 'UTF-8');
        
        return $cleaned;
    }

    /**
     * Clean content using HTMLPurifier for more advanced cleaning
     *
     * @param string|null $content
     * @return string
     */
    public static function purifyHtml(?string $content): string
    {
        if (empty($content)) {
            return '';
        }

        // استخدام strip_tags بدلاً من HTMLPurifier لتجنب مشاكل التكوين
        $cleaned = strip_tags($content);

        // إزالة المحتوى الخطير
        $dangerousPatterns = [
            '/javascript:/i',
            '/vbscript:/i',
            '/data:/i',
            '/on\w+\s*=/i', // onclick, onload, etc.
        ];

        foreach ($dangerousPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // إزالة المسافات الزائدة
        $cleaned = trim($cleaned);

        // تحويل الأحرف الخاصة
        $cleaned = htmlspecialchars($cleaned, ENT_QUOTES, 'UTF-8');

        return $cleaned;
    }

    /**
     * Validate and clean description for ads
     * 
     * @param string|null $description
     * @return string
     */
    public static function cleanAdDescription(?string $description): string
    {
        if (empty($description)) {
            return '';
        }

        // استخدام HTMLPurifier للتنظيف المتقدم
        $cleaned = self::purifyHtml($description);
        
        // التحقق من الحد الأدنى للطول بعد التنظيف
        if (strlen($cleaned) < 10) {
            throw new \InvalidArgumentException('Description must be at least 10 characters after cleaning.');
        }
        
        return $cleaned;
    }

    /**
     * Clean user input for general text fields
     * 
     * @param string|null $input
     * @return string
     */
    public static function cleanTextInput(?string $input): string
    {
        if (empty($input)) {
            return '';
        }

        // إزالة HTML tags
        $cleaned = strip_tags($input);
        
        // إزالة المسافات الزائدة
        $cleaned = trim($cleaned);
        
        // تحويل الأحرف الخاصة
        $cleaned = htmlspecialchars($cleaned, ENT_QUOTES, 'UTF-8');
        
        return $cleaned;
    }

    /**
     * Validate and clean URL input
     * 
     * @param string|null $url
     * @return string|null
     */
    public static function cleanUrl(?string $url): ?string
    {
        if (empty($url)) {
            return null;
        }

        // تنظيف URL
        $cleaned = filter_var($url, FILTER_SANITIZE_URL);
        
        // التحقق من صحة URL
        if (!filter_var($cleaned, FILTER_VALIDATE_URL)) {
            throw new \InvalidArgumentException('Invalid URL format.');
        }
        
        return $cleaned;
    }

    /**
     * Clean and validate email
     * 
     * @param string|null $email
     * @return string|null
     */
    public static function cleanEmail(?string $email): ?string
    {
        if (empty($email)) {
            return null;
        }

        // تنظيف email
        $cleaned = filter_var($email, FILTER_SANITIZE_EMAIL);
        
        // التحقق من صحة email
        if (!filter_var($cleaned, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email format.');
        }
        
        return $cleaned;
    }

    /**
     * Clean phone number
     *
     * @param string|null $phone
     * @return string|null
     */
    public static function cleanPhone(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // إزالة جميع الأحرف غير الرقمية والرموز المسموحة
        $cleaned = preg_replace('/[^0-9\s\-\+\(\)]/', '', $phone);

        // إزالة المسافات الزائدة
        $cleaned = trim($cleaned);

        return $cleaned;
    }

    /**
     * Clean description for admin with limited HTML support
     *
     * @param string|null $description
     * @return string
     */
    public static function cleanAdminDescription(?string $description): string
    {
        if (empty($description)) {
            return '';
        }

        // إزالة المحتوى الخطير أولاً
        $dangerousPatterns = [
            '/javascript:/i',
            '/vbscript:/i',
            '/data:/i',
            '/on\w+\s*=/i', // onclick, onload, etc.
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/<iframe\b[^>]*>/i',
            '/<object\b[^>]*>/i',
            '/<embed\b[^>]*>/i',
            '/<form\b[^>]*>/i',
        ];

        $cleaned = $description;
        foreach ($dangerousPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // ثم السماح ببعض HTML tags الآمنة للأدمن
        $allowedTags = '<p><br><strong><b><em><i><u><ul><ol><li><h1><h2><h3><h4><h5><h6>';
        $cleaned = strip_tags($cleaned, $allowedTags);

        // إزالة المسافات الزائدة
        $cleaned = trim($cleaned);

        // التحقق من الحد الأدنى للطول
        if (strlen(strip_tags($cleaned)) < 10) {
            throw new \InvalidArgumentException('Description must be at least 10 characters after cleaning.');
        }

        return $cleaned;
    }
}
