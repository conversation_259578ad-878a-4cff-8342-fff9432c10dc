<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CleanHtmlRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return;
        }

        // التحقق من وجود HTML tags
        if ($value !== strip_tags($value)) {
            $fail('The :attribute field cannot contain HTML tags.');
            return;
        }

        // التحقق من وجود JavaScript
        if (preg_match('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', $value)) {
            $fail('The :attribute field cannot contain JavaScript code.');
            return;
        }

        // التحقق من وجود أحداث JavaScript
        if (preg_match('/\bon\w+\s*=/i', $value)) {
            $fail('The :attribute field cannot contain JavaScript events.');
            return;
        }

        // التحقق من وجود محتوى ضار آخر
        $dangerousPatterns = [
            '/javascript:/i',
            '/vbscript:/i',
            '/data:/i',
            '/<iframe/i',
            '/<object/i',
            '/<embed/i',
            '/<form/i',
        ];

        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                $fail('The :attribute field contains potentially dangerous content.');
                return;
            }
        }
    }
}
