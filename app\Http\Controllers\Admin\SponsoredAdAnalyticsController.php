<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SponsoredAd;
use App\Models\Ad;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SponsoredAdAnalyticsController extends Controller
{
    /**
     * عرض لوحة التحليلات الرئيسية
     */
    public function index(Request $request): View
    {
        $dateRange = $this->getDateRange($request);
        
        // الإحصائيات العامة
        $generalStats = $this->getGeneralStats($dateRange);
        
        // أفضل الإعلانات أداءً
        $topPerforming = $this->getTopPerformingAds($dateRange);
        
        // إحصائيات الأولوية
        $priorityStats = $this->getPriorityStats($dateRange);
        
        // إحصائيات الموقع الجغرافي
        $locationStats = $this->getLocationStats($dateRange);
        
        // إحصائيات الفئات
        $categoryStats = $this->getCategoryStats($dateRange);
        
        // بيانات الرسم البياني للمشاهدات والنقرات
        $chartData = $this->getChartData($dateRange);
        
        return view('admin.sponsored-ads.analytics', [
            'generalStats' => $generalStats,
            'topPerforming' => $topPerforming,
            'priorityStats' => $priorityStats,
            'locationStats' => $locationStats,
            'categoryStats' => $categoryStats,
            'chartData' => $chartData,
            'dateRange' => $dateRange
        ]);
    }

    /**
     * الحصول على نطاق التاريخ
     */
    private function getDateRange(Request $request): array
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        
        return [
            'start' => Carbon::parse($startDate)->startOfDay(),
            'end' => Carbon::parse($endDate)->endOfDay()
        ];
    }

    /**
     * الحصول على الإحصائيات العامة
     */
    private function getGeneralStats(array $dateRange): array
    {
        $query = SponsoredAd::whereBetween('created_at', [$dateRange['start'], $dateRange['end']]);
        
        $totalSponsored = $query->count();
        $activeSponsored = $query->where('status', 'active')->count();
        $totalViews = $query->sum('views_count');
        $totalClicks = $query->sum('clicks_count');
        $totalRevenue = $query->sum('cost');
        
        $averageCTR = $totalViews > 0 ? round(($totalClicks / $totalViews) * 100, 2) : 0;
        $averageCPC = $totalClicks > 0 ? round($totalRevenue / $totalClicks, 2) : 0;
        
        return [
            'total_sponsored' => $totalSponsored,
            'active_sponsored' => $activeSponsored,
            'total_views' => $totalViews,
            'total_clicks' => $totalClicks,
            'total_revenue' => $totalRevenue,
            'average_ctr' => $averageCTR,
            'average_cpc' => $averageCPC,
            'conversion_rate' => $totalViews > 0 ? round(($totalClicks / $totalViews) * 100, 2) : 0
        ];
    }

    /**
     * الحصول على أفضل الإعلانات أداءً
     */
    private function getTopPerformingAds(array $dateRange): \Illuminate\Database\Eloquent\Collection
    {
        return SponsoredAd::with(['ad.user', 'ad.category'])
                          ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                          ->where('views_count', '>', 0)
                          ->orderByRaw('(clicks_count / GREATEST(views_count, 1)) DESC')
                          ->orderBy('clicks_count', 'desc')
                          ->limit(10)
                          ->get()
                          ->map(function ($sponsoredAd) {
                              $sponsoredAd->ctr = $sponsoredAd->views_count > 0 
                                  ? round(($sponsoredAd->clicks_count / $sponsoredAd->views_count) * 100, 2) 
                                  : 0;
                              return $sponsoredAd;
                          });
    }

    /**
     * الحصول على إحصائيات الأولوية
     */
    private function getPriorityStats(array $dateRange): \Illuminate\Database\Eloquent\Collection
    {
        return SponsoredAd::selectRaw('
                priority,
                COUNT(*) as count,
                SUM(views_count) as total_views,
                SUM(clicks_count) as total_clicks,
                SUM(cost) as total_revenue,
                AVG(CASE WHEN views_count > 0 THEN (clicks_count / views_count) * 100 ELSE 0 END) as avg_ctr
            ')
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->groupBy('priority')
            ->orderBy('priority')
            ->get()
            ->map(function ($stat) {
                $stat->priority_text = $this->getPriorityText($stat->priority);
                $stat->avg_ctr = round($stat->avg_ctr, 2);
                return $stat;
            });
    }

    /**
     * الحصول على إحصائيات الموقع الجغرافي
     */
    private function getLocationStats(array $dateRange): \Illuminate\Database\Eloquent\Collection
    {
        return SponsoredAd::with('country')
                          ->selectRaw('
                              country_id,
                              COUNT(*) as count,
                              SUM(views_count) as total_views,
                              SUM(clicks_count) as total_clicks,
                              SUM(cost) as total_revenue
                          ')
                          ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                          ->whereNotNull('country_id')
                          ->groupBy('country_id')
                          ->orderBy('total_revenue', 'desc')
                          ->limit(10)
                          ->get();
    }

    /**
     * الحصول على إحصائيات الفئات
     */
    private function getCategoryStats(array $dateRange): \Illuminate\Database\Eloquent\Collection
    {
        return SponsoredAd::with(['ad.category'])
                          ->selectRaw('
                              ads.category_id,
                              COUNT(*) as count,
                              SUM(sponsored_ads.views_count) as total_views,
                              SUM(sponsored_ads.clicks_count) as total_clicks,
                              SUM(sponsored_ads.cost) as total_revenue
                          ')
                          ->join('ads', 'sponsored_ads.ad_id', '=', 'ads.id')
                          ->whereBetween('sponsored_ads.created_at', [$dateRange['start'], $dateRange['end']])
                          ->groupBy('ads.category_id')
                          ->orderBy('total_revenue', 'desc')
                          ->limit(10)
                          ->get();
    }

    /**
     * الحصول على بيانات الرسم البياني
     */
    private function getChartData(array $dateRange): array
    {
        $days = [];
        $views = [];
        $clicks = [];
        $revenue = [];
        
        $currentDate = $dateRange['start']->copy();
        
        while ($currentDate <= $dateRange['end']) {
            $dayData = SponsoredAd::whereDate('created_at', $currentDate)
                                  ->selectRaw('
                                      SUM(views_count) as daily_views,
                                      SUM(clicks_count) as daily_clicks,
                                      SUM(cost) as daily_revenue
                                  ')
                                  ->first();
            
            $days[] = $currentDate->format('Y-m-d');
            $views[] = $dayData->daily_views ?? 0;
            $clicks[] = $dayData->daily_clicks ?? 0;
            $revenue[] = $dayData->daily_revenue ?? 0;
            
            $currentDate->addDay();
        }
        
        return [
            'days' => $days,
            'views' => $views,
            'clicks' => $clicks,
            'revenue' => $revenue
        ];
    }

    /**
     * الحصول على نص الأولوية
     */
    private function getPriorityText(int $priority): string
    {
        return match($priority) {
            1 => 'عالية جداً',
            2 => 'عالية',
            3 => 'متوسطة',
            4 => 'منخفضة',
            5 => 'منخفضة جداً',
            default => 'غير محدد'
        };
    }

    /**
     * تصدير البيانات إلى CSV
     */
    public function exportCSV(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        
        $sponsoredAds = SponsoredAd::with(['ad.user', 'ad.category', 'country'])
                                   ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                                   ->get();
        
        $filename = 'sponsored_ads_analytics_' . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($sponsoredAds) {
            $file = fopen('php://output', 'w');
            
            // CSV Headers
            fputcsv($file, [
                'ID',
                'عنوان الإعلان',
                'المستخدم',
                'الفئة',
                'الأولوية',
                'الدولة',
                'المشاهدات',
                'النقرات',
                'معدل النقر %',
                'التكلفة',
                'الحالة',
                'تاريخ الإنشاء'
            ]);
            
            foreach ($sponsoredAds as $sponsoredAd) {
                $ctr = $sponsoredAd->views_count > 0 
                    ? round(($sponsoredAd->clicks_count / $sponsoredAd->views_count) * 100, 2) 
                    : 0;
                
                fputcsv($file, [
                    $sponsoredAd->id,
                    $sponsoredAd->ad->title ?? '',
                    $sponsoredAd->ad->user->name ?? '',
                    $sponsoredAd->ad->category->name ?? '',
                    $this->getPriorityText($sponsoredAd->priority),
                    $sponsoredAd->country->name ?? '',
                    $sponsoredAd->views_count,
                    $sponsoredAd->clicks_count,
                    $ctr,
                    $sponsoredAd->cost,
                    $sponsoredAd->status,
                    $sponsoredAd->created_at->format('Y-m-d H:i:s')
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }

    /**
     * الحصول على تقرير مفصل لإعلان ممول محدد
     */
    public function detailedReport(SponsoredAd $sponsoredAd): View
    {
        $sponsoredAd->load(['ad.user', 'ad.category', 'country', 'state']);
        
        // إحصائيات يومية للشهر الماضي
        $dailyStats = $this->getDailyStats($sponsoredAd);
        
        // مقارنة الأداء
        $performanceComparison = $this->getPerformanceComparison($sponsoredAd);
        
        return view('admin.sponsored-ads.detailed-report', [
            'sponsoredAd' => $sponsoredAd,
            'dailyStats' => $dailyStats,
            'performanceComparison' => $performanceComparison
        ]);
    }

    /**
     * الحصول على الإحصائيات اليومية
     */
    private function getDailyStats(SponsoredAd $sponsoredAd): array
    {
        // This would require a separate tracking table for daily stats
        // For now, return mock data structure
        return [
            'days' => [],
            'views' => [],
            'clicks' => [],
            'ctr' => []
        ];
    }

    /**
     * الحصول على مقارنة الأداء
     */
    private function getPerformanceComparison(SponsoredAd $sponsoredAd): array
    {
        $avgStats = SponsoredAd::where('priority', $sponsoredAd->priority)
                               ->where('id', '!=', $sponsoredAd->id)
                               ->selectRaw('
                                   AVG(views_count) as avg_views,
                                   AVG(clicks_count) as avg_clicks,
                                   AVG(CASE WHEN views_count > 0 THEN (clicks_count / views_count) * 100 ELSE 0 END) as avg_ctr
                               ')
                               ->first();
        
        return [
            'current_ctr' => $sponsoredAd->views_count > 0 
                ? round(($sponsoredAd->clicks_count / $sponsoredAd->views_count) * 100, 2) 
                : 0,
            'average_ctr' => round($avgStats->avg_ctr ?? 0, 2),
            'current_views' => $sponsoredAd->views_count,
            'average_views' => round($avgStats->avg_views ?? 0),
            'current_clicks' => $sponsoredAd->clicks_count,
            'average_clicks' => round($avgStats->avg_clicks ?? 0)
        ];
    }
}
