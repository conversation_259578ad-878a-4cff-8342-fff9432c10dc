<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Ad;
use App\Models\Media;
use App\Models\Review;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class DeleteExpiredAds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:delete-expired {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete ads that are older than 30 days along with their images and reviews';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Starting expired ads cleanup...');

        // Get ads older than 30 days
        $expiredDate = Carbon::now()->subDays(30);
        $expiredAds = Ad::where('created_at', '<', $expiredDate)
                        ->with(['media', 'reviews'])
                        ->get();

        if ($expiredAds->isEmpty()) {
            $this->info('✅ No expired ads found.');
            return 0;
        }

        $this->info("📊 Found {$expiredAds->count()} expired ads to delete.");

        $deletedAdsCount = 0;
        $deletedImagesCount = 0;
        $deletedReviewsCount = 0;
        $deletedFilesCount = 0;

        foreach ($expiredAds as $ad) {
            $this->processExpiredAd($ad, $deletedAdsCount, $deletedImagesCount, $deletedReviewsCount, $deletedFilesCount);
        }

        // Summary
        $this->info('');
        $this->info('📈 Cleanup Summary:');
        $this->info("   🗑️  Deleted Ads: {$deletedAdsCount}");
        $this->info("   🖼️  Deleted Images: {$deletedImagesCount}");
        $this->info("   📝 Deleted Reviews: {$deletedReviewsCount}");
        $this->info("   📁 Deleted Files: {$deletedFilesCount}");
        $this->info('');
        $this->info('✅ Expired ads cleanup completed successfully!');

        // Log the cleanup
        Log::info('Expired ads cleanup completed', [
            'deleted_ads' => $deletedAdsCount,
            'deleted_images' => $deletedImagesCount,
            'deleted_reviews' => $deletedReviewsCount,
            'deleted_files' => $deletedFilesCount,
            'cutoff_date' => $expiredDate->toDateTimeString()
        ]);

        return 0;
    }

    /**
     * Process a single expired ad for deletion
     */
    private function processExpiredAd(Ad $ad, &$deletedAdsCount, &$deletedImagesCount, &$deletedReviewsCount, &$deletedFilesCount)
    {
        $this->info("🔄 Processing ad: {$ad->title} (ID: {$ad->id})");

        if ($this->option('dry-run')) {
            $this->warn("   [DRY RUN] Would delete ad with {$ad->media->count()} images and {$ad->reviews->count()} reviews");
            return;
        }

        try {
            // Delete associated images from storage
            foreach ($ad->media as $media) {
                if ($media->url && Storage::disk('public')->exists($media->url)) {
                    Storage::disk('public')->delete($media->url);
                    $deletedFilesCount++;
                    $this->line("   🖼️  Deleted image: {$media->url}");
                }
                $deletedImagesCount++;
            }

            // Count reviews before deletion
            $reviewsCount = $ad->reviews->count();
            $deletedReviewsCount += $reviewsCount;

            // Delete the ad (this will cascade delete media and reviews due to foreign key constraints)
            $ad->delete();
            $deletedAdsCount++;

            $this->info("   ✅ Deleted ad: {$ad->title} ({$ad->media->count()} images, {$reviewsCount} reviews)");

        } catch (\Exception $e) {
            $this->error("   ❌ Failed to delete ad {$ad->id}: {$e->getMessage()}");

            Log::error('Failed to delete expired ad', [
                'ad_id' => $ad->id,
                'ad_title' => $ad->title,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
