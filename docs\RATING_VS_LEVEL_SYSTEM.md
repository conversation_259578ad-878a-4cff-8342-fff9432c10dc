# نظام التقييم مقابل نظام المستوى (Rating vs Level System)

## نظرة عامة

يوجد في النظام مفهومان مختلفان لتقييم المستخدمين:

1. **Rating (التقييم)** - تقييم جودة المستخدم من المستخدمين الآخرين
2. **Level (المستوى)** - مستوى نشاط المستخدم حسب عدد الإعلانات المنشورة

## 1. نظام التقييم (Rating System) ⭐

### المصدر:
- **جدول**: `user_ratings`
- **العمود**: `average_rating` في جدول `users`
- **الحساب**: متوسط التقييمات من المستخدمين الآخرين

### النطاق:
- **من**: 0.0 إلى 5.0 نجوم
- **الدقة**: رقم عشري (مثل: 4.3)

### كيفية الحساب:
```php
$averageRating = $user->receivedRatings()->avg('rating');
$user->update(['average_rating' => $averageRating]);
```

### الاستخدام في الكود:
```php
// الحصول على التقييم
$rating = $user->average_rating ?? 0;
$rating = $user->rating; // باستخدام accessor

// عرض النجوم
@for($i = 1; $i <= 5; $i++)
    <i class="fas fa-star {{ $i <= round($user->rating) ? 'filled' : '' }}"></i>
@endfor
```

### صفحة التقييم:
- **URL**: `/users/{user}/rate`
- **الوظيفة**: السماح للمستخدمين بتقييم بعضهم البعض

## 2. نظام المستوى (Level System) 🏆

### المصدر:
- **جدول**: `users`
- **العمود**: `Number_Ads`
- **الحساب**: عدد الإعلانات المنشورة (`PUBLISHED`)

### النطاق:
- **من**: 1 إلى 50 مستوى
- **الحساب**: `min(floor($user->Number_Ads / 20) + 1, 50)`

### كيفية الحساب:
```php
// كل 20 إعلان = مستوى واحد
$level = min(floor($user->Number_Ads / 20) + 1, 50);
```

### أمثلة على المستويات:
- **0-19 إعلان**: Level 1
- **20-39 إعلان**: Level 2
- **40-59 إعلان**: Level 3
- **...**: ...
- **980+ إعلان**: Level 50 (الحد الأقصى)

### الاستخدام في الكود:
```php
// الحصول على المستوى
$level = min(floor($user->Number_Ads / 20) + 1, 50);
$level = $user->level; // باستخدام accessor

// عرض المستوى
<span class="level-badge">Level {{ $user->level }}</span>
```

## 3. الاستخدام الصحيح في الملفات

### Dashboard (`dashboard/user/index.blade.php`):
```php
@php
    $user = auth()->user();
    $averageRating = $user->average_rating ?? 0; // التقييم من 1-5 نجوم
    $level = min(floor($user->Number_Ads / 20) + 1, 50); // المستوى من 1-50 حسب عدد الإعلانات
@endphp
```

### User Profile (`pages/user-profile/index.blade.php`):
```php
@php
    $averageRating = $user->average_rating ?? 0; // التقييم من 1-5 نجوم
    $level = min(floor($user->Number_Ads / 20) + 1, 50); // المستوى من 1-50 حسب عدد الإعلانات
@endphp
```

### Auction Details (`pages/live-auction/show.blade.php`):
```php
@php
    $averageRating = $ad->user->receivedRatings ? $ad->user->receivedRatings->avg('rating') : 0; // التقييم
    $level = min(floor($ad->user->Number_Ads / 20) + 1, 50); // المستوى
@endphp
```

### Add Listing (`pages/live-auction/create.blade.php`):
```php
// للنجوم (التقييم)
<i class="fas fa-star {{ $i <= round(auth()->user()->average_rating ?? 0) ? 'active' : '' }}"></i>

// للمستوى
$level = floor(auth()->user()->Number_Ads / 20) + 1;
```

## 4. Helper Methods في User Model

### للتقييم:
```php
$user->rating; // التقييم (0-5)
$user->formatted_rating_with_stars; // "4.3 ★"
$user->stars; // عدد النجوم المملوءة (1-5)
```

### للمستوى:
```php
$user->level; // المستوى (1-50)
$user->formatted_level; // "Level 15"
$user->Number_Ads; // عدد الإعلانات المنشورة
```

## 5. أمثلة عملية

### عرض معلومات المستخدم:
```blade
<div class="user-info">
    <!-- التقييم -->
    <div class="rating">
        <span>{{ $user->formatted_rating_with_stars }}</span>
        <div class="stars">
            @for($i = 1; $i <= 5; $i++)
                <i class="fas fa-star {{ $i <= $user->stars ? 'filled' : '' }}"></i>
            @endfor
        </div>
        <span>({{ $user->total_ratings }} reviews)</span>
    </div>
    
    <!-- المستوى -->
    <div class="level">
        <span class="level-badge">{{ $user->formatted_level }}</span>
        <span>({{ $user->Number_Ads }} ads published)</span>
    </div>
</div>
```

### في الـ Cards:
```blade
<!-- كارد التقييم -->
<div class="rating-card">
    <h4>User Rating</h4>
    <div class="rating-display">
        <span class="rating-value">{{ number_format($user->rating, 1) }}</span>
        <div class="stars">...</div>
    </div>
</div>

<!-- كارد المستوى -->
<div class="level-card">
    <h4>User Level</h4>
    <div class="level-display">
        <span class="level-badge">Level {{ $user->level }}</span>
        <span class="ads-count">{{ $user->Number_Ads }} ads</span>
    </div>
</div>
```

## 6. ملاحظات مهمة

### للتقييم (Rating):
- **يتغير**: عند تقييم المستخدمين له
- **المصدر**: تفاعل المستخدمين
- **الهدف**: قياس جودة الخدمة
- **النطاق**: 0-5 نجوم

### للمستوى (Level):
- **يتغير**: عند نشر إعلانات جديدة
- **المصدر**: نشاط المستخدم
- **الهدف**: قياس مستوى النشاط
- **النطاق**: 1-50 مستوى

### قاعدة ذهبية:
- **استخدم `average_rating`** للنجوم والتقييم
- **استخدم `Number_Ads`** للمستوى والنشاط
- **لا تخلط** بين المفهومين أبداً

## 7. التحديثات التلقائية

### التقييم:
- يتم تحديثه عند إضافة تقييم جديد
- يحسب متوسط جميع التقييمات

### المستوى:
- يتم تحديثه تلقائياً عند موافقة الأدمن على إعلان
- يستخدم Observer Pattern لضمان الدقة
