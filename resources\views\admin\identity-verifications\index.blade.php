@extends('partials.admin')
@section('title', 'Identity Verifications Management')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'identity-verifications'])
@php use Illuminate\Support\Str; @endphp

<div class="main-content app-content mt-0">
    <div class="side-app">
        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Identity Verifications Management'])
            
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Identity Verifications Management</h3>
                        </div>
                        <div class="card-body">
                            <!-- Navigation Tabs -->
                            <ul class="nav nav-tabs" id="verificationTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                                        <i class="fas fa-clock text-warning me-2"></i>
                                        Pending ({{ $pendingCount }})
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Approved ({{ $approvedCount }})
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab">
                                        <i class="fas fa-times-circle text-danger me-2"></i>
                                        Rejected ({{ $rejectedCount }})
                                    </button>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content mt-4" id="verificationTabsContent">
                                <!-- Pending Tab -->
                                <div class="tab-pane fade show active" id="pending" role="tabpanel">
                                    @if($pendingVerifications->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>National ID</th>
                                                        <th>Submitted At</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($pendingVerifications as $verification)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                            <img src="{{ $verification->user->avatar ? \Illuminate\Support\Facades\Storage::url($verification->user->avatar) : get_random_avatar() }}">
                                                                     alt="{{ $verification->user->name }}" class="avatar avatar-sm rounded-circle me-2">
                                                                <div>
                                                                    <strong>{{ $verification->user->name }}</strong><br>
                                                                    <small class="text-muted">{{ $verification->user->email }}</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $verification->national_id }}</td>
                                                        <td>{{ $verification->created_at->format('Y-m-d H:i') }}</td>
                                                        <td>
                                                            <a href="{{ route('admin.identity-verifications.show', $verification) }}" 
                                                               class="btn btn-sm btn-primary">
                                                                <i class="fas fa-eye me-1"></i>Review
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        {{ $pendingVerifications->links() }}
                                    @else
                                        <div class="text-center py-4">
                                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                            <h5>No Pending Verifications</h5>
                                            <p class="text-muted">All identity verification requests have been processed.</p>
                                        </div>
                                    @endif
                                </div>

                                <!-- Approved Tab -->
                                <div class="tab-pane fade" id="approved" role="tabpanel">
                                    @if($approvedVerifications->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>National ID</th>
                                                        <th>Approved At</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($approvedVerifications as $verification)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <img src="{{ $verification->user->avatar ? \Illuminate\Support\Facades\Storage::url($verification->user->avatar) : get_random_avatar() }}" 
                                                                     alt="{{ $verification->user->name }}" class="avatar avatar-sm rounded-circle me-2">
                                                                <div>
                                                                    <strong>{{ $verification->user->name }}</strong><br>
                                                                    <small class="text-muted">{{ $verification->user->email }}</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $verification->national_id }}</td>
                                                        <td>{{ $verification->approved_at->format('Y-m-d H:i') }}</td>
                                                        <td>
                                                            <a href="{{ route('admin.identity-verifications.show', $verification) }}" 
                                                               class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye me-1"></i>View
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        {{ $approvedVerifications->links() }}
                                    @else
                                        <div class="text-center py-4">
                                            <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                            <h5>No Approved Verifications</h5>
                                            <p class="text-muted">No identity verifications have been approved yet.</p>
                                        </div>
                                    @endif
                                </div>

                                <!-- Rejected Tab -->
                                <div class="tab-pane fade" id="rejected" role="tabpanel">
                                    @if($rejectedVerifications->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>National ID</th>
                                                        <th>Rejected At</th>
                                                        <th>Reason</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($rejectedVerifications as $verification)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <img src="{{ $verification->user->avatar ? \Illuminate\Support\Facades\Storage::url($verification->user->avatar) : get_random_avatar() }}" 
                                                                     alt="{{ $verification->user->name }}" class="avatar avatar-sm rounded-circle me-2">
                                                                <div>
                                                                    <strong>{{ $verification->user->name }}</strong><br>
                                                                    <small class="text-muted">{{ $verification->user->email }}</small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $verification->national_id }}</td>
                                                        <td>{{ $verification->rejected_at->format('Y-m-d H:i') }}</td>
                                                        <td>
                                                            <small class="text-muted">{{ \Illuminate\Support\Str::limit($verification->rejection_reason, 50) }}</small>
                                                        </td>
                                                        <td>
                                                            <a href="{{ route('admin.identity-verifications.show', $verification) }}" 
                                                               class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye me-1"></i>View
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        {{ $rejectedVerifications->links() }}
                                    @else
                                        <div class="text-center py-4">
                                            <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                                            <h5>No Rejected Verifications</h5>
                                            <p class="text-muted">No identity verifications have been rejected.</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- CONTAINER END -->
    </div>
</div>

@endsection

@push('scripts')
<script>
// Auto-refresh pending count every 30 seconds
setInterval(function() {
    if ($('#pending-tab').hasClass('active')) {
        location.reload();
    }
}, 30000);
</script>
@endpush
