<div class="messenger-sendCard" id="sendCard">
    <!-- Block Alert (will be shown if user is blocked) -->
    <div id="blockAlert" class="block-alert" style="display: none;">
        <i class="fas fa-ban"></i>
        <strong>تم حظرك من قبل هذا المستخدم</strong>
        <p>لا يمكنك إرسال رسائل لهذا المستخدم</p>
    </div>

    <form id="message-form" method="POST" action="{{ route('send.message') }}" enctype="multipart/form-data">
        @csrf
        <textarea readonly='readonly' name="message" class="m-send app-scroll" placeholder="Type a message.."></textarea>
        <button disabled='disabled' class="send-button"><span class="fas fa-paper-plane"></span></button>
    </form>
</div>

<script>
// فحص الحظر عند تغيير المحادثة
function checkBlockStatus(userId) {
    if (!userId) return;

    fetch('/chat/check-block', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.is_blocked) {
            // إظهار تنبيه الحظر وإخفاء نموذج الإرسال
            document.getElementById('blockAlert').style.display = 'block';
            document.getElementById('message-form').style.display = 'none';
        } else {
            // إخفاء تنبيه الحظر وإظهار نموذج الإرسال
            document.getElementById('blockAlert').style.display = 'none';
            document.getElementById('message-form').style.display = 'flex';
        }
    })
    .catch(error => {
        console.error('Error checking block status:', error);
    });
}

// مراقبة تغيير المحادثة
document.addEventListener('DOMContentLoaded', function() {
    // فحص الحظر عند تحميل الصفحة
    const currentUserId = getMessengerFocus();
    if (currentUserId) {
        checkBlockStatus(currentUserId);
    }

    // فحص الحظر عند النقر على محادثة جديدة
    document.addEventListener('click', function(e) {
        if (e.target.closest('.listItem')) {
            setTimeout(() => {
                const newUserId = getMessengerFocus();
                if (newUserId) {
                    checkBlockStatus(newUserId);
                }
            }, 500);
        }
    });
});
</script>
