<?php

namespace Tests\Feature;

use App\Helpers\SecurityHelper;
use App\Rules\AdminHtmlRule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class AdminSecurityTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_allows_safe_html_for_admin()
    {
        $safeHtml = '<p>This is a <strong>safe</strong> description with <em>formatting</em>.</p>';
        $cleaned = SecurityHelper::cleanAdminDescription($safeHtml);
        
        $this->assertStringContainsString('<p>', $cleaned);
        $this->assertStringContainsString('<strong>', $cleaned);
        $this->assertStringContainsString('<em>', $cleaned);
        $this->assertStringContainsString('safe', $cleaned);
    }

    /** @test */
    public function it_removes_dangerous_content_for_admin()
    {
        $dangerousHtml = '<p>Safe content</p><script>alert("XSS")</script><iframe src="evil.com"></iframe>';
        $cleaned = SecurityHelper::cleanAdminDescription($dangerousHtml);
        
        $this->assertStringContainsString('<p>', $cleaned);
        $this->assertStringContainsString('Safe content', $cleaned);
        $this->assertStringNotContainsString('<script>', $cleaned);
        $this->assertStringNotContainsString('<iframe>', $cleaned);
        $this->assertStringNotContainsString('alert("XSS")', $cleaned);
    }

    /** @test */
    public function it_validates_admin_html_rule()
    {
        $rule = new AdminHtmlRule();
        
        // Test valid HTML
        $validator = Validator::make(
            ['description' => '<p>This is a <strong>valid</strong> description with HTML.</p>'],
            ['description' => $rule]
        );
        $this->assertTrue($validator->passes());

        // Test dangerous JavaScript
        $validator = Validator::make(
            ['description' => '<script>alert("XSS")</script>'],
            ['description' => $rule]
        );
        $this->assertFalse($validator->passes());

        // Test JavaScript events
        $validator = Validator::make(
            ['description' => '<div onclick="alert(\'XSS\')">Click me</div>'],
            ['description' => $rule]
        );
        $this->assertFalse($validator->passes());
    }

    /** @test */
    public function it_allows_common_formatting_tags()
    {
        $formattedContent = '
            <h1>Main Title</h1>
            <h2>Subtitle</h2>
            <p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
            <ul>
                <li>First item</li>
                <li>Second item with <u>underlined</u> text</li>
            </ul>
            <ol>
                <li>Numbered item</li>
                <li>Another numbered item</li>
            </ol>
        ';
        
        $cleaned = SecurityHelper::cleanAdminDescription($formattedContent);
        
        $this->assertStringContainsString('<h1>', $cleaned);
        $this->assertStringContainsString('<h2>', $cleaned);
        $this->assertStringContainsString('<p>', $cleaned);
        $this->assertStringContainsString('<strong>', $cleaned);
        $this->assertStringContainsString('<em>', $cleaned);
        $this->assertStringContainsString('<ul>', $cleaned);
        $this->assertStringContainsString('<ol>', $cleaned);
        $this->assertStringContainsString('<li>', $cleaned);
        $this->assertStringContainsString('<u>', $cleaned);
    }

    /** @test */
    public function it_prevents_dangerous_urls()
    {
        $dangerousContent = '
            <p>Safe content</p>
            <a href="javascript:alert(\'XSS\')">Dangerous link</a>
            <img src="data:text/html,<script>alert(\'XSS\')</script>">
        ';
        
        $cleaned = SecurityHelper::cleanAdminDescription($dangerousContent);
        
        $this->assertStringContainsString('Safe content', $cleaned);
        $this->assertStringNotContainsString('javascript:', $cleaned);
        $this->assertStringNotContainsString('data:', $cleaned);
    }

    /** @test */
    public function it_enforces_minimum_text_length()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Description must be at least 10 characters after cleaning.');
        
        // Short content with only HTML tags
        SecurityHelper::cleanAdminDescription('<p></p><br><strong></strong>');
    }

    /** @test */
    public function it_handles_mixed_content()
    {
        $mixedContent = '
            <h2>Product Description</h2>
            <p>This is a <strong>great product</strong> with the following features:</p>
            <ul>
                <li>High quality materials</li>
                <li>Excellent <em>performance</em></li>
                <li>Affordable price</li>
            </ul>
            <p>Contact us for more information!</p>
        ';
        
        $cleaned = SecurityHelper::cleanAdminDescription($mixedContent);
        
        // Should preserve formatting
        $this->assertStringContainsString('<h2>', $cleaned);
        $this->assertStringContainsString('<p>', $cleaned);
        $this->assertStringContainsString('<strong>', $cleaned);
        $this->assertStringContainsString('<em>', $cleaned);
        $this->assertStringContainsString('<ul>', $cleaned);
        $this->assertStringContainsString('<li>', $cleaned);
        
        // Should preserve content
        $this->assertStringContainsString('Product Description', $cleaned);
        $this->assertStringContainsString('great product', $cleaned);
        $this->assertStringContainsString('High quality materials', $cleaned);
    }

    /** @test */
    public function it_removes_form_elements()
    {
        $formContent = '
            <p>Safe content</p>
            <form action="malicious.com">
                <input type="text" name="data">
                <button type="submit">Submit</button>
            </form>
        ';
        
        $cleaned = SecurityHelper::cleanAdminDescription($formContent);
        
        $this->assertStringContainsString('Safe content', $cleaned);
        $this->assertStringNotContainsString('<form', $cleaned);
        $this->assertStringNotContainsString('<input', $cleaned);
        $this->assertStringNotContainsString('<button', $cleaned);
    }
}
