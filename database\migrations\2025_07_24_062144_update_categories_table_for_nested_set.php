<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Category;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Add only the nested set columns we need (parent_id already exists)
            $table->unsignedInteger('_lft')->default(0);
            $table->unsignedInteger('_rgt')->default(0);
            $table->unsignedInteger('depth')->default(0);

            // Add index for better performance
            $table->index(['_lft', '_rgt', 'parent_id']);
        });

        // Convert existing data to nested set structure
        $this->convertToNestedSet();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex(['_lft', '_rgt', 'parent_id']);
            $table->dropColumn(['_lft', '_rgt', 'depth']);
        });
    }

    /**
     * Convert existing parent_id based structure to nested set
     */
    private function convertToNestedSet()
    {
        // Get all root categories (parent_id is null)
        $rootCategories = Category::whereNull('parent_id')->get();

        $left = 1;

        foreach ($rootCategories as $category) {
            $left = $this->buildNestedSet($category, $left, 0);
        }
    }

    /**
     * Recursively build nested set values
     */
    private function buildNestedSet($category, $left, $depth)
    {
        $right = $left + 1;

        // Get children of this category
        $children = Category::where('parent_id', $category->id)->get();

        foreach ($children as $child) {
            $right = $this->buildNestedSet($child, $right, $depth + 1);
        }

        // Update the category with nested set values
        Category::where('id', $category->id)->update([
            '_lft' => $left,
            '_rgt' => $right,
            'depth' => $depth
        ]);

        return $right + 1;
    }
};
