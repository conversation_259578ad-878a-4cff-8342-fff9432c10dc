<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\CategoryAttribute;

class QuickAttributesSeeder extends Seeder
{
    public function run(): void
    {
        echo "🚀 Adding quick attributes for remaining categories...\n";
        
        // Common attributes for different category types
        $this->addVehicleAttributes();
        $this->addPropertyAttributes();
        $this->addMobileAttributes();
        $this->addJobAttributes();
        $this->addFurnitureAttributes();
        $this->addElectronicsAttributes();
        $this->addFashionAttributes();
        $this->addPetAttributes();
        $this->addKidsAttributes();
        $this->addHobbyAttributes();
        $this->addBusinessAttributes();
        $this->addServiceAttributes();
        
        echo "✅ Quick attributes added successfully!\n";
    }
    
    private function addVehicleAttributes()
    {
        echo "🚗 Adding vehicle attributes...\n";
        
        // Common vehicle attributes
        $commonVehicleAttributes = [
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'excellent' => 'ممتاز'
            ], 1, true],
            ['brand', 'الماركة', 'input', null, 2, false],
            ['price_negotiable', 'السعر قابل للتفاوض', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 3, false]
        ];
        
        $vehicleSlugs = [
            'atarat-obtaryat-ozyot-oakssoarat', 'ktaa-ghyar-alsyarat', 
            'motosyklat-oakssoarat', 'koarb-omrakb-mayy', 
            'shahnat-thkyl-ohaflat-omrkbat-akhr'
        ];
        
        foreach ($vehicleSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonVehicleAttributes);
        }
    }
    
    private function addPropertyAttributes()
    {
        echo "🏠 Adding property attributes...\n";
        
        $commonPropertyAttributes = [
            ['area', 'المساحة', 'input', null, 1, false],
            ['location', 'الموقع', 'input', null, 2, false],
            ['price_negotiable', 'السعر قابل للتفاوض', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 3, false]
        ];
        
        $propertySlugs = [
            'shkk-llaygar', 'fylat-llbyaa', 'fylat-llaygar', 
            'byot-aatlat-llbyaa', 'byot-aatlat-llaygar', 
            'aakarat-tgary-llbyaa', 'aakarat-tgary-llaygar', 'mbany-oarady'
        ];
        
        foreach ($propertySlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonPropertyAttributes);
        }
    }
    
    private function addMobileAttributes()
    {
        echo "📱 Adding mobile attributes...\n";
        
        $commonMobileAttributes = [
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'excellent' => 'ممتاز'
            ], 1, true],
            ['brand', 'الماركة', 'input', null, 2, false],
            ['warranty', 'الضمان', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 3, false]
        ];
        
        $mobileSlugs = ['tablt', 'akssoarat-mobayl-otablt', 'arkam-mobayl'];
        
        foreach ($mobileSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonMobileAttributes);
        }
    }
    
    private function addJobAttributes()
    {
        echo "💼 Adding job attributes...\n";
        
        $commonJobAttributes = [
            ['job_type', 'نوع الوظيفة', 'select', [
                'full_time' => 'دوام كامل',
                'part_time' => 'دوام جزئي',
                'contract' => 'عقد'
            ], 1, true],
            ['experience', 'الخبرة المطلوبة', 'select', [
                'entry' => 'مبتدئ',
                '1-3' => '1-3 سنوات',
                '3+' => 'أكثر من 3 سنوات'
            ], 2, false],
            ['salary_range', 'نطاق الراتب', 'input', null, 3, false]
        ];
        
        $jobSlugs = [
            'mhasb-omaly-omsrfy', 'hnds', 'msmmyn', 'khdm-aamlaaa-omrakz-atsal',
            'aamal-ofnyyn', 'adar-oastsharat', 'saykyn-otosyl', 'taalym',
            'moard-bshry', 'syah-osfr-odyaf', 'tknologya-almaalomat-oalatsalat',
            'tsoyk-oaalakat-aaam', 'tby-oraaay-shy-otmryd', 'mbyaaat',
            'skrtary', 'hras-oamn', 'kanony-omhama'
        ];
        
        foreach ($jobSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonJobAttributes);
        }
    }
    
    private function addFurnitureAttributes()
    {
        echo "🪑 Adding furniture attributes...\n";
        
        $commonFurnitureAttributes = [
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'excellent' => 'ممتاز'
            ], 1, true],
            ['material', 'الخامة', 'select', [
                'wood' => 'خشب',
                'metal' => 'معدن',
                'plastic' => 'بلاستيك'
            ], 2, false],
            ['color', 'اللون', 'input', null, 3, false]
        ];
        
        $furnitureSlugs = [
            'athath-mktby', 'athath', 'dykor-mnzly-oakssoarat',
            'adoat-oakssoarat-hmam-omtbkh', 'akmsh-ofrash',
            'hdyk-khargy', 'adaaa', 'athath-mtnoaaakhr'
        ];
        
        foreach ($furnitureSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonFurnitureAttributes);
        }
    }
    
    private function addElectronicsAttributes()
    {
        echo "📺 Adding electronics attributes...\n";
        
        $commonElectronicsAttributes = [
            ['brand', 'الماركة', 'input', null, 1, false],
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'excellent' => 'ممتاز'
            ], 2, true],
            ['warranty', 'الضمان', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 3, false]
        ];
        
        $electronicsSlugs = [
            'tlfzyon-sot-fydyo', 'kmbyotr-akssoarat', 'alaaab-fydyo-aghz-alaaab',
            'kamyrat-tsoyr', 'aghz-mnzly'
        ];
        
        foreach ($electronicsSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonElectronicsAttributes);
        }
    }
    
    private function addFashionAttributes()
    {
        echo "👗 Adding fashion attributes...\n";
        
        $commonFashionAttributes = [
            ['size', 'المقاس', 'select', [
                's' => 'S',
                'm' => 'M',
                'l' => 'L',
                'xl' => 'XL'
            ], 1, true],
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'excellent' => 'ممتاز'
            ], 2, true],
            ['color', 'اللون', 'input', null, 3, false]
        ];
        
        $fashionSlugs = [
            'mlabs-nsayy', 'mlabs-rgaly',
            'akssoarat-nsayy-msthdrat-tgmyl-aanay-shkhsy',
            'akssoarat-rgaly-aanay-shkhsy'
        ];
        
        foreach ($fashionSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonFashionAttributes);
        }
    }
    
    private function addPetAttributes()
    {
        echo "🐕 Adding pet attributes...\n";
        
        $commonPetAttributes = [
            ['age', 'العمر', 'input', null, 1, false],
            ['gender', 'الجنس', 'select', [
                'male' => 'ذكر',
                'female' => 'أنثى'
            ], 2, false],
            ['health_status', 'الحالة الصحية', 'select', [
                'excellent' => 'ممتاز',
                'good' => 'جيد'
            ], 3, false]
        ];
        
        $petSlugs = [
            'klab', 'ktt', 'tyor', 'asmak-zyn', 'khyol', 'abkar-oaghnam-ogmal',
            'taaam-gaf-llhyoanat-oaltyor-oalasmak',
            'adoat-oakssoarat-raaay-alhyoanat-oaltyor-oalasmak',
            'hyoanat-alyf-akhr'
        ];
        
        foreach ($petSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonPetAttributes);
        }
    }
    
    private function addKidsAttributes()
    {
        echo "👶 Adding kids attributes...\n";
        
        $commonKidsAttributes = [
            ['age_group', 'الفئة العمرية', 'select', [
                'newborn' => 'حديث الولادة',
                'infant' => 'رضيع',
                'toddler' => 'طفل صغير'
            ], 1, true],
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل'
            ], 2, true],
            ['gender', 'الجنس', 'select', [
                'boy' => 'ولد',
                'girl' => 'بنت',
                'unisex' => 'للجنسين'
            ], 3, false]
        ];
        
        $kidsSlugs = [
            'raaay-shy-lltfl-oalam', 'mlabs-atfal', 'adoat-ataaam-alatfal',
            'asr-atfal-aarbat-mkaaad-syar', 'alaaab', 'mstlzmat-atfal-akhr',
            'mshayat-atfal-krasy-atfal-krasy-ataaam'
        ];
        
        foreach ($kidsSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonKidsAttributes);
        }
    }
    
    private function addHobbyAttributes()
    {
        echo "📚 Adding hobby attributes...\n";
        
        $commonHobbyAttributes = [
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'excellent' => 'ممتاز'
            ], 1, true],
            ['age_group', 'الفئة العمرية', 'select', [
                'kids' => 'أطفال',
                'adults' => 'بالغين',
                'all' => 'جميع الأعمار'
            ], 2, false],
            ['brand', 'الماركة', 'input', null, 3, false]
        ];
        
        $hobbySlugs = [
            'thf-mktnyat', 'dragat-hoayy', 'ktb', 'alaaab-lohy-alaaab-ork',
            'aflam-mosyk', 'alat-mosyky', 'maadat-ryady', 'adoat-drasy',
            'tthakr-ksaym', 'hkayb-sfr', 'ashyaaa-akhr'
        ];
        
        foreach ($hobbySlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonHobbyAttributes);
        }
    }
    
    private function addBusinessAttributes()
    {
        echo "🏭 Adding business attributes...\n";
        
        $commonBusinessAttributes = [
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل'
            ], 1, true],
            ['brand', 'الماركة', 'input', null, 2, false],
            ['warranty', 'الضمان', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 3, false]
        ];
        
        $businessSlugs = [
            'zraaa', 'anshaaaat', 'maadat-snaaay', 'maadat-tby',
            'maadat-mtaaam', 'aaamal-kaml-llbyaa', 'aaamal-osnaaa-ozraaa-akhr'
        ];
        
        foreach ($businessSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonBusinessAttributes);
        }
    }
    
    private function addServiceAttributes()
    {
        echo "🔧 Adding service attributes...\n";
        
        $commonServiceAttributes = [
            ['service_type', 'نوع الخدمة', 'select', [
                'one_time' => 'مرة واحدة',
                'recurring' => 'متكررة'
            ], 1, true],
            ['availability', 'التوفر', 'select', [
                '24_7' => '24/7',
                'business_hours' => 'ساعات العمل'
            ], 2, false],
            ['experience', 'سنوات الخبرة', 'input', null, 3, false]
        ];
        
        $serviceSlugs = [
            'khdmat-aaamal', 'khdmat-syarat', 'khdmat-faaalyat', 'khdmat-sh-ogmal',
            'khdmat-mnzly-osyan', 'khdmat-tby', 'khdmat-nkl', 'khdmat-hyoanat-alyf',
            'khdmat-taalymy', 'khdmat-akhr'
        ];
        
        foreach ($serviceSlugs as $slug) {
            $this->addAttributesToCategory($slug, $commonServiceAttributes);
        }
    }
    
    private function addAttributesToCategory($slug, $attributes)
    {
        $category = Category::where('slug', $slug)->first();
        if (!$category) {
            echo "  ❌ Category not found: {$slug}\n";
            return;
        }
        
        foreach ($attributes as $attr) {
            CategoryAttribute::create([
                'category_id' => $category->id,
                'attribute_name' => $attr[0],
                'attribute_label' => $attr[1],
                'attribute_type' => $attr[2],
                'attribute_options' => $attr[3],
                'sort_order' => $attr[4],
                'is_required' => $attr[5]
            ]);
        }
        
        echo "  ✅ Added " . count($attributes) . " attributes to {$category->name}\n";
    }
}
