<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IdentityVerification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'national_id',
        'front_image',
        'back_image',
        'status',
        'rejection_reason',
        'rejected_at',
        'approved_at',
    ];

    protected $casts = [
        'rejected_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    // الحالات المختلفة
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * التحقق من إمكانية التقديم مرة أخرى (بعد 5 أيام من الرفض)
     */
    public function canReapply(): bool
    {
        if ($this->status !== self::STATUS_REJECTED) {
            return false;
        }

        if (!$this->rejected_at) {
            return true;
        }

        return now()->diffInDays($this->rejected_at) >= 5;
    }

    /**
     * الحصول على رابط صورة البطاقة الأمامية
     */
    public function getFrontImageUrlAttribute(): string
    {
        return asset('storage/' . $this->front_image);
    }

    /**
     * الحصول على رابط صورة البطاقة الخلفية
     */
    public function getBackImageUrlAttribute(): string
    {
        return asset('storage/' . $this->back_image);
    }
}
