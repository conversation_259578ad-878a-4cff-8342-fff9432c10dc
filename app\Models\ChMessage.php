<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Chatify\Traits\UUID;

class ChMessage extends Model
{
    use UUID;

    protected $fillable = [
        'from_id',
        'to_id',
        'body',
        'attachment',
        'seen'
    ];

    protected $casts = [
        'seen' => 'boolean'
    ];

    /**
     * Get the user who sent the message.
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_id');
    }

    /**
     * Get the user who received the message.
     */
    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_id');
    }
}
