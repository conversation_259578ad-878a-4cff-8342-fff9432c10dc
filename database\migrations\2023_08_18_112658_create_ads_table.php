<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ads', function (Blueprint $table) {
            $table->boolean('needs_brokering')->default(false);
            $table->decimal('broker_commission', 8, 2)->nullable();
            $table->boolean('is_sponsored')->default(false);
            $table->decimal('sponsorship_cost', 8, 2)->nullable();
            $table->integer('sponsored_minutes')->default(0);
            $table->timestamp('sponsored_end_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->foreignId('sub_category_id')->nullable()->constrained('categories')->onDelete('cascade');
            $table->string('unique_ad_code', 191)->unique()->nullable();
            
            $table->string('title');
            $table->string('slug')->unique()->index();
            $table->longText('description')->nullable();
            $table->smallInteger('type')->nullable();
            $table->unsignedDecimal('price', 12, 4)->nullable();
            $table->boolean('is_negotiable')->default(false);
            

            $table->string('seller_name')->nullable();
            $table->string('seller_email')->nullable();
            $table->string('seller_mobile', 15)->nullable();
            $table->string('seller_address')->nullable();
            
            $table->integer('Number_Ads')->default(0); // التقييم من 1 إلى 60
            $table->integer('rank')->default('0'); // الرتبة (تاجر، admin، غير موثوق، إلخ)
            $table->boolean('is_trusted')->default(false); // هل المستخدم موثوق؟
            
            
            $table->boolean('mark_as_urgent')->default(false);
            $table->smallInteger('status')->default(1); // 1: pending for review, 2: published, 3: rejected, 4: expired
            $table->unsignedBigInteger('views')->default(0);
            
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('cascade');
            $table->foreignId('state_id')->nullable();
            $table->string('city_id')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ads');
    }
};
