<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('deals', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('email', 191)->unique();
            $table->string('unique_ad_code', 191)->unique()->nullable();
            $table->uuid('ad_id');
            $table->timestamps();

        });
    }

    public function down(): void
    {
        Schema::dropIfExists('deals');
    }
};

