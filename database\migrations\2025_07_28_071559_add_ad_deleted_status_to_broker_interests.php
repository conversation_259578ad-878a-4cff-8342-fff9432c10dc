<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تعديل الـ enum لإضافة حالة 'ad_deleted'
        DB::statement("ALTER TABLE broker_interests MODIFY COLUMN status ENUM('active', 'withdrawn', 'expired', 'ad_deleted') DEFAULT 'active'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إرجاع الـ enum للحالة الأصلية
        DB::statement("ALTER TABLE broker_interests MODIFY COLUMN status ENUM('active', 'withdrawn', 'expired') DEFAULT 'active'");
    }
};
