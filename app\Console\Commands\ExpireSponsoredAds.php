<?php

namespace App\Console\Commands;

use App\Models\SponsoredAd;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ExpireSponsoredAds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sponsored-ads:expire {--force : Force expire all active ads}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire sponsored ads that have reached their expiration time';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting sponsored ads expiration check...');

        // جلب الإعلانات النشطة التي انتهت صلاحيتها
        $expiredAds = SponsoredAd::where('is_active', true)
            ->where('status', 'active')
            ->where('expires_at', '<=', now())
            ->get();

        if ($this->option('force')) {
            // فرض انتهاء جميع الإعلانات النشطة (للاختبار)
            $expiredAds = SponsoredAd::where('is_active', true)
                ->where('status', 'active')
                ->get();
            $this->warn('Force mode enabled - expiring ALL active sponsored ads');
        }

        if ($expiredAds->isEmpty()) {
            $this->info('No expired sponsored ads found.');
            return 0;
        }

        $expiredCount = 0;
        $bar = $this->output->createProgressBar($expiredAds->count());
        $bar->start();

        foreach ($expiredAds as $sponsoredAd) {
            try {
                // تحويل الإعلان لغير نشط بدلاً من حذفه
                $sponsoredAd->expireSponsorship();

                $this->line("\n✓ Expired sponsored ad ID: {$sponsoredAd->id} (Ad: {$sponsoredAd->ad->title})");
                $expiredCount++;

            } catch (\Exception $e) {
                $this->error("\n✗ Failed to expire sponsored ad ID: {$sponsoredAd->id} - {$e->getMessage()}");
            }

            $bar->advance();
        }

        $bar->finish();

        // مسح الكاش المتعلق بالإعلانات الممولة
        Cache::tags(['sponsored_ads', 'ads'])->flush();

        $this->newLine(2);
        $this->info("✅ Successfully expired {$expiredCount} sponsored ads");
        $this->info("🗑️  Cache cleared for sponsored ads");

        // إحصائيات سريعة
        $activeCount = SponsoredAd::active()->count();
        $pendingCount = SponsoredAd::where('status', 'pending')->count();
        $expiredTotalCount = SponsoredAd::where('status', 'expired')->count();

        $this->table(['Status', 'Count'], [
            ['Active', $activeCount],
            ['Pending', $pendingCount],
            ['Expired', $expiredTotalCount],
        ]);

        return Command::SUCCESS;
    }
}
