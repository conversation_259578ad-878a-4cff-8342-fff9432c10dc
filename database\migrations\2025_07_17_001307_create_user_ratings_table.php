<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserRatingsTable extends Migration
{
    public function up()
    {
        Schema::create('user_ratings', function (Blueprint $table) {
            $table->id();
            $table->uuid('rater_id');
            $table->uuid('rated_id');
            $table->decimal('rating', 2, 1);
            $table->timestamps();

            $table->unique(['rater_id', 'rated_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_ratings');
    }
}
