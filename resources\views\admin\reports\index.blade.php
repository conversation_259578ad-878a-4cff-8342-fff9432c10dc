@extends('partials.admin')

@section('title', 'Reports Management')

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'reports'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Reports Management', 'hasBack' => true, 'backTitle' => 'Dashboard', 'backUrl' => route('admin.dashboard')])

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="counter-icon bg-primary-gradient box-shadow-primary brround ms-auto">
                                    <i class="fas fa-flag text-white"></i>
                                </div>
                                <div class="ms-auto">
                                    <p class="mb-1 text-muted">Total Reports</p>
                                    <h3 class="mb-0 font-weight-semibold">{{ $stats['total'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="counter-icon bg-warning-gradient box-shadow-warning brround ms-auto">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div class="ms-auto">
                                    <p class="mb-1 text-muted">Pending</p>
                                    <h3 class="mb-0 font-weight-semibold">{{ $stats['pending'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="counter-icon bg-info-gradient box-shadow-info brround ms-auto">
                                    <i class="fas fa-ad text-white"></i>
                                </div>
                                <div class="ms-auto">
                                    <p class="mb-1 text-muted">Ads</p>
                                    <h3 class="mb-0 font-weight-semibold">{{ $stats['ads'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="counter-icon bg-success-gradient box-shadow-success brround ms-auto">
                                    <i class="fas fa-comments text-white"></i>
                                </div>
                                <div class="ms-auto">
                                    <p class="mb-1 text-muted">Comments</p>
                                    <h3 class="mb-0 font-weight-semibold">{{ $stats['comments'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="counter-icon bg-danger-gradient box-shadow-danger brround ms-auto">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                                <div class="ms-auto">
                                    <p class="mb-1 text-muted">Users</p>
                                    <h3 class="mb-0 font-weight-semibold">{{ $stats['users'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="counter-icon bg-secondary-gradient box-shadow-secondary brround ms-auto">
                                    <i class="fas fa-star text-white"></i>
                                </div>
                                <div class="ms-auto">
                                    <p class="mb-1 text-muted">Reviews</p>
                                    <h3 class="mb-0 font-weight-semibold">{{ $stats['reviews'] }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Quick Actions</h4>
                        </div>
                        <div class="card-body">
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.reports.by-type', 'ads') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-ad me-2"></i>Ad Reports ({{ $stats['ads'] }})
                                </a>
                                <a href="{{ route('admin.reports.by-type', 'comments') }}" class="btn btn-outline-success">
                                    <i class="fas fa-comments me-2"></i>Comment Reports ({{ $stats['comments'] }})
                                </a>
                                <a href="{{ route('admin.reports.by-type', 'users') }}" class="btn btn-outline-danger">
                                    <i class="fas fa-users me-2"></i>User Reports ({{ $stats['users'] }})
                                </a>
                                <a href="{{ route('admin.reports.by-type', 'reviews') }}" class="btn btn-outline-warning">
                                    <i class="fas fa-star me-2"></i>Review Reports ({{ $stats['reviews'] }})
                                </a>
                                <a href="{{ route('admin.reports.by-type', 'user-ratings') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-user-star me-2"></i>User Rating Reports ({{ $stats['user_ratings'] }})
                                </a>
                                <a href="{{ route('admin.reports.by-type', 'messages') }}" class="btn btn-outline-info">
                                    <i class="fas fa-envelope me-2"></i>Message Reports ({{ $stats['messages'] }})
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Reports -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Recent Reports</h4>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    Filter by Status
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="?status=pending">Pending</a></li>
                                    <li><a class="dropdown-item" href="?status=reviewed">Reviewed</a></li>
                                    <li><a class="dropdown-item" href="?status=resolved">Resolved</a></li>
                                    <li><a class="dropdown-item" href="?status=dismissed">Dismissed</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ route('admin.reports.index') }}">All</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($recentReports->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Type</th>
                                                <th>Reporter</th>
                                                <th>Reason</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($recentReports as $report)
                                                <tr>
                                                    <td><code>{{ substr($report->id, 0, 8) }}</code></td>
                                                    <td>
                                                        <span class="badge bg-info">{{ $report->getTypeLabel() }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <img src="{{ $report->reporter->avatar ?? '/assets/images/default-avatar.png' }}" 
                                                                 alt="{{ $report->reporter->name }}" 
                                                                 class="avatar avatar-sm rounded-circle me-2">
                                                            <div>
                                                                <div class="fw-semibold">{{ $report->reporter->name }}</div>
                                                                <small class="text-muted">{{ $report->reporter->email }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-warning">{{ $report->reason_label }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ $report->status === 'pending' ? 'warning' : ($report->status === 'resolved' ? 'success' : 'secondary') }}">
                                                            {{ $report->status_label }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $report->created_at->diffForHumans() }}</td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.reports.show', $report) }}" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </a>

                                                            @if($report->reportable && $report->status === 'pending')
                                                                <!-- أزرار الحذف السريع -->
                                                                @if($report->reportable_type === 'App\Models\Ad')
                                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                                            onclick="quickDeleteContent('{{ $report->id }}', 'delete_ad')"
                                                                            title="حذف الإعلان">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                @elseif($report->reportable_type === 'App\Models\Comment')
                                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                                            onclick="quickDeleteContent('{{ $report->id }}', 'delete_comment')"
                                                                            title="حذف التعليق">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                @elseif(in_array($report->reportable_type, ['App\Models\ProductReview', 'App\Models\UserRating']))
                                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                                            onclick="quickDeleteContent('{{ $report->id }}', 'delete_review')"
                                                                            title="حذف التقييم">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                @elseif($report->reportable_type === 'App\Models\ChMessage')
                                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                                            onclick="quickDeleteContent('{{ $report->id }}', 'delete_message')"
                                                                            title="حذف الرسالة">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                @endif
                                                            @endif

                                                            @if($report->status === 'pending')
                                                                <form method="POST" action="{{ route('admin.reports.update-status', $report) }}" class="d-inline">
                                                                    @csrf
                                                                    @method('PATCH')
                                                                    <input type="hidden" name="status" value="resolved">
                                                                    <button type="submit" class="btn btn-sm btn-outline-success" title="تم الحل">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                </form>
                                                                <form method="POST" action="{{ route('admin.reports.update-status', $report) }}" class="d-inline">
                                                                    @csrf
                                                                    @method('PATCH')
                                                                    <input type="hidden" name="status" value="dismissed">
                                                                    <button type="submit" class="btn btn-sm btn-outline-secondary" title="تجاهل البلاغ">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </form>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                                    <h5>No Reports Found</h5>
                                    <p class="text-muted">There are no reports to display at the moment.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function quickDeleteContent(reportId, action) {
    // تأكيد الحذف مع رسالة واضحة
    const confirmMessage = getDeleteConfirmMessage(action);
    if (!confirm(confirmMessage)) {
        return;
    }

    // إظهار حالة التحميل
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    fetch('{{ route("admin.warnings.delete-content") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            report_id: reportId,
            action: action
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // إعادة تحميل الصفحة بعد 2 ثانية
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('danger', data.message || 'حدث خطأ أثناء حذف المحتوى');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        // إعادة تعيين حالة الزر
        button.disabled = false;
        button.innerHTML = originalHTML;
    });
}

function getDeleteConfirmMessage(action) {
    switch(action) {
        case 'delete_ad':
            return 'هل أنت متأكد من حذف هذا الإعلان؟ هذا الإجراء لا يمكن التراجع عنه.';
        case 'delete_comment':
            return 'هل أنت متأكد من حذف هذا التعليق؟ هذا الإجراء لا يمكن التراجع عنه.';
        case 'delete_review':
            return 'هل أنت متأكد من حذف هذا التقييم؟ هذا الإجراء لا يمكن التراجع عنه.';
        case 'delete_message':
            return 'هل أنت متأكد من حذف هذه الرسالة؟ هذا الإجراء لا يمكن التراجع عنه.';
        default:
            return 'هل أنت متأكد من حذف هذا المحتوى؟ هذا الإجراء لا يمكن التراجع عنه.';
    }
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.main-container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);

        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
@endpush
