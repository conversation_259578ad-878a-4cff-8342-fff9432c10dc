/* Related Ads Section - Enhanced Styles */
.related-ads-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 4rem 0;
    margin-top: 3rem;
    position: relative;
    overflow: hidden;
}

.related-ads-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #007bff, transparent);
}

.related-ads-title {
    text-align: center;
    margin-bottom: 3rem;
    color: #2c3e50;
    font-weight: 800;
    font-size: 2.5rem;
    position: relative;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.related-ads-title i {
    color: #007bff;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.related-ads-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #007bff, #28a745, #ffc107);
    border-radius: 2px;
    animation: shimmer 3s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

.related-ad-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    margin-bottom: 2rem;
    position: relative;
    border: 1px solid rgba(0,0,0,0.05);
}

.related-ad-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-ad-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.related-ad-card:hover::before {
    opacity: 1;
}

.sponsored-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    z-index: 3;
    box-shadow: 0 4px 12px rgba(255,107,107,0.4);
    animation: glow 2s infinite alternate;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes glow {
    from { box-shadow: 0 4px 12px rgba(255,107,107,0.4); }
    to { box-shadow: 0 6px 20px rgba(255,107,107,0.6); }
}

.sponsored-badge i {
    margin-right: 4px;
    animation: sparkle 1.5s infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
}

.related-ad-image {
    position: relative;
    height: 220px;
    overflow: hidden;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

.related-ad-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.1) 100%);
    pointer-events: none;
}

.related-ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: brightness(1.05) contrast(1.1);
}

.related-ad-card:hover .related-ad-image img {
    transform: scale(1.08) rotate(1deg);
}

.related-ad-content {
    padding: 1.5rem;
    position: relative;
}

.related-ad-title {
    font-size: 1.15rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: color 0.3s ease;
}

.related-ad-card:hover .related-ad-title {
    color: #007bff;
}

.related-ad-price {
    font-size: 1.4rem;
    font-weight: 800;
    background: linear-gradient(45deg, #28a745, #20c997);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(40,167,69,0.2);
}

.related-ad-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 1.25rem;
    padding: 0.5rem 0;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.related-ad-location,
.related-ad-date {
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.related-ad-location:hover,
.related-ad-date:hover {
    color: #007bff;
}

.related-ad-location i {
    margin-right: 0.4rem;
    color: #007bff;
    font-size: 0.9rem;
}

.related-ad-date i {
    margin-right: 0.4rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.view-ad-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 0.875rem 1.75rem;
    border-radius: 12px;
    font-weight: 700;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.view-ad-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.view-ad-btn:hover::before {
    left: 100%;
}

.view-ad-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,123,255,0.4);
}

.view-ad-btn i {
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.view-ad-btn:hover i {
    transform: scale(1.2);
}

/* Loading Animation */
.related-ad-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.related-ad-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .related-ads-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .related-ads-section {
        padding: 2.5rem 0;
    }
    
    .related-ads-title {
        font-size: 1.8rem;
        margin-bottom: 2rem;
    }
    
    .related-ad-image {
        height: 180px;
    }
    
    .related-ad-content {
        padding: 1.25rem;
    }
    
    .related-ad-title {
        font-size: 1rem;
    }
    
    .related-ad-price {
        font-size: 1.2rem;
    }
    
    .view-ad-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .related-ad-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .sponsored-badge {
        top: 8px;
        right: 8px;
        padding: 4px 8px;
        font-size: 0.7rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .related-ads-section {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }
    
    .related-ads-title {
        color: #f8f9fa;
    }
    
    .related-ad-card {
        background: #2d2d2d;
        border-color: rgba(255,255,255,0.1);
    }
    
    .related-ad-title {
        color: #f8f9fa;
    }
    
    .related-ad-card:hover .related-ad-title {
        color: #66b3ff;
    }
    
    .related-ad-meta {
        border-color: rgba(255,255,255,0.1);
    }
}
