<?php

namespace App\Jobs;

use App\Models\Ad;
use App\Models\BrokerInterest;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class RefundBrokerInterestsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $adId;
    protected $adTitle;

    /**
     * Create a new job instance.
     */
    public function __construct($adId, $adTitle = null)
    {
        $this->adId = $adId;
        $this->adTitle = $adTitle;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("بدء معالجة استرداد النقاط للإعلان: {$this->adTitle} (ID: {$this->adId})");

        // جلب جميع الاهتمامات النشطة للإعلان المحذوف
        $activeInterests = BrokerInterest::where('ad_id', $this->adId)
                                        ->where('status', 'active')
                                        ->where('code_entered', false)
                                        ->get();

        $totalRefunded = 0;
        $processedCount = 0;

        foreach ($activeInterests as $interest) {
            try {
                DB::transaction(function () use ($interest, &$totalRefunded, &$processedCount) {
                    $daysSinceInterest = $interest->created_at->diffInDays(now());

                    // حساب النقاط المسترجعة حسب التوقيت
                    if ($daysSinceInterest < 5) {
                        // أقل من 5 أيام: خصم 10%
                        $refundPoints = (int) floor($interest->points_paid * 0.9);
                        $deductionPercent = 10;
                    } else {
                        // 5 أيام أو أكثر: خصم 20%
                        $refundPoints = (int) floor($interest->points_paid * 0.8);
                        $deductionPercent = 20;
                    }

                    // إضافة النقاط للمندوب
                    $interest->broker->increment('points', $refundPoints);

                    // تحديث حالة الاهتمام
                    $interest->update([
                        'status' => 'ad_deleted',
                        'points_earned' => $refundPoints,
                    ]);

                    $totalRefunded += $refundPoints;
                    $processedCount++;

                    Log::info("تم استرداد {$refundPoints} نقطة للمندوب {$interest->broker->name} (خصم {$deductionPercent}%)");
                });
            } catch (\Exception $e) {
                Log::error("خطأ في استرداد النقاط للاهتمام {$interest->id}: " . $e->getMessage());
            }
        }

        Log::info("تم الانتهاء من معالجة استرداد النقاط. تم معالجة {$processedCount} اهتمام واسترداد {$totalRefunded} نقطة إجمالية");
    }
}
