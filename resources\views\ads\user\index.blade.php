@extends('partials.app')
@section('title', 'Ads Listing')
@section('content')

@include('layouts.breadcrumb', ['admin' => false, 'pageTitle' => 'Ads Listing'])

<div class="dashboard-section pt-120 pb-120">
    <div class="container">
        <div class="row g-4">
            @include('layouts.sidebar', ['active' => 'ads', 'admin' => false])
            <div class="col-lg-9">
                <div class="tab-pane">
                    <div class="table-title-area">
                       <h3>Ads Listing</h3>
                       <form class="d-flex align-items-center">
                       <select name="status">
                        <option value=""> Show: All Listing (Filter)</option>
                        <option value="pending" @selected(request()->status == 'pending')>Show: Pending Listing</option>
                        <option value="active" @selected(request()->status == 'active')>Show: Active Listing</option>
                        <option value="upcoming" @selected(request()->status == 'upcoming')>Show: Upcoming Listing</option>
                        <option value="expired" @selected(request()->status == 'expired')>Show: Expired Listing</option>
                        <option value="rejected" @selected(request()->status == 'rejected')>Show: Rejected Listing</option>
                     </select>
                     <button type="submit" class="filter-btn bg-dark text-white ml-2">Filter</button>
                    </form>
                    </div>
                    @if($ads->count() > 0)
                    <div class="table-wrapper">
                       <table class="eg-table order-table table mb-0">
                          <thead>
                             <tr>
                                <th>Image</th>
                                <th>Ads Title</th>
                                <th>Starting Price</th>
                                <th>Status</th>
                                <th>Admin Notes</th>
                                <th>Action</th>
                             </tr>
                          </thead>
                          <tbody>
                            @foreach($ads as $ad)
                            <tr>
                                <td data-label="Image">
                                    @if ($ad->media->isNotEmpty())
                                        <img alt="image" src="{{ $ad->media->first()->url }}" class="img-fluid">
                                    @else
                                        <img alt="No image" src="{{ asset('path-to-default-image.jpg') }}" class="img-fluid">
                                    @endif
                                </td>
                                <td data-label="Ads Title">{{ shorten_chars($ad->title, 20) }}</td>
                                <td data-label="Starting Price">{{ money($ad->price) }}</td>
                                <td data-label="Status" class="text-{{ $ad->status->color() }}">{{ $ad->status->label() }}</td>
                                <td data-label="Admin Notes">
                                    @if($ad->latestAdminNote)
                                        <div class="admin-note-mini">
                                            <span class="note-type-badge-mini {{ $ad->latestAdminNote->type_color }}">
                                                {{ $ad->latestAdminNote->type_name }}
                                            </span>
                                            <p class="note-preview">{{ \Illuminate\Support\Str::limit($ad->latestAdminNote->note, 50) }}</p>
                                            <small class="text-muted">{{ $ad->latestAdminNote->created_at->diffForHumans() }}</small>
                                        </div>
                                    @else
                                        <span class="text-muted">No notes</span>
                                    @endif
                                </td>
                                <td data-label="Action">
                                    <div class="action-buttons">
                                        <a href="{{ route('user.ads.show', $ad->slug) }}" class="eg-btn action-btn green text-white">
                                            <i class="bi bi-eye-fill"></i> View
                                        </a>

                                        @if($ad->status && $ad->status->value === 'published')
                                            @if($ad->canBeBosted())
                                                <form action="{{ route('user.ads.boost', $ad->slug) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="eg-btn action-btn blue text-white boost-btn-table">
                                                        <i class="fas fa-rocket"></i> Boost
                                                    </button>
                                                </form>
                                            @else
                                                <button class="eg-btn action-btn secondary text-white" disabled
                                                        title="Next boost available in {{ $ad->boost_available_in }}">
                                                    <i class="fas fa-clock"></i> {{ $ad->boost_available_in }}
                                                </button>
                                            @endif
                                        @endif

                                        <form action="{{ route('user.ads.delete', $ad->slug) }}" method="POST" class="d-inline delete-form-table">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="eg-btn action-btn red text-white">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                </td>
                             </tr>
                            @endforeach
                          </tbody>
                       </table>
                    </div>
                    {{ $ads->links('pagination.simple') }}
                    @else
                    <div class="d-flex flex-column align-items-center justify-content-center">
                        <div class="text-center mb-4">
                            <img src="{{ asset('assets/images/icons/man.svg') }}" alt="empty" class="w-25">
                        </div>
                        <x-alert type="dark">
                            <p class="text-center mb-0"><strong>Sorry!</strong> You have no ads listing currently. To add a listing, click <a href="{{ route('add-listing') }}" class="fw-bold">here</a>.</p>
                        </x-alert>
                    </div>
                    @endif
                 </div>
            </div>
        </div>
    </div>
</div>

<x-metric-card />

@endsection

@push('styles')
<style>
.admin-note-mini {
    max-width: 200px;
}

.note-type-badge-mini {
    font-size: 0.6rem;
    font-weight: 600;
    padding: 0.1rem 0.3rem;
    border-radius: 8px;
    text-transform: uppercase;
    display: inline-block;
    margin-bottom: 0.25rem;
}

.note-type-badge-mini.info {
    background: #d1ecf1;
    color: #0c5460;
}

.note-type-badge-mini.danger {
    background: #f8d7da;
    color: #721c24;
}

.note-type-badge-mini.secondary {
    background: #e2e3e5;
    color: #383d41;
}

.note-preview {
    margin: 0;
    font-size: 0.8rem;
    color: #495057;
    line-height: 1.3;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn.blue {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.action-btn.red {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.action-btn.secondary {
    background: #6c757d;
    cursor: not-allowed;
}

.action-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation for table
    const deleteFormsTable = document.querySelectorAll('.delete-form-table');
    deleteFormsTable.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this ad? This action cannot be undone.')) {
                this.submit();
            }
        });
    });

    // Boost button click effect for table
    const boostBtnsTable = document.querySelectorAll('.boost-btn-table');
    boostBtnsTable.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (confirm('Boost this ad? It will appear at the top of listings and you can boost again in 48 hours.')) {
                // Add loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Boosting...';
                this.disabled = true;
            } else {
                e.preventDefault();
            }
        });
    });
});
</script>
@endpush