<?php

namespace App\Observers;

use App\Models\Post;
use Illuminate\Support\Facades\Log;

class PostObserver
{
    /**
     * Handle the Post "created" event.
     */
    public function created(Post $post): void
    {
        Log::info("تم إنشاء مقال جديد: {$post->title} (ID: {$post->id})");
    }

    /**
     * Handle the Post "updated" event.
     */
    public function updated(Post $post): void
    {
        // تسجيل تغيير حالة النشر
        if ($post->isDirty('is_published')) {
            $status = $post->is_published ? 'منشور' : 'غير منشور';
            Log::info("تم تغيير حالة المقال (ID: {$post->id}) إلى: {$status}");
        }
    }

    /**
     * Handle the Post "deleted" event.
     */
    public function deleted(Post $post): void
    {
        try {
            // حذف جميع الصور المرتبطة بالمقال
            $post->media()->each(function ($media) {
                try {
                    // حذف الملف من التخزين
                    if (file_exists(storage_path('app/' . $media->path))) {
                        unlink(storage_path('app/' . $media->path));
                    }
                    
                    // حذف سجل الميديا من قاعدة البيانات
                    $media->delete();
                    
                    Log::info("تم حذف صورة المقال: {$media->path}");
                } catch (\Exception $e) {
                    Log::error("خطأ في حذف صورة المقال {$media->path}: " . $e->getMessage());
                }
            });

            Log::info("تم حذف المقال وجميع صوره: {$post->title} (ID: {$post->id})");

        } catch (\Exception $e) {
            Log::error("خطأ في حذف صور المقال (ID: {$post->id}): " . $e->getMessage());
        }
    }

    /**
     * Handle the Post "restored" event.
     */
    public function restored(Post $post): void
    {
        //
    }

    /**
     * Handle the Post "force deleted" event.
     */
    public function forceDeleted(Post $post): void
    {
        // نفس منطق الحذف العادي
        $this->deleted($post);
    }
}
