<?php

namespace App\Http\Controllers;

use App\Models\Report;
use App\Models\Ad;
use App\Models\Comment;
use App\Models\User;
use App\Models\ProductReview;
use App\Models\UserRating;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ReportController extends Controller
{
    /**
     * Store a new report.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'reportable_type' => 'required|string',
            'reportable_id' => 'required|string',
            'reason' => 'required|string|in:' . implode(',', array_keys(Report::REASONS)),
            'description' => 'nullable|string|max:1000'
        ]);

        try {
            // Check if user is authenticated
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            // Check if user already reported this item
            $existingReport = Report::where([
                'reporter_id' => Auth::id(),
                'reportable_type' => $request->reportable_type,
                'reportable_id' => $request->reportable_id
            ])->first();

            if ($existingReport) {
                return response()->json([
                    'success' => false,
                    'message' => 'لقد قمت بالإبلاغ عن هذا العنصر من قبل'
                ]);
            }

            // Validate that the reportable item exists
            $reportableClass = $request->reportable_type;
            if (!class_exists($reportableClass)) {
                return response()->json([
                    'success' => false,
                    'message' => 'نوع العنصر غير صحيح'
                ], 400);
            }

            $reportableItem = $reportableClass::find($request->reportable_id);
            if (!$reportableItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'العنصر المراد الإبلاغ عنه غير موجود'
                ], 404);
            }

            // Collect metadata based on reportable type
            $metadata = $this->collectMetadata($request->reportable_type, $reportableItem);

            // Create the report
            $report = Report::create([
                'reporter_id' => Auth::id(),
                'reportable_type' => $request->reportable_type,
                'reportable_id' => $request->reportable_id,
                'reason' => $request->reason,
                'description' => $request->description,
                'metadata' => $metadata
            ]);

            Log::info('Report created', [
                'report_id' => $report->id,
                'reporter_id' => Auth::id(),
                'reportable_type' => $request->reportable_type,
                'reportable_id' => $request->reportable_id,
                'reason' => $request->reason
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإبلاغ بنجاح. سيتم مراجعته من قبل فريق الإدارة.'
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating report', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإبلاغ. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * Collect metadata for different reportable types.
     */
    private function collectMetadata(string $type, $item): array
    {
        $metadata = [];

        switch ($type) {
            case Report::TYPE_AD:
                $metadata = [
                    'ad_title' => $item->title,
                    'ad_slug' => $item->slug,
                    'seller_id' => $item->user_id,
                    'seller_name' => $item->user->name ?? 'Unknown',
                    'category' => $item->category->name ?? 'Unknown',
                    'price' => $item->price,
                    'status' => $item->status->value ?? 'Unknown'
                ];
                break;

            case Report::TYPE_COMMENT:
                $metadata = [
                    'comment_content' => substr($item->content, 0, 200),
                    'commenter_id' => $item->user_id,
                    'commenter_name' => $item->user->name ?? 'Unknown',
                    'ad_id' => $item->ad_id ?? null,
                    'ad_title' => $item->ad->title ?? 'Unknown',
                    'ad_slug' => $item->ad->slug ?? null
                ];
                break;

            case Report::TYPE_USER:
                $metadata = [
                    'user_name' => $item->name,
                    'user_email' => $item->email,
                    'user_phone' => $item->phone ?? null,
                    'is_verified' => $item->email_verified_at ? true : false,
                    'is_trusted' => $item->is_trusted ?? false,
                    'registration_date' => $item->created_at->format('Y-m-d')
                ];
                break;

            case Report::TYPE_REVIEW:
                $metadata = [
                    'review_rating' => $item->rating,
                    'review_comment' => substr($item->comment ?? '', 0, 200),
                    'reviewer_id' => $item->reviewer_id,
                    'reviewer_name' => $item->reviewer->name ?? 'Unknown',
                    'reviewed_user_id' => $item->user_id,
                    'reviewed_user_name' => $item->user->name ?? 'Unknown'
                ];
                break;

            case Report::TYPE_USER_RATING:
                $metadata = [
                    'rating_value' => $item->rating,
                    'rating_comment' => substr($item->comment ?? '', 0, 200),
                    'rater_id' => $item->rater_id,
                    'rater_name' => $item->rater->name ?? 'Unknown',
                    'rated_user_id' => $item->rated_id,
                    'rated_user_name' => $item->rated->name ?? 'Unknown'
                ];
                break;

            case Report::TYPE_MESSAGE:
                $metadata = [
                    'message_content' => substr($item->body ?? '', 0, 200),
                    'sender_id' => $item->from_id,
                    'sender_name' => $item->fromUser->name ?? 'Unknown',
                    'receiver_id' => $item->to_id,
                    'receiver_name' => $item->toUser->name ?? 'Unknown',
                    'conversation_id' => $item->conversation_id ?? null
                ];
                break;
        }

        return $metadata;
    }

    /**
     * Get report modal content.
     */
    public function getModal(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string',
            'id' => 'required|string'
        ]);

        $reasons = Report::REASONS;

        return response()->json([
            'success' => true,
            'reasons' => $reasons,
            'type' => $request->type,
            'id' => $request->id
        ]);
    }
}
