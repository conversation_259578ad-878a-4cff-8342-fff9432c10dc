@extends('partials.admin')
@section('title', 'Identity Verification Details')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'identity-verifications'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Identity Verification Details', 'hasBack' => true, 'backTitle' => 'All Verifications', 'backUrl' => route('admin.identity-verifications.index')])

            <!-- Alert Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {!! session('error') !!}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- User Information -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">User Information</h4>
                        </div>
                        <div class="card-body text-center">
                            <img src="{{ $identityVerification->user->avatar ? \Illuminate\Support\Facades\Storage::url($identityVerification->user->avatar) : get_random_avatar() }}"
                                 alt="{{ $identityVerification->user->name }}" class="avatar avatar-xl rounded-circle mb-3">

                            <h5>{{ $identityVerification->user->name }}</h5>
                            <p class="text-muted">{{ $identityVerification->user->email }}</p>
                            
                            <div class="row text-center mt-4">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h6 class="mb-1">{{ $identityVerification->user->ads()->count() }}</h6>
                                        <small class="text-muted">Total Ads</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h6 class="mb-1">{{ number_format($identityVerification->user->average_rating ?? 0, 1) }}</h6>
                                    <small class="text-muted">Rating</small>
                                </div>
                            </div>

                            <div class="mt-3">
                                <a href="{{ route('admin.users.edit', $identityVerification->user->id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-edit me-1"></i>
                                    View User Profile
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Status Card -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Verification Status</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Current Status</label>
                                <div>
                                    @if($identityVerification->status === 'pending')
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Pending Review
                                        </span>
                                    @elseif($identityVerification->status === 'approved')
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Approved
                                        </span>
                                    @else
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Rejected
                                        </span>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Submitted At</label>
                                <p class="text-muted">{{ $identityVerification->created_at->format('Y-m-d H:i:s') }}</p>
                            </div>

                            @if($identityVerification->approved_at)
                                <div class="mb-3">
                                    <label class="form-label">Approved At</label>
                                    <p class="text-muted">{{ $identityVerification->approved_at->format('Y-m-d H:i:s') }}</p>
                                </div>
                            @endif

                            @if($identityVerification->rejected_at)
                                <div class="mb-3">
                                    <label class="form-label">Rejected At</label>
                                    <p class="text-muted">{{ $identityVerification->rejected_at->format('Y-m-d H:i:s') }}</p>
                                </div>
                            @endif

                            @if($identityVerification->rejection_reason)
                                <div class="mb-3">
                                    <label class="form-label">Rejection Reason</label>
                                    <p class="text-muted">{{ $identityVerification->rejection_reason }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Verification Details -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Identity Documents</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label class="form-label">National ID Number</label>
                                @if($identityVerification->status === 'pending')
                                    <!-- نموذج تعديل الرقم القومي للأدمن -->
                                    <form action="{{ route('admin.identity-verifications.update-national-id', $identityVerification) }}" method="POST">
                                        @csrf
                                        <div class="input-group">
                                            <input type="text"
                                                   class="form-control"
                                                   name="national_id"
                                                   value="{{ $identityVerification->national_id }}"
                                                   maxlength="20"
                                                   required>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-1"></i>
                                                تحديث
                                            </button>
                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $identityVerification->national_id }}')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </form>
                                @else
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $identityVerification->national_id }}" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $identityVerification->national_id }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                @endif

                                <!-- التحقق من تكرار الرقم القومي -->
                                @php
                                    $existingUser = \App\Models\User::where('national_id', $identityVerification->national_id)->first();
                                @endphp
                                @if($existingUser)
                                    <div class="mt-2">
                                        <span class="badge bg-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            رقم مكرر - مستخدم: {{ $existingUser->name }} (ID: {{ $existingUser->id }})
                                        </span>
                                    </div>
                                @else
                                    <div class="mt-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>
                                            رقم غير مكرر
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Front Image (with Selfie)</label>
                                        <div class="text-center">
                                            <img src="{{ \Illuminate\Support\Facades\Storage::url($identityVerification->front_image) }}"
                                                 alt="Front ID Image"
                                                 class="img-fluid border rounded"
                                                 style="max-height: 300px; cursor: pointer;"
                                                 onclick="showImageModal('{{ \Illuminate\Support\Facades\Storage::url($identityVerification->front_image) }}', 'Front ID with Selfie')">
                                        </div>
                                        <small class="text-muted">Click to view full size</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Back Image</label>
                                        <div class="text-center">
                                            <img src="{{ \Illuminate\Support\Facades\Storage::url($identityVerification->back_image) }}"
                                                 alt="Back ID Image"
                                                 class="img-fluid border rounded"
                                                 style="max-height: 300px; cursor: pointer;"
                                                 onclick="showImageModal('{{ \Illuminate\Support\Facades\Storage::url($identityVerification->back_image) }}', 'Back ID Image')"
                                        </div>
                                        <small class="text-muted">Click to view full size</small>
                                    </div>
                                </div>
                            </div>

                            @if($identityVerification->status === 'pending')
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <form method="POST" action="{{ route('admin.identity-verifications.approve', $identityVerification) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-success w-100" onclick="return confirm('Are you sure you want to approve this identity verification?')">
                                                <i class="fas fa-check me-2"></i>
                                                Approve Verification
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-danger w-100" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                            <i class="fas fa-times me-2"></i>
                                            Reject Verification
                                        </button>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- CONTAINER END -->
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.identity-verifications.reject', $identityVerification) }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Reject Identity Verification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" name="rejection_reason" rows="4" required 
                                  placeholder="Please provide a clear reason for rejection..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        Reject Verification
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    National ID copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        setTimeout(() => toast.remove(), 3000);
    });
}

function showImageModal(imageSrc, title) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModalTitle').textContent = title;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}
</script>
@endpush
