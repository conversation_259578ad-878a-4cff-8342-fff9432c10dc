<?php

namespace App\Http\Controllers\User\Ad;

use App\Contracts\Repositories\AdRepositoryInterface;
use App\Contracts\Repositories\AuthenticateRepositoryInterface;
use App\Contracts\Repositories\CategoryRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\Ad\CreateAdRequest;
use App\Http\Requests\Ad\FilterAdRequest;
use App\Http\Requests\Ad\FilterUserAdsRequest;
use App\Http\Requests\Ad\ReportAdRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;
use App\Contracts\Repositories\CountryRepositoryInterface;

use App\Repositories\AdRepository;
use App\Models\Ad;
use App\Models\AdBrokerRequest;
use App\Models\Deals;
use App\Models\Category;
use App\Models\CategoryAttribute;
use App\Services\RelatedAdsService;

class AdController extends Controller
{
    /**
     * Instantiate new controller instance
     */
    public function __construct(
        protected AdRepositoryInterface $adRepository,
        protected CountryRepositoryInterface $countryRepository,
        protected AuthenticateRepositoryInterface $authRepository,
        protected CategoryRepositoryInterface $categoryRepository,
        protected RelatedAdsService $relatedAdsService
    ) {}
    
    /**
     * Index page for listing ads.
     * 
     * @param \App\Http\Requests\Ad\FilterAdsRequest $query
     * @return \Illuminate\View\View
     */
    public function index(FilterAdRequest $query ): View
    {
        return view('pages.live-auction.index', [
            'countries' => $this->countryRepository->all([ 'id', 'name', 'iso2', 'emoji']),
            'ads' => $this->adRepository->getLatestAdsWithAttributes(12, 'active', $query->validated()),
            'categories' => $this->categoryRepository->getPrimaryCategories(),
            'priceRanges' => \App\Enums\PriceRange::cases(),
        ]);
    }



    
    /**
     * Show ad details.
     * 
     * @param string $ad
     * @return \Illuminate\View\View
     */
public function show(string $slug): View
{
    $ads_broker_requests_count = 0;
    $deal_exists = false;
    $user_is_broker = false;
    $totalAds = 0;

    // التحقق من وجود المستخدم أولاً
    $user = auth()->user();
    $isActiveBroker = false;
    if ($user) {
        $user_is_broker = $user->is_broker ?? false;

        if ($user_is_broker) {
            $ad_obj = Ad::where('slug', $slug)->firstOrFail(); // الإعلان الأساسي

            // التحقق من نشاط المندوب
            if ($user->last_successful_code_at) {
                // لديه أكواد ناجحة - نحسب من آخر كود ناجح في جدول المستخدمين
                $daysSinceLastCode = $user->last_successful_code_at->diffInDays(now());
                $isActiveBroker = $daysSinceLastCode < 30;
            } else {
                // لا يوجد أكواد ناجحة - نتحقق من تاريخ تفعيل المندوب
                // المندوب الجديد له فترة سماح 30 يوم من تاريخ التفعيل
                // هذه الفترة لا تتجدد إلا بكود ناجح (ليس بالاهتمام)
                $brokerActivationDate = $user->broker_activated_at ?? $user->updated_at;
                $daysSinceBecameBroker = $brokerActivationDate->diffInDays(now());
                $isActiveBroker = $daysSinceBecameBroker <= 30;
            }

            $ads_broker_requests_count = \App\Models\BrokerInterest::where('ad_id', $ad_obj->id)
                                                                      ->where('status', 'active')
                                                                      ->count();
            $deal_exists = Deals::where('ad_id', $ad_obj->id)->exists();
        }

        // حساب عدد الإعلانات فقط للمستخدمين المسجلين
        $totalAds = Ad::where('user_id', $user->id)
            ->where('seller_email', $user->email) // تأكيد إضافي
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->count();
    }

        
    $ad = $this->adRepository->getAd($slug);

    // الحصول على الإعلانات ذات الصلة مع الإعلانات الممولة (زيادة العدد)
    $relatedAdsWithSponsored = $this->relatedAdsService->getRelatedAdsWithSponsored($ad, 24);

    // الحصول على الإعلانات الممولة فقط للشريط الجانبي
    $relatedSponsoredAds = \App\Models\Ad::query()
        ->whereHas('sponsoredAd', function ($query) {
            $query->where('is_active', true)
                  ->where('status', 'active')
                  ->where('expires_at', '>', now());
        })
        ->where('category_id', $ad->category_id)
        ->where('id', '!=', $ad->id)
        ->with(['category', 'user', 'media', 'sponsoredAd'])
        ->latest()
        ->take(6)
        ->get();

    return view('pages.live-auction.show', [
        'totalAds' => $totalAds,
        'user_is_broker' => $user_is_broker,
        'isActiveBroker' => $isActiveBroker,
        'countries' => $this->countryRepository->all(['id', 'name', 'iso2', 'emoji']),
        'ad' => $ad,
        'relatedAdsWithSponsored' => $relatedAdsWithSponsored,
        'relatedSponsoredAds' => $relatedSponsoredAds,
        'ads_broker_requests_count' => $ads_broker_requests_count,
        'deal_exists' => $deal_exists,
    ]);
}






    public function affiliate(): View
    {
        return view('pages.affiliate.index');
    }



    public function create(): View
{
    return view('pages.live-auction.create', [
        'countries' => $this->countryRepository->all(['id', 'name', 'iso2', 'emoji']),
    ]);
}




    /**
     * Create an ad listing.
     * 
     * @param \App\Http\Requests\Ad\CreateAdRequest $request
     * @return RedirectResponse;
     */
    public function store(CreateAdRequest $request): RedirectResponse
    {
        $user = auth()->user();
        if (!$user) {
            return redirect()->back()->with('error', 'You must be logged in to create an ad.');
        }

        $data = $request->validated();

        // التحقق من الـ rank و is_trusted قبل إنشاء الإعلان
        // تعيين القيم الافتراضية إذا كانت null
        $userIsTrusted = $user->is_trusted ?? 0;
        $userRank = $user->rank ?? 0;

        // السماح للمستخدمين الموثقين بالوصول للميزات المتقدمة
        $allowPremiumFeatures = $userIsTrusted > 0;

        if ($allowPremiumFeatures) {
            // المستخدم مؤهل لاستخدام الميزات المتقدمة
            $data['needs_brokering'] = $data['needs_brokering'] ?? false;
            $data['broker_commission'] = $data['broker_commission'] ?? 0;
            $data['is_sponsored'] = $data['is_sponsored'] ?? false;
            $data['sponsorship_cost'] = $data['sponsorship_cost'] ?? 0;

            // التحقق من صحة تكلفة الرعاية
            if (isset($data['sponsorship_cost']) && $data['sponsorship_cost'] < 10) {
                $data['is_sponsored'] = false; // لا يمكن الرعاية بأقل من 10 جنيه
                $data['sponsorship_cost'] = 0;
            }
        } else {
            // المستخدم غير مؤهل، إرجاع خطأ إذا حاول استخدام الميزات المتقدمة
            if (isset($data['needs_brokering']) && $data['needs_brokering']) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'يجب أن تكون مستخدماً موثقاً لاستخدام ميزة الدعم التسويقي.');
            }

            if (isset($data['is_sponsored']) && $data['is_sponsored']) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'يجب أن تكون مستخدماً موثقاً لاستخدام ميزة الإعلانات الممولة.');
            }

            // التحقق من الحد الأدنى للرعاية
            if (isset($data['sponsorship_cost']) && $data['sponsorship_cost'] > 0 && $data['sponsorship_cost'] < 10) {
                return redirect()->back()
                    ->withInput()
                    ->with('error', 'الحد الأدنى لميزانية الرعاية هو 10 جنيه.');
            }

            // تعيين القيم الافتراضية
            $data['needs_brokering'] = false;
            $data['broker_commission'] = 0;
            $data['is_sponsored'] = false;
            $data['sponsorship_cost'] = 0;
        }

        // Check if we're editing an existing ad
        if (isset($data['editing_ad_id']) && $data['editing_ad_id']) {
            // We're editing an existing ad
            Log::info('Editing existing ad', ['ad_id' => $data['editing_ad_id'], 'user_id' => $user->id]);

            $existingAd = Ad::find($data['editing_ad_id']);

            // Check if ad exists and user owns it
            if (!$existingAd || $existingAd->user_id !== $user->id) {
                Log::error('User trying to edit ad they do not own', ['ad_id' => $data['editing_ad_id'], 'user_id' => $user->id, 'ad_owner' => $existingAd?->user_id]);
                abort(403, 'You can only edit your own ads.');
            }

            Log::info('Updating ad', ['ad_id' => $existingAd->id, 'old_title' => $existingAd->title, 'new_title' => $data['title']]);

            // Update the ad with new data
            $ad = $this->adRepository->updateAd($existingAd, $data);

            Log::info('Ad updated successfully', ['ad_id' => $ad->id, 'new_status' => $ad->status->value]);

            return redirect()->route('user.dashboard')->with('success', 'Your ad has been updated successfully! It will be reviewed by our team before being published again.');
        } else {
            // استدعاء الـ Repository لإنشاء الإعلان مع رفع الصور
            $ad = $this->adRepository->create($user, $data);

            return redirect()->route('add-listing')->with('success', 'Your ad has been created successfully, it will be reviewed by our team before it is published.');
        }
    }
    
    

    /**
     * Get user ads.
     * 
     * @return \Illuminate\View\View
     */
    public function userAds(FilterUserAdsRequest $query): View
    {
        $user = $this->authRepository->user();
        if (!$user) {
            abort(403, 'You must be logged in to access this page.');
        }

        return view('ads.user.index', [
            'countries' => $this->countryRepository->all([ 'id', 'name', 'iso2', 'emoji']),
            'ads' => $this->adRepository->getUserAds($user, 10, $query->validated()),
        ]);
    }






public function ad_code(FilterUserAdsRequest $query): View
{
    // التحقق من وجود المستخدم أولاً
    $user = auth()->user();
    if (!$user) {
        abort(403, 'You must be logged in to access this page.');
    }

    $userEmail = $user->email;
    // جلب إعلانات المستخدم (كل الحالات لأنه صاحب الإعلان)
    $ads = Ad::where('seller_email', $userEmail)->get();
    $ads_request = [];
    $deals_ads = [];

    if ($user->is_broker ?? false) {
        $ads_broker_requests = \App\Models\BrokerInterest::where('broker_id', $user->id)
                                                                ->where('status', 'active')
                                                                ->with('ad')
                                                                ->get();

        foreach ($ads_broker_requests as $ad_broker) {
            if ($ad_broker->ad) {
                $ads_request[] = $ad_broker->ad;
            }
        }

        $deals = Deals::where('email', $userEmail)->get();
        foreach ($deals as $deal) {
            $ad = Ad::find($deal->ad_id);
            if ($ad) {
                $deals_ads[] = $ad;
            }
        }
    }

    return view('ads.ad_code', [
        'ads' => $ads,
        'ads_request' => $ads_request ?? [],
        'deals_ads' => $deals_ads ?? [],
    ]);
}

/**
 * Show user profile page.
 *
 * @param string $username
 * @return \Illuminate\View\View
 */
public function userProfile(string $username): View
{
    // البحث عن المستخدم بالـ username
    $user = \App\Models\User::where('username', $username)->firstOrFail();

    // جلب إعلانات المستخدم في آخر 30 يوم (المقبولة فقط)
    // التأكد من أن الإعلانات تخص هذا المستخدم فقط
    $recentAds = Ad::where('user_id', $user->id)
        ->where('seller_email', $user->email) // تأكيد إضافي
        ->where('status', \App\Enums\AdStatus::PUBLISHED)
        ->where('created_at', '>=', now()->subDays(30))
        ->with(['media', 'category:id,name'])
        ->orderBy('created_at', 'desc')
        ->limit(6)
        ->get();

    // عدد إجمالي الإعلانات المقبولة
    $totalAds = Ad::where('user_id', $user->id)
        ->where('seller_email', $user->email) // تأكيد إضافي
        ->where('status', \App\Enums\AdStatus::PUBLISHED)
        ->count();

    // إذا كان مندوب، جلب الـ deals الخاصة به
    $deals = [];
    $dealsAds = collect(); // مجموعة فارغة
    if ($user->is_broker ?? false) {
        $deals = \App\Models\Deals::where('email', $user->email)
            ->with(['ad' => function($query) {
                $query->select('id', 'title', 'slug', 'price', 'created_at', 'user_id', 'seller_name')
                      ->with(['media', 'category:id,name', 'user:id,name,username,avatar']);
            }])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // جلب الإعلانات من الـ deals لعرضها منفصلة
        $dealsAds = $deals->map(function($deal) {
            return $deal->ad;
        })->filter(); // إزالة القيم الفارغة
    }

    // جلب آخر التقييمات للمستخدم
    $recentRatings = $user->receivedRatings()
        ->with(['rater:id,name,username,avatar'])
        ->orderBy('created_at', 'desc')
        ->limit(6)
        ->get();

    return view('pages.user-profile.index', [
        'user' => $user,
        'recentAds' => $recentAds,
        'totalAds' => $totalAds,
        'deals' => $deals,
        'dealsAds' => $dealsAds,
        'recentRatings' => $recentRatings,
    ]);
}




    
    /**
     * Show user ad details.
     * 
     * @param string $ad
     * @return \Illuminate\View\View
     */
    public function showUserAd(string $ad): View
    {
        $user = $this->authRepository->user();
        if (!$user) {
            abort(403, 'You must be logged in to access this page.');
        }

        return view('ads.user.show', [
            'countries' => $this->countryRepository->all([ 'id', 'name', 'iso2', 'emoji']),
            'ad' => $this->adRepository->getUserAd($user, $ad),
        ]);
    }

    /**
     * Edit user ad.
     * 
     * @param string $ad
     * @return \Illuminate\View\View
     */

    /**
     * Update user ad.
     * 
     * @param string $ad
     * @param \App\Http\Requests\Ad\UpdateAdRequest $request
     */

    /**
     * Report ad.
     * 
     * @param string $ad
     * @return \Illuminate\View\View
     */

    /**
     * Handle ad report.
     *
     * @param string $ad
     * @param \App\Http\Requests\Ad\ReportAdRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleReport(string $ad, ReportAdRequest $request): RedirectResponse
    {
        $this->adRepository->reportAd($ad, $request->validated());
        return redirect()->route('auction-details', $ad)->with('success', 'Your report has been submitted successfully.');
    }

    /**
     * Delete user ad.
     *
     * @param string $ad
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteUserAd(string $ad): RedirectResponse
    {
        $user = $this->authRepository->user();
        if (!$user) {
            abort(403, 'You must be logged in to delete ads.');
        }

        try {
            $adModel = $this->adRepository->getUserAd($user, $ad);

            // Check if user owns this ad
            if ($adModel->user_id !== $user->id) {
                abort(403, 'You can only delete your own ads.');
            }

            // Delete the ad
            $adModel->delete();

            return redirect()->route('user.dashboard')->with('success', 'Ad deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->route('user.dashboard')->with('error', 'Failed to delete ad. Please try again.');
        }
    }

    /**
     * Boost user ad.
     *
     * @param string $ad
     * @return \Illuminate\Http\RedirectResponse
     */
    public function boostUserAd(string $ad): RedirectResponse
    {
        $user = $this->authRepository->user();
        if (!$user) {
            abort(403, 'You must be logged in to boost ads.');
        }

        try {
            $adModel = $this->adRepository->getUserAd($user, $ad);

            // Check if user owns this ad
            if ($adModel->user_id !== $user->id) {
                abort(403, 'You can only boost your own ads.');
            }

            // Check if ad is published
            if ($adModel->status->value !== 1) { // 1 = published
                return redirect()->route('user.dashboard')->with('error', 'Only published ads can be boosted.');
            }

            // Check if ad can be boosted
            if (!$adModel->canBeBosted()) {
                $timeRemaining = $adModel->boost_available_in;
                return redirect()->route('user.dashboard')->with('error', "You can boost this ad again in {$timeRemaining}.");
            }

            // Boost the ad
            Log::info('Attempting to boost ad', ['ad_id' => $adModel->id, 'can_boost' => $adModel->canBeBosted()]);

            if ($adModel->boost()) {
                Log::info('Ad boosted successfully', ['ad_id' => $adModel->id]);
                return redirect()->route('user.dashboard')->with('success', 'Ad boosted successfully! It will now appear at the top.');
            } else {
                Log::error('Failed to boost ad', ['ad_id' => $adModel->id]);
                return redirect()->route('user.dashboard')->with('error', 'Failed to boost ad. Please try again.');
            }
        } catch (\Exception $e) {
            return redirect()->route('user.dashboard')->with('error', 'Failed to boost ad. Please try again.');
        }
    }

    /**
     * Get subcategories for a given category
     *
     * @param string $categorySlug
     * @return JsonResponse
     */
    public function getSubcategories(string $categorySlug): JsonResponse
    {
        try {
            $subcategories = $this->categoryRepository->getChildren($categorySlug);

            return response()->json([
                'success' => true,
                'subcategories' => $subcategories->map(function ($subcategory) {
                    return [
                        'id' => $subcategory->id,
                        'name' => $subcategory->name,
                        'slug' => $subcategory->slug,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found'
            ], 404);
        }
    }

    /**
     * Get attributes for a given category
     *
     * @param string $categorySlug
     * @return JsonResponse
     */
    public function getAttributes(string $categorySlug): JsonResponse
    {
        try {
            $category = $this->categoryRepository->findBySlug($categorySlug);
            $attributes = $category->attributes;

            return response()->json([
                'success' => true,
                'attributes' => $attributes->map(function ($attribute) {
                    return [
                        'id' => $attribute->id,
                        'name' => $attribute->attribute_name,
                        'label' => $attribute->attribute_label,
                        'type' => $attribute->attribute_type,
                        'options' => $attribute->attribute_options,
                        'is_required' => $attribute->is_required,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found'
            ], 404);
        }
    }

    /**
     * Edit listing - redirect to add-listing page with ad data for editing.
     *
     * @param string $ad
     * @return \Illuminate\View\View
     */
    public function editListing(string $ad): View
    {
        $user = $this->authRepository->user();
        $adModel = $this->adRepository->getUserAd($user, $ad);

        // Check if user owns this ad
        if ($adModel->user_id !== $user->id) {
            abort(403, 'You can only edit your own ads.');
        }

        // Get all necessary data for the form
        $categories = Category::whereNull('parent_id')->with('children')->get();

        // Get subcategories if ad has a category
        $subcategories = collect();
        if ($adModel->category && $adModel->category->parent_id) {
            $subcategories = Category::where('parent_id', $adModel->category->parent_id)->get();
        }

        // Get category attributes
        $categoryAttributes = collect();
        if ($adModel->category_id) {
            $categoryAttributes = CategoryAttribute::where('category_id', $adModel->category_id)
                ->orWhere('category_id', $adModel->category->parent_id ?? $adModel->category_id)
                ->get();
        }

        return view('pages.live-auction.create', [
            'categories' => $categories,
            'subcategories' => $subcategories,
            'categoryAttributes' => $categoryAttributes,
            'editingAd' => $adModel, // Pass the ad for editing
            'isEditing' => true, // Flag to indicate we're editing
        ]);
    }

    /**
     * Update an existing ad listing.
     *
     * @param CreateAdRequest $request
     * @param string $slug
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateListing(CreateAdRequest $request, string $slug): RedirectResponse
    {
        $user = $this->authRepository->user();
        $data = $request->validated();

        Log::info('Updating existing ad via updateListing', ['slug' => $slug, 'user_id' => $user->id]);

        // Find the ad by slug
        $existingAd = Ad::where('slug', $slug)->where('user_id', $user->id)->first();

        if (!$existingAd) {
            Log::error('Ad not found or user does not own it', ['slug' => $slug, 'user_id' => $user->id]);
            abort(403, 'You can only edit your own ads.');
        }

        Log::info('Found ad to update', ['ad_id' => $existingAd->id, 'old_title' => $existingAd->title, 'new_title' => $data['title']]);

        // Update the ad with new data
        $ad = $this->adRepository->updateAd($existingAd, $data);

        Log::info('Ad updated successfully via updateListing', ['ad_id' => $ad->id, 'new_status' => $ad->status->value]);

        return redirect()->route('user.dashboard')->with('success', 'Your ad has been updated successfully! It will be reviewed by our team before being published again.');
    }
}
