<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateChatifyMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
public function up()
{
    Schema::create('ch_messages', function (Blueprint $table) {
        $table->uuid('id')->primary();
        $table->string('from_id', 36);
        $table->string('to_id', 36);
        $table->string('body',5000)->nullable();
        $table->string('attachment')->nullable();
        $table->boolean('seen')->default(false);
        $table->timestamps();
    });
}

    
        public function down()
        {
            Schema::table('ch_messages', function (Blueprint $table) {
                $table->string('from_id', 255)->change();
                $table->string('to_id', 255)->change();
            });
        }

}
