<?php

namespace App\Http\Middleware;

use App\Models\UserWarning;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckChatWarnings
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Check if user has active chat ban
        if ($user->hasActiveWarning(UserWarning::TYPE_CHAT_BAN)) {
            $warning = $user->getActiveWarning(UserWarning::TYPE_CHAT_BAN);

            $timeMessage = $warning->expires_at ?
                " حتى {$warning->expires_at->format('d/m/Y H:i')}" :
                " بشكل دائم";

            $message = "تم منعك من استخدام الشات{$timeMessage}. السبب: {$warning->reason}";

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'warning' => [
                        'type' => $warning->type_label,
                        'title' => $warning->title,
                        'reason' => $warning->reason,
                        'expires_at' => $warning->expires_at?->format('d/m/Y H:i'),
                        'remaining_time' => $warning->remaining_time
                    ]
                ], 403);
            }

            // Redirect to dashboard with warning message
            return redirect()->route('user.dashboard')->withErrors(['warning' => $message]);
        }

        return $next($request);
    }
}
