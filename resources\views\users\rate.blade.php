@extends('partials.app')
@section('title', 'Rate ' . $user->name)
@section('description', 'Rate and review ' . $user->name . ' on our platform')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('content')

<!-- Hero Section -->
<div class="rating-hero">
    <div class="container">
        <div class="hero-breadcrumb">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ url('user-profile/' . $user->username) }}">{{ $user->name }}</a></li>
                    <li class="breadcrumb-item active">Rate User</li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="rating-main-section">
    <div class="container">
        <div class="row">
            <!-- User Profile Card -->
            <div class="col-lg-4">
                <div class="user-profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <img src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}" alt="{{ $user->name }}">
                            @if($user->is_trusted)
                                <div class="verified-badge-blue">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            @endif
                        </div>
                        <div class="profile-info">
                            <h3 class="profile-name">{{ $user->name }}</h3>
                            <p class="profile-username">{{ $user->username }}</p>

                            <!-- User Badges -->
                            <div class="user-badges">
                                <span class="badge badge-rank rank-{{ $user->rank ?? 0 }}">
                                    @switch($user->rank)
                                        @case(1)
                                            <i class="fas fa-crown"></i>
                                            VIP Member
                                        @break
                                        @case(2)
                                            <i class="fas fa-chart-line"></i>
                                            Trader
                                        @break
                                        @case(3)
                                            <i class="fas fa-building"></i>
                                            Company
                                        @break
                                        @case(4)
                                            <i class="fas fa-gem"></i>
                                            Premium
                                        @break
                                        @default
                                            <i class="fas fa-user"></i>
                                            Regular Member
                                    @endswitch
                                </span>

                                @if($user->is_trusted)
                                    <span class="badge badge-verified">
                                        <i class="fas fa-shield-check"></i>
                                        Verified
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Rating Summary -->
                    <div class="rating-summary">
                        <div class="rating-overview">
                            <div class="rating-score">
                                <span class="score-number">{{ number_format($user->average_rating ?? 0, 1) }}</span>
                                <span class="score-max">/5</span>
                            </div>
                            <div class="rating-stars">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= round($user->average_rating ?? 0) ? 'filled' : '' }}"></i>
                                @endfor
                            </div>
                            <p class="rating-count">Based on {{ $user->total_ratings ?? 0 }} reviews</p>
                        </div>
                    </div>

                    <!-- User Stats -->
                    <div class="user-stats">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-value">{{ $user->Number_Ads }}</span>
                                <span class="stat-label">Published Ads</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div class="stat-content">
                                <span class="stat-value">{{ $user->created_at ? $user->created_at->format('F Y') : 'N/A' }}</span>
                                <span class="stat-label">Member since</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Main Content Area -->
            <div class="col-lg-8">
                <!-- Alerts -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif
                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if($canRate)
                <!-- Rating Form -->
                <div class="rating-form-card">
                    <div class="card-header">
                        <h4><i class="fas fa-star"></i> Rate This User</h4>
                        <p>Share your experience with {{ $user->name }}</p>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('rate-user', ['user' => $user->id]) }}" class="rating-form">
                            @csrf

                            <!-- Star Rating -->
                            <div class="form-group">
                                <label class="form-label">Your Rating</label>
                                <div class="star-rating-input">
                                    <div class="stars-container">
                                        @for($i = 1; $i <= 5; $i++)
                                            <span class="star" data-rating="{{ $i }}">
                                                <i class="fas fa-star"></i>
                                            </span>
                                        @endfor
                                    </div>
                                    <span class="rating-text">Click to rate</span>
                                    <input type="hidden" name="rating" id="rating" required>
                                </div>
                                @error('rating')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Comment -->
                            <div class="form-group">
                                <label for="comment" class="form-label">Your Review (Optional)</label>
                                <textarea name="comment" id="comment" class="form-control"
                                          rows="4" maxlength="500"
                                          placeholder="Share your experience with {{ $user->name }}..."></textarea>
                                <div class="form-text">Maximum 500 characters</div>
                                @error('comment')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i>
                                Submit Rating
                            </button>
                        </form>
                    </div>
                </div>
                @else
                <!-- Cannot Rate Message -->
                <div class="cannot-rate-card">
                    <div class="cannot-rate-content">
                        <i class="fas fa-info-circle"></i>
                        <h4>Cannot Rate This User</h4>
                        <p>You cannot rate this user because :</p>
                        <ul>
                            @if(!auth()->user()->is_trusted)
                                <li>Your account is not verified</li>
                            @endif
                            @if(auth()->id() === $user->id)
                                <li>You cannot rate yourself</li>
                            @endif
                            @if(auth()->user()->is_trusted && auth()->id() !== $user->id)
                                <li>You have already rated this user</li>
                            @endif
                        </ul>
                    </div>
                </div>
                @endif


                <!-- Reviews List -->
                <div class="reviews-section">
                    <div class="reviews-header">
                        <h4><i class="fas fa-comments"></i> User Reviews</h4>
                        <span class="reviews-count">{{ count($ratings) }} reviews</span>
                    </div>

                    <div class="reviews-list">
                        @forelse($ratings as $rating)
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <div class="reviewer-avatar">
                                            <img src="{{ $rating->rater->avatar ?? get_random_avatar() }}" alt="{{ $rating->rater->name }}">
                                            @if($rating->rater->is_trusted)
                                                <div class="verified-badge-small">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="reviewer-details">
                                            <h6 class="reviewer-name">{{ $rating->rater->name }}</h6>
                                            <span class="reviewer-username">{{ $rating->rater->username }}</span>
                                            <span class="review-date">{{ $rating->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                    <div class="review-rating">
                                        <div class="rating-stars">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= round($rating->rating) ? 'filled' : '' }}"></i>
                                            @endfor
                                        </div>
                                        <span class="rating-value">{{ number_format($rating->rating, 1) }}/5</span>
                                    </div>
                                    @auth
                                        @if(auth()->id() !== $rating->rater_id)
                                            <button type="button" class="report-btn report-btn-sm"
                                                    data-type="App\Models\UserRating"
                                                    data-id="{{ $rating->id }}"
                                                    title="إبلاغ عن هذا التقييم">
                                                <i class="fas fa-flag"></i>
                                            </button>
                                        @endif
                                    @endauth
                                </div>

                                @if($rating->comment)
                                    <div class="review-comment">
                                        <p>"{{ $rating->comment }}"</p>
                                    </div>
                                @endif
                            </div>
                        @empty
                            <div class="no-reviews">
                                <div class="no-reviews-icon">
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <h5>No Reviews Yet</h5>
                                <p>{{ $user->name }} hasn't received any reviews yet. Be the first to share your experience!</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Hero Section */
.rating-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem 0;
    border-bottom: 1px solid #dee2e6;
}

.hero-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #6c757d;
}

/* Main Section */
.rating-main-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

/* User Profile Card */
.user-profile-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    position: sticky;
    top: 2rem;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.profile-avatar img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e9ecef;
}

.verified-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: 3px solid white;
    font-size: 0.8rem;
}

.verified-badge-blue {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    animation: verifiedPulse 2s infinite;
}

@keyframes verifiedPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.5);
    }
}

.profile-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.profile-username {
    color: #6c757d;
    margin-bottom: 1rem;
}

.user-badges {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.badge-rank {
    background: #e2e3e5;
    color: #383d41;
}

.badge-rank.rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b4513;
}

.badge-rank.rank-2 {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
}

.badge-rank.rank-3 {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
}

.badge-rank.rank-4 {
    background: linear-gradient(135deg, #fd7e14, #ffc107);
    color: white;
}

.badge-verified {
    background: #d4edda;
    color: #155724;
}

/* Rating Summary */
.rating-summary {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

.rating-overview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.rating-score {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
}

.score-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.score-max {
    font-size: 1.2rem;
    color: #6c757d;
}

.rating-stars {
    display: flex;
    gap: 0.25rem;
}

.rating-stars i {
    font-size: 1.2rem;
    color: #dee2e6;
}

.rating-stars i.filled {
    color: #ffc107;
}

.rating-count {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

/* User Stats */
.user-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Rating Form */
.rating-form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
}

.rating-form-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.rating-form-card .card-header h4 {
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.rating-form-card .card-header p {
    margin: 0;
    opacity: 0.9;
}

.rating-form-card .card-body {
    padding: 2rem;
}

/* Star Rating Input */
.star-rating-input {
    text-align: center;
    margin-bottom: 1.5rem;
}

.stars-container {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.star {
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 2rem;
    color: #dee2e6;
}

.star:hover,
.star.active {
    color: #ffc107;
    transform: scale(1.1);
}

.rating-text {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Cannot Rate Card */
.cannot-rate-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    padding: 2rem;
    text-align: center;
}

.cannot-rate-content i {
    font-size: 3rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.cannot-rate-content h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.cannot-rate-content ul {
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
}

/* Reviews Section */
.reviews-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.reviews-header {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reviews-header h4 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reviews-count {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
}

.reviews-list {
    padding: 2rem;
}

/* Review Item */
.review-item {
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.review-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.review-item:last-child {
    margin-bottom: 0;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reviewer-avatar {
    position: relative;
}

.reviewer-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.verified-badge-small {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: 2px solid white;
    font-size: 0.6rem;
}

.reviewer-details {
    display: flex;
    flex-direction: column;
}

.reviewer-name {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.reviewer-username {
    color: #6c757d;
    font-size: 0.875rem;
}

.review-date {
    color: #6c757d;
    font-size: 0.8rem;
}

.review-rating {
    text-align: right;
}

.review-rating .rating-stars {
    margin-bottom: 0.25rem;
}

.review-rating .rating-stars i {
    font-size: 1rem;
}

.rating-value {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 600;
}

.review-comment {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.review-comment p {
    margin: 0;
    color: #495057;
    font-style: italic;
    line-height: 1.6;
}

/* No Reviews */
.no-reviews {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-reviews-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-reviews h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 992px) {
    .user-profile-card {
        position: static;
        margin-bottom: 2rem;
    }

    .review-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .review-rating {
        text-align: left;
    }
}

@media (max-width: 768px) {
    .rating-main-section {
        padding: 2rem 0;
    }

    .user-profile-card,
    .rating-form-card,
    .reviews-section {
        margin: 0 1rem 2rem 1rem;
    }

    .stars-container {
        gap: 0.25rem;
    }

    .star {
        font-size: 1.5rem;
    }

    .reviewer-info {
        gap: 0.75rem;
    }

    .reviewer-avatar img {
        width: 40px;
        height: 40px;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star Rating Functionality
    const stars = document.querySelectorAll('.star');
    const ratingInput = document.getElementById('rating');
    const ratingText = document.querySelector('.rating-text');
    let selectedRating = 0;

    stars.forEach((star, index) => {
        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });

        star.addEventListener('mouseleave', function() {
            highlightStars(selectedRating);
        });

        star.addEventListener('click', function() {
            selectedRating = index + 1;
            ratingInput.value = selectedRating;
            highlightStars(selectedRating);
            updateRatingText(selectedRating);
        });
    });

    function highlightStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }

    function updateRatingText(rating) {
        const texts = {
            1: 'Poor',
            2: 'Fair',
            3: 'Good',
            4: 'Very Good',
            5: 'Excellent'
        };
        ratingText.textContent = texts[rating] || 'Click to rate';
    }

    // Form Validation
    const form = document.querySelector('.rating-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!selectedRating) {
                e.preventDefault();
                alert('Please select a rating before submitting.');
                return false;
            }
        });
    }

    // Character Counter for Comment
    const commentTextarea = document.getElementById('comment');
    if (commentTextarea) {
        const maxLength = 500;
        const formText = commentTextarea.nextElementSibling;

        commentTextarea.addEventListener('input', function() {
            const remaining = maxLength - this.value.length;
            formText.textContent = `${remaining} characters remaining`;

            if (remaining < 50) {
                formText.style.color = '#dc3545';
            } else {
                formText.style.color = '#6c757d';
            }
        });
    }

    // Smooth Animations
    const animateElements = document.querySelectorAll('.user-profile-card, .rating-form-card, .reviews-section');
    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    animateElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        animationObserver.observe(element);
    });

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.querySelector('.btn-close')) {
                alert.querySelector('.btn-close').click();
            }
        }, 5000);
    });
});
</script>
@endpush

<!-- Include Report Modal -->
@include('components.report-modal')

@endsection