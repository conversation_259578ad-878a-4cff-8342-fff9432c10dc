<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\BrokerInterest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class BrokerInterestController extends Controller
{
    /**
     * إظهار الاهتمام بإعلان
     */
    public function showInterest(Request $request, Ad $ad)
    {
        $user = Auth::user();

        // التحقق من أن المستخدم مندوب
        if (!$user->is_broker) {
            return response()->json([
                'success' => false,
                'message' => 'يجب أن تكون مندوباً لإظهار الاهتمام'
            ], 403);
        }

        // التحقق من عدم وجود اهتمام سابق
        if ($user->hasInterestInAd($ad->id)) {
            return response()->json([
                'success' => false,
                'message' => 'لديك اهتمام سابق بهذا الإعلان'
            ], 400);
        }

        // حساب النقاط المطلوبة
        $requiredPoints = BrokerInterest::calculateRequiredPoints($ad->price);

        // التحقق من وجود نقاط كافية
        if (!$user->hasEnoughPoints($requiredPoints)) {
            return response()->json([
                'success' => false,
                'message' => "تحتاج إلى {$requiredPoints} نقطة لإظهار الاهتمام بهذا الإعلان. رصيدك الحالي: {$user->points} نقطة"
            ], 400);
        }

        try {
            DB::beginTransaction();

            // خصم النقاط من المندوب
            $user->deductPoints($requiredPoints);

            // إنشاء سجل الاهتمام
            $interest = BrokerInterest::create([
                'broker_id' => $user->id,
                'ad_id' => $ad->id,
                'points_paid' => $requiredPoints,
                'expires_at' => now()->addDays(10),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إظهار الاهتمام بنجاح',
                'points_paid' => $requiredPoints,
                'remaining_points' => $user->fresh()->points,
                'interest_id' => $interest->id
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إظهار الاهتمام'
            ], 500);
        }
    }

    /**
     * إدخال كود المندوب
     */
    public function enterCode(Request $request, Ad $ad)
    {
        $request->validate([
            'broker_code' => 'required|string'
        ]);

        $user = Auth::user();

        // التحقق من وجود اهتمام نشط
        $interest = $user->getInterestInAd($ad->id);
        if (!$interest) {
            return response()->json([
                'success' => false,
                'message' => 'يجب إظهار الاهتمام أولاً'
            ], 400);
        }

        // التحقق من عدم إدخال الكود مسبقاً
        if ($interest->code_entered) {
            return response()->json([
                'success' => false,
                'message' => 'تم إدخال الكود مسبقاً'
            ], 400);
        }

        // التحقق من صحة الكود
        if ($request->broker_code !== $ad->broker_code) {
            return response()->json([
                'success' => false,
                'message' => 'كود المندوب غير صحيح'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // إدخال الكود وكسب النقاط
            $interest->enterCode();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إدخال الكود بنجاح وحصلت على النقاط',
                'points_earned' => $interest->points_earned,
                'total_points' => $user->fresh()->points
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إدخال الكود'
            ], 500);
        }
    }

    /**
     * طلب الانسحاب من الاهتمام
     */
    public function withdraw(Request $request, BrokerInterest $interest)
    {
        $user = Auth::user();

        // التحقق من ملكية الاهتمام
        if ($interest->broker_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بهذا الإجراء'
            ], 403);
        }

        try {
            DB::beginTransaction();

            $result = $interest->requestWithdrawal();

            DB::commit();

            return response()->json($result);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء طلب الانسحاب'
            ], 500);
        }
    }

    /**
     * الحصول على معلومات الاهتمام
     */
    public function getInterestInfo(Ad $ad)
    {
        $user = Auth::user();

        if (!$user->is_broker) {
            return response()->json([
                'success' => false,
                'message' => 'يجب أن تكون مندوباً'
            ], 403);
        }

        $interest = $user->getInterestInAd($ad->id);
        $requiredPoints = BrokerInterest::calculateRequiredPoints($ad->price);

        return response()->json([
            'success' => true,
            'has_interest' => (bool) $interest,
            'required_points' => $requiredPoints,
            'user_points' => $user->points,
            'can_afford' => $user->hasEnoughPoints($requiredPoints),
            'interest' => $interest ? [
                'id' => $interest->id,
                'points_paid' => $interest->points_paid,
                'points_earned' => $interest->points_earned,
                'code_entered' => $interest->code_entered,
                'code_entered_at' => $interest->code_entered_at,
                'days_left_for_withdrawal' => $interest->getDaysLeftForWithdrawal(),
                'withdrawal_penalty_percentage' => $interest->getWithdrawalPenalty(),
                'can_withdraw' => $interest->status === 'active' && $interest->getDaysLeftForWithdrawal() > 0,
            ] : null
        ]);
    }
}
