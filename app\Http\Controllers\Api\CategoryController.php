<?php

namespace App\Http\Controllers\Api;
use App\Contracts\Repositories\CategoryRepositoryInterface;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;

class CategoryController extends Controller
{
    /**
     * Instantiate a new controller instance.
     * 
     * @return void
     */
    public function __construct(protected CategoryRepositoryInterface $categoryRepository)
    {
    }

    /**
     * Get the categories with sub categories.
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */


    /**
     * Get children of a specific category.
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChildren(string $slug): JsonResponse
    {
        return $this->response('children', $this->categoryRepository->getChildren($slug));
    }

    /**
     * Get subcategories (legacy method for backward compatibility).
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSubCategories(string $slug): JsonResponse
    {
        return $this->response('data', $this->categoryRepository->getChildren($slug));
    }

    /**
     * Get category tree structure.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryTree(): JsonResponse
    {
        return $this->response('tree', $this->categoryRepository->getCategoryTree());
    }

    /**
     * Get category breadcrumb.
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBreadcrumb(string $slug): JsonResponse
    {
        return $this->response('breadcrumb', $this->categoryRepository->getBreadcrumb($slug));
    }

    /**
     * Get root categories only.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRootCategories(): JsonResponse
    {
        return $this->response('categories', $this->categoryRepository->getPrimaryCategories());
    }

    /**
     * Get all descendants of a category.
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDescendants(string $slug): JsonResponse
    {
        return $this->response('descendants', $this->categoryRepository->getDescendants($slug));
    }

    /**
     * Get category with all its children (nested structure).
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryWithChildren(string $slug): JsonResponse
    {
        try {
            $category = $this->categoryRepository->findBySlug($slug);
            $categoryWithChildren = $category->load('children.children.children'); // Load 3 levels deep

            return $this->response('category', $categoryWithChildren);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found',
                'data' => null
            ], 404);
        }
    }
}
