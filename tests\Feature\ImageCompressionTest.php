<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\ImageCompressionService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ImageCompressionTest extends TestCase
{
    use RefreshDatabase;

    protected ImageCompressionService $compressionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->compressionService = new ImageCompressionService();
        Storage::fake('public');
    }

    /** @test */
    public function it_can_compress_and_store_image()
    {
        // Create a fake image
        $file = UploadedFile::fake()->image('test.jpg', 1200, 800)->size(2048); // 2MB

        // Compress and store
        $result = $this->compressionService->compressAndStore($file, 'test', [
            'quality' => 75,
            'max_width' => 800,
            'max_height' => 600,
            'format' => 'webp'
        ]);

        // Assert success
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('path', $result);
        $this->assertArrayHasKey('compression_ratio', $result);
        $this->assertEquals('webp', $result['format']);

        // Assert file exists
        Storage::disk('public')->assertExists($result['path']);

        // Assert compression occurred
        $this->assertGreaterThan(0, $result['compression_ratio']);
    }

    /** @test */
    public function it_provides_optimized_settings_for_different_image_types()
    {
        $adSettings = $this->compressionService->getOptimizedSettings('ad_images');
        $profileSettings = $this->compressionService->getOptimizedSettings('profile_avatar');
        $documentSettings = $this->compressionService->getOptimizedSettings('identity_documents');

        // Ad images should use WebP
        $this->assertEquals('webp', $adSettings['format']);
        $this->assertEquals(80, $adSettings['quality']);

        // Profile avatars should be smaller
        $this->assertEquals(400, $profileSettings['max_width']);
        $this->assertEquals('webp', $profileSettings['format']);

        // Documents should use JPG for better text readability
        $this->assertEquals('jpg', $documentSettings['format']);
        $this->assertEquals(90, $documentSettings['quality']); // Higher quality for documents
    }

    /** @test */
    public function it_validates_image_files_correctly()
    {
        // Valid image
        $validImage = UploadedFile::fake()->image('test.jpg', 800, 600);
        $validation = $this->compressionService->validateImage($validImage);
        $this->assertTrue($validation['valid']);

        // Invalid file type
        $invalidFile = UploadedFile::fake()->create('test.txt', 100);
        $validation = $this->compressionService->validateImage($invalidFile);
        $this->assertFalse($validation['valid']);
        $this->assertStringContainsString('Invalid file type', $validation['error']);

        // File too large
        $largeFile = UploadedFile::fake()->image('large.jpg', 2000, 2000)->size(15000); // 15MB
        $validation = $this->compressionService->validateImage($largeFile, 10); // 10MB limit
        $this->assertFalse($validation['valid']);
        $this->assertStringContainsString('exceeds', $validation['error']);
    }

    /** @test */
    public function it_can_delete_images()
    {
        // Create and store an image
        $file = UploadedFile::fake()->image('test.jpg');
        $result = $this->compressionService->compressAndStore($file, 'test');
        
        Storage::disk('public')->assertExists($result['path']);

        // Delete the image
        $deleted = $this->compressionService->deleteImage($result['path']);
        $this->assertTrue($deleted);
        
        Storage::disk('public')->assertMissing($result['path']);
    }

    /** @test */
    public function it_formats_file_sizes_correctly()
    {
        $this->assertEquals('1 KB', $this->compressionService->formatFileSize(1024));
        $this->assertEquals('1 MB', $this->compressionService->formatFileSize(1024 * 1024));
        $this->assertEquals('1.5 MB', $this->compressionService->formatFileSize(1024 * 1024 * 1.5));
        $this->assertEquals('500 B', $this->compressionService->formatFileSize(500));
    }

    /** @test */
    public function it_handles_compression_errors_gracefully()
    {
        // Try to compress a non-existent file (this should fail)
        $invalidFile = new UploadedFile('/non/existent/path', 'test.jpg', 'image/jpeg', null, true);
        
        $result = $this->compressionService->compressAndStore($invalidFile, 'test');
        
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error', $result);
    }

    /** @test */
    public function compression_reduces_file_size_significantly()
    {
        // Create a large fake image
        $file = UploadedFile::fake()->image('large.jpg', 2000, 1500)->size(5000); // 5MB

        // Compress with aggressive settings
        $result = $this->compressionService->compressAndStore($file, 'test', [
            'quality' => 60,
            'max_width' => 1200,
            'max_height' => 900,
            'format' => 'webp'
        ]);

        $this->assertTrue($result['success']);
        
        // Should achieve significant compression
        $this->assertGreaterThan(30, $result['compression_ratio']); // At least 30% reduction
        $this->assertLessThan($result['original_size'], $result['compressed_size']);
    }
}
