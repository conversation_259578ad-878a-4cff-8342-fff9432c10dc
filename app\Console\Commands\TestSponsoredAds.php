<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SponsoredAd;
use App\Models\Ad;
use Illuminate\Support\Facades\Schema;

class TestSponsoredAds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-sponsored-ads';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sponsored ads system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Sponsored Ads System...');

        // Check if tables exist
        $this->info('Checking tables...');
        $this->info('Ads table exists: ' . (Schema::hasTable('ads') ? 'Yes' : 'No'));
        $this->info('Sponsored Ads table exists: ' . (Schema::hasTable('sponsored_ads') ? 'Yes' : 'No'));

        if (Schema::hasTable('sponsored_ads')) {
            $this->info('Sponsored Ads columns: ' . implode(', ', Schema::getColumnListing('sponsored_ads')));
        }

        if (Schema::hasTable('ads')) {
            $this->info('Ads table has notes column: ' . (Schema::hasColumn('ads', 'notes') ? 'Yes' : 'No'));
            $this->info('Ads table has is_sponsored column: ' . (Schema::hasColumn('ads', 'is_sponsored') ? 'Yes' : 'No'));
        }

        $this->info('Test completed!');
    }
}
