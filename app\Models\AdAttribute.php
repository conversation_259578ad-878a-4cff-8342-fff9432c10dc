<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdAttribute extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'category_attribute_id',
        'attribute_name',
        'attribute_label',
        'attribute_value',
        'attribute_type'
    ];

    /**
     * Get the ad that owns this attribute.
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * Get the category attribute definition.
     */
    public function categoryAttribute(): BelongsTo
    {
        return $this->belongsTo(CategoryAttribute::class);
    }

    /**
     * Get the formatted value for display.
     */
    public function getFormattedValueAttribute(): string
    {
        // If it's a select type, try to get the display value from category attribute options
        if ($this->attribute_type === 'select' && $this->categoryAttribute) {
            $options = $this->categoryAttribute->attribute_options;
            if (is_array($options) && isset($options[$this->attribute_value])) {
                return $options[$this->attribute_value];
            }
        }
        
        return $this->attribute_value;
    }
}
