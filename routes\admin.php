<?php

use App\Http\Controllers\Admin\Ad\AdController;
use App\Http\Controllers\Admin\Auth\LoginController;
use App\Http\Controllers\Admin\Auth\PasswordController;
use App\Http\Controllers\Admin\Bid\BidController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Admin\UserWarningController;
use App\Http\Controllers\Admin\Dashboard\MetricsController;
use App\Http\Controllers\Admin\Media\MediaController;
use App\Http\Controllers\Admin\Payment\PaymentController;
use App\Http\Controllers\Admin\Payout\PayoutController;
use App\Http\Controllers\Admin\Payout\PayoutMethodController;
use App\Http\Controllers\Admin\Post\CommentController;
use App\Http\Controllers\Admin\Post\PostController;
use App\Http\Controllers\Admin\Profile\ProfileController;
use App\Http\Controllers\Admin\Search\SearchController;
use App\Http\Controllers\Admin\Support\SupportController;
use App\Http\Controllers\Admin\User\UserController;
use App\Http\Controllers\Admin\BrokerManagementController;
use App\Http\Controllers\Admin\BrokerApplicationManagementController;
use App\Http\Controllers\Admin\SponsoredAdController;
use App\Http\Controllers\Admin\SponsoredAdAnalyticsController;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "admin" middleware group. Make something great!
|
*/

/**
 * Auth Routes
 */
Route::middleware('guest:admin_web')->group(function () {
    Route::view('/login', 'auth.admin.login')->name('login');
    Route::post('/login', [LoginController::class, 'login'])->name('login.handle');
    Route::view('/forgot-password', 'auth.admin.password.forgot')->name('forgot-password');
    Route::post('/forgot-password', [PasswordController::class, 'forgotPassword'])->name('forgot-password.handle');
    Route::get('/reset-password/{token}', [PasswordController::class, 'resetPasswordForm'])->name('reset-password');
    Route::post('/reset-password', [PasswordController::class, 'resetPassword'])->name('reset-password.handle');
});


Route::middleware(['auth:admin_web', 'ensure.account.active'])->group(function () {
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout.handle');

    Route::get('/', [MetricsController::class, 'index'])->name('dashboard');
    Route::get(uri: '/search', action: SearchController::class)->name('search');

    /* ========  PROFILE MANAGEMENT  =========== */
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/change-password', [ProfileController::class, 'changePassword'])->name('profile.change-password.handle');

    /* ========  USER  =========== */
    Route::resource('users', UserController::class);
    Route::get('/users/{users:id}/request-password-reset', [UserController::class, 'requestPasswordReset'])->name('users.request-password-reset');
    Route::get('/users/{users:id}/impersonate', [UserController::class, 'impersonate'])->name('impersonate');
    Route::get('/leave-impersonate', [UserController::class, 'leaveImpersonate'])->name('leave-impersonate');

    // Broker Management Routes
    Route::post('/users/{id}/activate-broker', [UserController::class, 'activateBroker'])->name('users.activate-broker');
    Route::post('/users/{id}/deactivate-broker', [UserController::class, 'deactivateBroker'])->name('users.deactivate-broker');

    /* ========  ADS  =========== */
    Route::get('/ads', [AdController::class, 'index'])->name('ads.index');
    Route::get('/ads/pending', [AdController::class, 'pending'])->name('ads.pending');
    Route::get('/ads/upcoming', [AdController::class, 'upcoming'])->name('ads.upcoming');
    Route::get('/ads/rejected', [AdController::class, 'rejected'])->name('ads.rejected');
    Route::get('/ads/expired', [AdController::class, 'expired'])->name('ads.expired');
    Route::get('/ads/active', [AdController::class, 'active'])->name('ads.active');
    Route::get('/ads/reported', [AdController::class, 'reported'])->name('ads.reported');
    Route::get('/ad/{ads:slug}/report', [AdController::class, 'reportAd'])->name('ads.reported.show');
    Route::get('/ad/{ads:slug}', [AdController::class, 'show'])->name('ads.show');
    Route::get('/ad/{ads:slug}/edit', [AdController::class, 'edit'])->name('ads.edit');
    Route::put('/ad/{ads:slug}/edit', [AdController::class, 'update'])->name('ads.update');
    Route::delete('/ad/{ads:slug}/', [AdController::class, 'destroy'])->name('ads.destroy');

    /* ========  BIDS  =========== */
    Route::get('/bids', [BidController::class, 'index'])->name('bids.index');
    Route::get('/bids/{bids:id}', [BidController::class, 'show'])->name('bids.show');

    /* ========  PAYOUT METHOD  =========== */
    Route::get('/payout-methods', [PayoutMethodController::class, 'index'])->name('payout-methods.index');
    Route::get('/payout-methods/{payoutMethods:id}', [PayoutMethodController::class, 'show'])->name('payout-methods.show');

    /* ========  PAYMENTS  =========== */
    Route::get('/payments', [PaymentController::class, 'index'])->name('payments.index');
    Route::get('/payments/pending', [PaymentController::class, 'pending'])->name('payments.pending');
    Route::get('/payments/success', [PaymentController::class, 'success'])->name('payments.success');
    Route::get('/payments/failed', [PaymentController::class, 'failed'])->name('payments.failed');
    Route::get('/payment/{payments:txn_id}', [PaymentController::class, 'show'])->name('payments.show');
    Route::patch('/payment/{payments:txn_id}/update-status', [PaymentController::class, 'updateStatus'])->name('payments.update.status');

    /* ======== PAYOUTS  ======== */
    Route::get('/payouts', [PayoutController::class, 'index'])->name('payouts.index');
    Route::get('/payouts/pending', [PayoutController::class, 'pending'])->name('payouts.pending');
    Route::get('/payouts/success', [PayoutController::class, 'success'])->name('payouts.success');
    Route::get('/payouts/failed', [PayoutController::class, 'failed'])->name('payouts.failed');
    Route::get('/payout/{payouts:pyt_token}', [PayoutController::class, 'show'])->name('payouts.show');

    /* ========  BLOGS  =========== */
    Route::resource('blogs', PostController::class);
    Route::post('/blogs/{post:slug}/comment', [CommentController::class, 'store'])->name('blogs.comment.store');
    Route::resource('comments', CommentController::class)->only(['index', 'update', 'edit', 'destroy']);

    /* ========  MEDIA  =========== */
    Route::get('/media', [MediaController::class, 'index'])->name('media.index');
    Route::get('/media/{media:id}', [MediaController::class, 'show'])->name('media.show');
    Route::delete('/media/{media:id}', [MediaController::class, 'destroy'])->name('media.destroy');

    /* ========  SUPPORT  =========== */
    Route::get('/supports', [SupportController::class, 'index'])->name('support.index');
    Route::get('/supports/pending', [SupportController::class, 'pending'])->name('support.pending');
    Route::get('/supports/resolved', [SupportController::class, 'resolved'])->name('support.resolved');
    Route::get('/supports/create', [SupportController::class, 'create'])->name('support.create');
    Route::put('/supports/{supports:id}/update', [SupportController::class, 'update'])->name('support.update');
    Route::post('/supports', [SupportController::class, 'store'])->name('support.store');
    Route::get('/support/{supports:id}', [SupportController::class, 'show'])->name('support.show');
    Route::delete('/support/{supports:id}', [SupportController::class, 'destroy'])->name('support.destroy');

    // Categories management routes
    Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::post('/categories', [CategoryController::class, 'store'])->name('categories.store');
    Route::put('/categories/{category}', [CategoryController::class, 'update'])->name('categories.update');
    Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy');

    // Category attributes routes
    Route::post('/categories/{category}/attributes', [CategoryController::class, 'storeAttribute'])->name('categories.attributes.store');
    Route::put('/attributes/{attribute}', [CategoryController::class, 'updateAttribute'])->name('categories.attributes.update');
    Route::delete('/attributes/{attribute}', [CategoryController::class, 'destroyAttribute'])->name('categories.attributes.destroy');

    // AJAX routes for categories
    Route::get('/categories/{category}/subcategories', [CategoryController::class, 'getSubcategories'])->name('categories.subcategories');
    Route::get('/categories/{category}/attributes', [CategoryController::class, 'getAttributes'])->name('categories.attributes.get');

    // Reports management routes
    Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
    Route::get('/reports/show/{report}', [ReportController::class, 'show'])->name('reports.show');
    Route::get('/reports/{type}', [ReportController::class, 'byType'])->name('reports.by-type');
    Route::patch('/reports/{report}/status', [ReportController::class, 'updateStatus'])->name('reports.update-status');
    Route::post('/reports/bulk-update', [ReportController::class, 'bulkUpdate'])->name('reports.bulk-update');

    // User warnings routes
    Route::post('/warnings', [UserWarningController::class, 'store'])->name('warnings.store');
    Route::get('/users/{user}/warnings', [UserWarningController::class, 'getUserWarnings'])->name('users.warnings');
    Route::patch('/warnings/{warning}/deactivate', [UserWarningController::class, 'deactivate'])->name('warnings.deactivate');
    Route::post('/warnings/delete-content', [UserWarningController::class, 'deleteContent'])->name('warnings.delete-content');

    /* ========  BROKER MANAGEMENT  =========== */
    Route::prefix('broker-management')->name('broker-management.')->group(function () {
        Route::get('/interests', [BrokerManagementController::class, 'interests'])->name('interests');
        Route::get('/successful-codes', [BrokerManagementController::class, 'successfulCodes'])->name('successful-codes');
        Route::get('/points-monitoring', [BrokerManagementController::class, 'pointsMonitoring'])->name('points-monitoring');
        Route::put('/update-points/{broker}', [BrokerManagementController::class, 'updatePoints'])->name('update-points');
        Route::delete('/delete-interest/{interest}', [BrokerManagementController::class, 'deleteInterest'])->name('delete-interest');
    });

    /* ========  BROKER APPLICATIONS  =========== */
    Route::prefix('broker-applications')->name('broker-applications.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'index'])->name('index');
        Route::get('/{application}', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'show'])->name('show');
        Route::post('/{application}/accept-first-stage', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'acceptFirstStage'])->name('accept-first-stage');
        Route::post('/{application}/accept-final', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'acceptFinal'])->name('accept-final');
        Route::post('/{application}/reject', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'reject'])->name('reject');
        Route::post('/{application}/update-national-id', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'updateNationalId'])->name('update-national-id');
        Route::delete('/{application}/delete', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'deleteRejected'])->name('delete-rejected');
        Route::delete('/delete-old-rejected', [App\Http\Controllers\Admin\BrokerApplicationManagementController::class, 'deleteOldRejected'])->name('delete-old-rejected');
    });

    /* ========  IDENTITY VERIFICATIONS  =========== */
    Route::prefix('identity-verifications')->name('identity-verifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\IdentityVerificationManagementController::class, 'index'])->name('index');
        Route::get('/{identityVerification}', [App\Http\Controllers\Admin\IdentityVerificationManagementController::class, 'show'])->name('show');
        Route::post('/{identityVerification}/approve', [App\Http\Controllers\Admin\IdentityVerificationManagementController::class, 'approve'])->name('approve');
        Route::post('/{identityVerification}/reject', [App\Http\Controllers\Admin\IdentityVerificationManagementController::class, 'reject'])->name('reject');
        Route::post('/{identityVerification}/update-national-id', [App\Http\Controllers\Admin\IdentityVerificationManagementController::class, 'updateNationalId'])->name('update-national-id');
    });

    /* ========  SPONSORED ADS  =========== */
    Route::prefix('sponsored-ads')->name('sponsored-ads.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SponsoredAdController::class, 'index'])->name('index');
        Route::get('/update-all', [App\Http\Controllers\Admin\SponsoredAdController::class, 'updateAll'])->name('update-all');
        Route::get('/{sponsoredAd}', [App\Http\Controllers\Admin\SponsoredAdController::class, 'show'])->name('show');
        Route::get('/{sponsoredAd}/edit', [App\Http\Controllers\Admin\SponsoredAdController::class, 'edit'])->name('edit');
        Route::put('/{sponsoredAd}', [App\Http\Controllers\Admin\SponsoredAdController::class, 'update'])->name('update');
        Route::post('/{sponsoredAd}/activate', [App\Http\Controllers\Admin\SponsoredAdController::class, 'activate'])->name('activate');
        Route::post('/{sponsoredAd}/approve-and-activate', [App\Http\Controllers\Admin\SponsoredAdController::class, 'approveAndActivate'])->name('approve-and-activate');
        Route::post('/{sponsoredAd}/reject', [App\Http\Controllers\Admin\SponsoredAdController::class, 'reject'])->name('reject');
        Route::post('/{sponsoredAd}/deactivate', [App\Http\Controllers\Admin\SponsoredAdController::class, 'deactivate'])->name('deactivate');
        Route::post('/{sponsoredAd}/toggle-status', [App\Http\Controllers\Admin\SponsoredAdController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/{sponsoredAd}/update-priority', [App\Http\Controllers\Admin\SponsoredAdController::class, 'updatePriority'])->name('update-priority');
        Route::delete('/{sponsoredAd}', [App\Http\Controllers\Admin\SponsoredAdController::class, 'destroy'])->name('destroy');
        Route::delete('/{sponsoredAd}/destroy-ad', [App\Http\Controllers\Admin\SponsoredAdController::class, 'destroyAd'])->name('destroy-ad');

        // AJAX routes for location data
        Route::get('/countries/{country}/states', [App\Http\Controllers\Admin\SponsoredAdController::class, 'getStates'])->name('get-states');
        Route::get('/states/{state}/cities', [App\Http\Controllers\Admin\SponsoredAdController::class, 'getCities'])->name('get-cities');
    });

    /* ========  SPONSORED ADS ANALYTICS  =========== */
    Route::prefix('sponsored-ads-analytics')->name('sponsored-ads-analytics.')->group(function () {
        Route::get('/', [SponsoredAdAnalyticsController::class, 'index'])->name('index');
        Route::get('/export-csv', [SponsoredAdAnalyticsController::class, 'exportCSV'])->name('export-csv');
        Route::get('/{sponsoredAd}/detailed-report', [SponsoredAdAnalyticsController::class, 'detailedReport'])->name('detailed-report');
    });

    // Impersonation routes
    Route::get('/impersonate/{user}', function(\App\Models\User $user) {
        if (auth()->guard('admin')->check()) {
            auth()->guard('admin')->user()->impersonate($user);
            return redirect('/dashboard')->with('success', 'You are now logged in as ' . $user->name);
        }
        return redirect()->back()->with('error', 'Unauthorized');
    })->name('admin.impersonate');
});

// Leave impersonation route (accessible from user side)
Route::get('/leave-impersonate', function() {
    if (auth()->user() && auth()->user()->isImpersonated()) {
        auth()->user()->leaveImpersonation();
        return redirect('/admin/dashboard')->with('success', 'You have returned to admin account');
    }
    return redirect('/dashboard');
})->name('admin.leave-impersonate');

