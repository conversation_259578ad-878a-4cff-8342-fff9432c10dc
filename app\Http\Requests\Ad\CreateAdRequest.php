<?php

namespace App\Http\Requests\Ad;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CleanHtmlRule;
use App\Models\Category;
use App\Models\CategoryAttribute;
use Illuminate\Support\Facades\Log;

class CreateAdRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'min:10', new CleanHtmlRule()],
            'category' => ['required', 'exists:categories,slug'],
            'subcategory' => ['nullable', 'exists:categories,slug'],
            'price' => ['required', 'numeric', 'min:0'],

            'is_negotiable' => ['sometimes', 'boolean'],

            'images' => ['required', 'array', 'min:1', 'max:7'], // تحديد العدد إلى 7
            'images.*' => [
                'required',
                'image',
                'max:10240', // 10MB max - أكثر مرونة
                'mimes:jpg,jpeg,png,webp,gif,bmp', // إضافة gif و bmp
                // إزالة distinct و dimensions للمرونة
            ],

            'terms' => ['required', 'accepted'],

            'needs_brokering' => ['sometimes', 'boolean'],
            'broker_commission' => ['nullable', 'numeric', 'min:0'],

            'is_sponsored' => ['sometimes', 'boolean'],
            'sponsorship_cost' => ['nullable', 'numeric', 'min:10'],
        ];

        // إضافة validation مشروط للحقول المتقدمة
        if ($this->has('needs_brokering') && $this->input('needs_brokering')) {
            $price = $this->input('price', 0);
            $maxCommission = $price * 0.25; // 25% من السعر

            // التأكد من أن الحد الأقصى لا يقل عن 1
            $maxCommission = max($maxCommission, 1);

            $rules['broker_commission'] = [
                'required',
                'numeric',
                'min:1',
                'max:' . $maxCommission
            ];
        }

        if ($this->has('is_sponsored') && $this->input('is_sponsored')) {
            $rules['sponsorship_cost'] = ['required', 'numeric', 'min:10'];
        }

        // إضافة validation للخصائص الديناميكية
        $this->addAttributeValidation($rules);

        return $rules;
    }

    /**
     * Add validation rules for category attributes.
     */
    private function addAttributeValidation(array &$rules): void
    {
        $subcategorySlug = $this->input('subcategory');

        if (!$subcategorySlug) {
            return;
        }

        try {
            $category = Category::where('slug', $subcategorySlug)->first();

            if (!$category) {
                return;
            }

            $attributes = $category->attributes;

            foreach ($attributes as $attribute) {
                $fieldName = $attribute->attribute_name;

                $fieldRules = [];

                if ($attribute->is_required) {
                    $fieldRules[] = 'required';
                } else {
                    $fieldRules[] = 'nullable';
                }

                if ($attribute->attribute_type === 'select') {
                    // Validate that the value is one of the allowed options
                    if ($attribute->attribute_options && is_array($attribute->attribute_options)) {
                        $allowedValues = array_keys($attribute->attribute_options);
                        $fieldRules[] = 'in:' . implode(',', $allowedValues);
                    }
                } else {
                    $fieldRules[] = 'string';
                    $fieldRules[] = 'max:255';
                }

                $rules[$fieldName] = $fieldRules;
            }
        } catch (\Exception $e) {
            // Log error but don't break validation
            Log::error('Error adding attribute validation: ' . $e->getMessage());
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        $price = $this->input('price', 0);
        $maxCommission = number_format($price * 0.25);

        return [
            'images.required' => 'At least one image is required for your listing.',
            'images.min' => 'You must upload at least one image.',
            'images.max' => 'You can upload a maximum of 7 images.',
            'images.*.required' => 'Each image file is required.',
            'images.*.image' => 'Only image files are allowed.',
            'images.*.max' => 'Each image must be smaller than 10MB.',
            'images.*.mimes' => 'Images must be in JPG, JPEG, PNG, WebP, GIF, or BMP format.',

            'broker_commission.required' => 'Marketing commission is required when Marketing Support is enabled.',
            'broker_commission.min' => 'Marketing commission must be at least 1 EGP.',
            'broker_commission.max' => "Marketing commission cannot exceed 25% of the item price ({$maxCommission} EGP maximum). Consider reducing the commission or increasing the item price.",
            'sponsorship_cost.required' => 'Sponsorship budget is required when Sponsored Listing is enabled.',
            'sponsorship_cost.min' => 'Sponsorship budget must be at least 10 EGP.',
        ];
    }
}
