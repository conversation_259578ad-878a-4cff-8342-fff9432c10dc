@extends('partials.admin')
@section('title', 'إدارة اهتمامات المندوبين')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'broker-management'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'إدارة اهتمامات المندوبين'])

            <!-- إحصائيات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">إجمالي الاهتمامات</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_interests']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-heart fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">الاهتمامات النشطة</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['active_interests']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-check-circle fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">تم إدخال الكود</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['code_entered']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-key fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">إجمالي النقاط المدفوعة</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_points_paid']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-coins fa-2x text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">فلترة الاهتمامات</h3>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">الحالة</label>
                                    <select name="status" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>نشط</option>
                                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                        <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>منتهي الصلاحية</option>
                                        <option value="ad_deleted" {{ request('status') == 'ad_deleted' ? 'selected' : '' }}>الإعلان محذوف</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">المندوب</label>
                                    <select name="broker_id" class="form-select">
                                        <option value="">جميع المندوبين</option>
                                        @foreach($brokers as $broker)
                                            <option value="{{ $broker->id }}" {{ request('broker_id') == $broker->id ? 'selected' : '' }}>
                                                {{ $broker->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">البحث في الإعلانات</label>
                                    <input type="text" name="ad_search" class="form-control" 
                                           placeholder="عنوان الإعلان..." value="{{ request('ad_search') }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إدخال الكود</label>
                                    <select name="code_entered" class="form-select">
                                        <option value="">الكل</option>
                                        <option value="1" {{ request('code_entered') == '1' ? 'selected' : '' }}>تم إدخال الكود</option>
                                        <option value="0" {{ request('code_entered') == '0' ? 'selected' : '' }}>لم يتم إدخال الكود</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="{{ route('admin.broker-management.interests') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الاهتمامات -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">اهتمامات المندوبين ({{ $interests->total() }} اهتمام)</h3>
                        </div>
                        <div class="card-body">
                            @if($interests->count())
                                <div class="table-responsive">
                                    <table class="table table-bordered text-nowrap border-bottom">
                                        <thead>
                                            <tr>
                                                <th>المندوب</th>
                                                <th>الإعلان</th>
                                                <th>النقاط المدفوعة</th>
                                                <th>النقاط المكتسبة</th>
                                                <th>الحالة</th>
                                                <th>إدخال الكود</th>
                                                <th>تاريخ الإنشاء</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($interests as $interest)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="flex-grow-1">
                                                                <strong>{{ $interest->broker->name }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $interest->broker->email }}</small>
                                                                <br>
                                                                <small class="text-info">{{ number_format($interest->broker->points) }} نقطة</small>
                                                            </div>
                                                            <div class="ms-2">
                                                                <button type="button" class="btn btn-sm btn-warning"
                                                                        data-bs-toggle="modal" data-bs-target="#editPointsModal{{ $interest->broker->id }}"
                                                                        title="تعديل النقاط">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            @if($interest->ad)
                                                                <strong>{{ \Illuminate\Support\Str::limit($interest->ad->title, 30) }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $interest->ad->category->name ?? 'غير محدد' }}</small>
                                                                <br>
                                                                <small class="text-info">{{ number_format($interest->ad->price) }} جنيه</small>
                                                            @else
                                                                <strong class="text-danger">إعلان محذوف</strong>
                                                                <br>
                                                                <small class="text-muted">الإعلان غير متاح</small>
                                                                <br>
                                                                <small class="text-muted">-</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-danger">{{ number_format($interest->points_paid) }} نقطة</span>
                                                    </td>
                                                    <td>
                                                        @if($interest->points_earned)
                                                            <span class="badge bg-success">{{ number_format($interest->points_earned) }} نقطة</span>
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @switch($interest->status)
                                                            @case('active')
                                                                <span class="badge bg-success">نشط</span>
                                                                @break
                                                            @case('cancelled')
                                                                <span class="badge bg-warning">ملغي</span>
                                                                @break
                                                            @case('expired')
                                                                <span class="badge bg-danger">منتهي الصلاحية</span>
                                                                @break
                                                            @case('ad_deleted')
                                                                <span class="badge bg-secondary">الإعلان محذوف</span>
                                                                @break
                                                            @default
                                                                <span class="badge bg-light text-dark">{{ $interest->status }}</span>
                                                        @endswitch
                                                    </td>
                                                    <td>
                                                        @if($interest->code_entered)
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check"></i> تم الإدخال
                                                            </span>
                                                            <br>
                                                            <small class="text-muted">{{ $interest->code_entered_at?->diffForHumans() }}</small>
                                                        @else
                                                            <span class="badge bg-secondary">لم يتم الإدخال</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        {{ $interest->created_at->format('Y-m-d H:i') }}
                                                        <br>
                                                        <small class="text-muted">{{ $interest->created_at->diffForHumans() }}</small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            @if($interest->ad)
                                                                <a href="{{ route('admin.ads.show', $interest->ad->slug) }}"
                                                                   class="btn btn-sm btn-info" target="_blank" title="عرض الإعلان">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            @else
                                                                <span class="btn btn-sm btn-secondary disabled" title="الإعلان محذوف">
                                                                    <i class="fas fa-eye-slash"></i>
                                                                </span>
                                                            @endif
                                                            <form method="POST"
                                                                  action="{{ route('admin.broker-management.delete-interest', $interest) }}"
                                                                  class="d-inline"
                                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا الاهتمام؟ سيتم إرجاع النقاط للمندوب.')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-danger" title="حذف الاهتمام">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- التصفح -->
                                <div class="mt-3">
                                    {{ $interests->withQueryString()->links() }}
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد اهتمامات</h5>
                                    <p class="text-muted">لم يتم العثور على أي اهتمامات تطابق معايير البحث</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals لتعديل النقاط -->
@foreach($interests->unique('broker_id') as $interest)
    <div class="modal fade" id="editPointsModal{{ $interest->broker->id }}" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="{{ route('admin.broker-management.update-points', $interest->broker) }}">
                    @csrf
                    @method('PUT')
                    <div class="modal-header">
                        <h5 class="modal-title">تعديل نقاط {{ $interest->broker->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">النقاط الحالية</label>
                            <input type="text" class="form-control" value="{{ number_format($interest->broker->points) }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">النقاط الجديدة <span class="text-danger">*</span></label>
                            <input type="number" name="points" class="form-control"
                                   value="{{ $interest->broker->points }}" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سبب التعديل <span class="text-danger">*</span></label>
                            <textarea name="reason" class="form-control" rows="3"
                                      placeholder="اذكر سبب تعديل النقاط..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التعديل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach

@endsection
