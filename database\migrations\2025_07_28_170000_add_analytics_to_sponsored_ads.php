<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sponsored_ads', function (Blueprint $table) {
            $table->unsignedInteger('views_count')->default(0)->after('expires_at');
            $table->unsignedInteger('clicks_count')->default(0)->after('views_count');
            $table->decimal('ctr', 5, 2)->default(0.00)->after('clicks_count')->comment('Click Through Rate percentage');
            $table->index(['status', 'is_active', 'expires_at']);
            $table->index(['views_count', 'clicks_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sponsored_ads', function (Blueprint $table) {
            $table->dropIndex(['status', 'is_active', 'expires_at']);
            $table->dropIndex(['views_count', 'clicks_count']);
            $table->dropColumn(['views_count', 'clicks_count', 'ctr']);
        });
    }
};
