<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserBlock extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'blocker_id',
        'blocked_id'
    ];

    /**
     * Get the user who is blocking.
     */
    public function blocker(): BelongsTo
    {
        return $this->belongsTo(User::class, 'blocker_id');
    }

    /**
     * Get the user who is blocked.
     */
    public function blocked(): BelongsTo
    {
        return $this->belongsTo(User::class, 'blocked_id');
    }

    /**
     * Check if user A has blocked user B.
     */
    public static function isBlocked(string $blockerId, string $blockedId): bool
    {
        return self::where('blocker_id', $blockerId)
                   ->where('blocked_id', $blockedId)
                   ->exists();
    }

    /**
     * Check if there's any block between two users.
     */
    public static function hasBlockBetween(string $userId1, string $userId2): bool
    {
        return self::where(function($query) use ($userId1, $userId2) {
            $query->where('blocker_id', $userId1)->where('blocked_id', $userId2);
        })->orWhere(function($query) use ($userId1, $userId2) {
            $query->where('blocker_id', $userId2)->where('blocked_id', $userId1);
        })->exists();
    }
}
