<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;

/**
 * Advanced Image Optimizer with multiple compression techniques
 * Designed to achieve maximum file size reduction while maintaining acceptable quality
 */
class AdvancedImageOptimizer
{
    /**
     * Ultra-aggressive compression for maximum size reduction
     */
    public function ultraCompress(UploadedFile $file, array $options = []): array
    {
        $defaultOptions = [
            'target_size_kb' => 100, // Target 100KB or less
            'min_quality' => 25,     // Minimum acceptable quality
            'max_width' => 800,      // Maximum width
            'max_height' => 800,     // Maximum height
            'format' => 'webp',      // Preferred format
            'progressive' => true,
            'strip_all_metadata' => true,
            'color_reduction' => true,
            'smart_crop' => false
        ];

        $options = array_merge($defaultOptions, $options);
        $targetSizeBytes = $options['target_size_kb'] * 1024;

        try {
            // Load and analyze image
            $image = Image::make($file->getRealPath());
            $originalWidth = $image->width();
            $originalHeight = $image->height();
            $originalSize = $file->getSize();

            Log::info('Starting ultra compression', [
                'original_size' => round($originalSize / 1024, 2) . 'KB',
                'target_size' => $options['target_size_kb'] . 'KB',
                'dimensions' => $originalWidth . 'x' . $originalHeight
            ]);

            // Step 1: Aggressive resizing
            $this->aggressiveResize($image, $options);

            // Step 2: Color optimization
            if ($options['color_reduction']) {
                $this->optimizeColors($image);
            }

            // Step 3: Apply compression filters
            $this->applyCompressionFilters($image);

            // Step 4: Iterative quality reduction until target size is reached
            $compressedData = $this->iterativeCompress($image, $options, $targetSizeBytes);

            return [
                'success' => true,
                'compressed_data' => $compressedData,
                'original_size' => $originalSize,
                'compressed_size' => strlen($compressedData),
                'compression_ratio' => round((($originalSize - strlen($compressedData)) / $originalSize) * 100, 2),
                'final_dimensions' => [
                    'width' => $image->width(),
                    'height' => $image->height()
                ],
                'techniques_applied' => [
                    'aggressive_resize',
                    'color_optimization',
                    'compression_filters',
                    'iterative_quality_reduction'
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Ultra compression failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Aggressive resizing with smart aspect ratio preservation
     */
    private function aggressiveResize($image, array $options): void
    {
        $currentWidth = $image->width();
        $currentHeight = $image->height();
        $maxWidth = $options['max_width'];
        $maxHeight = $options['max_height'];

        // Calculate aggressive resize factor
        $widthRatio = $maxWidth / $currentWidth;
        $heightRatio = $maxHeight / $currentHeight;
        $resizeRatio = min($widthRatio, $heightRatio, 0.7); // Max 70% of original

        if ($resizeRatio < 1) {
            $newWidth = (int)($currentWidth * $resizeRatio);
            $newHeight = (int)($currentHeight * $resizeRatio);

            $image->resize($newWidth, $newHeight, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }
    }

    /**
     * Optimize colors for better compression
     */
    private function optimizeColors($image): void
    {
        // Reduce color palette
        $image->limitColors(128, '#ffffff');
        
        // Apply slight desaturation for better compression
        $image->colorize(0, 0, 0, -10);
    }

    /**
     * Apply filters that improve compression
     */
    private function applyCompressionFilters($image): void
    {
        // Slight blur to reduce noise and improve compression
        $image->blur(0.5);
        
        // Sharpen to compensate for blur
        $image->sharpen(5);
        
        // Reduce contrast slightly
        $image->contrast(-5);
    }

    /**
     * Iteratively reduce quality until target size is achieved
     */
    private function iterativeCompress($image, array $options, int $targetSizeBytes): string
    {
        $format = $options['format'];
        $currentQuality = 80;
        $minQuality = $options['min_quality'];
        $attempts = 0;
        $maxAttempts = 10;

        while ($attempts < $maxAttempts && $currentQuality >= $minQuality) {
            try {
                if ($format === 'webp') {
                    $compressedData = $image->encode('webp', $currentQuality);
                } else {
                    $compressedData = $image->encode('jpg', $currentQuality);
                }

                $currentSize = strlen($compressedData);

                Log::debug('Compression attempt', [
                    'attempt' => $attempts + 1,
                    'quality' => $currentQuality,
                    'size' => round($currentSize / 1024, 2) . 'KB',
                    'target' => round($targetSizeBytes / 1024, 2) . 'KB'
                ]);

                // If we've reached the target size, return
                if ($currentSize <= $targetSizeBytes) {
                    return $compressedData;
                }

                // Reduce quality for next attempt
                $currentQuality -= 8;
                $attempts++;

            } catch (\Exception $e) {
                Log::warning('Compression attempt failed', [
                    'quality' => $currentQuality,
                    'error' => $e->getMessage()
                ]);
                $currentQuality -= 10;
                $attempts++;
            }
        }

        // If we couldn't reach target size, try extreme measures
        return $this->extremeCompress($image, $format, $minQuality);
    }

    /**
     * Extreme compression as last resort
     */
    private function extremeCompress($image, string $format, int $minQuality): string
    {
        // Further reduce dimensions
        $image->resize(
            (int)($image->width() * 0.8),
            (int)($image->height() * 0.8),
            function ($constraint) {
                $constraint->aspectRatio();
            }
        );

        // Apply more aggressive filters
        $image->blur(1);
        $image->limitColors(64);

        // Encode with minimum quality
        if ($format === 'webp') {
            return $image->encode('webp', $minQuality);
        } else {
            return $image->encode('jpg', $minQuality);
        }
    }

    /**
     * Create optimized thumbnails for different screen sizes
     */
    public function createResponsiveImages(UploadedFile $file, string $baseDirectory): array
    {
        $sizes = [
            'xs' => ['width' => 320, 'quality' => 60, 'target_kb' => 30],
            'sm' => ['width' => 640, 'quality' => 65, 'target_kb' => 80],
            'md' => ['width' => 1024, 'quality' => 70, 'target_kb' => 150],
            'lg' => ['width' => 1440, 'quality' => 75, 'target_kb' => 250],
            'xl' => ['width' => 1920, 'quality' => 80, 'target_kb' => 400]
        ];

        $results = [];

        foreach ($sizes as $sizeName => $config) {
            $options = [
                'target_size_kb' => $config['target_kb'],
                'max_width' => $config['width'],
                'max_height' => (int)($config['width'] * 0.75), // 4:3 aspect ratio
                'format' => 'webp'
            ];

            $result = $this->ultraCompress($file, $options);
            
            if ($result['success']) {
                $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $path = $baseDirectory . '/' . $filename . '_' . $sizeName . '.webp';
                
                Storage::disk('public')->put($path, $result['compressed_data']);
                
                $results[$sizeName] = [
                    'path' => $path,
                    'url' => Storage::disk('public')->url($path),
                    'size' => $result['compressed_size'],
                    'dimensions' => $result['final_dimensions']
                ];
            }
        }

        return $results;
    }

    /**
     * Analyze image and suggest optimal compression settings
     */
    public function analyzeAndSuggest(UploadedFile $file): array
    {
        try {
            $image = Image::make($file->getRealPath());
            $width = $image->width();
            $height = $image->height();
            $fileSize = $file->getSize();
            $aspectRatio = $width / $height;

            // Analyze image characteristics
            $isHighRes = $width > 2000 || $height > 2000;
            $isLargeFile = $fileSize > 5 * 1024 * 1024; // 5MB
            $isWideAspect = $aspectRatio > 2 || $aspectRatio < 0.5;

            // Generate recommendations
            $recommendations = [];

            if ($isHighRes) {
                $recommendations[] = 'High resolution detected - aggressive resizing recommended';
            }

            if ($isLargeFile) {
                $recommendations[] = 'Large file size - ultra compression recommended';
            }

            if ($isWideAspect) {
                $recommendations[] = 'Unusual aspect ratio - smart cropping may help';
            }

            // Suggest optimal settings
            $suggestedSettings = [
                'target_size_kb' => $isLargeFile ? 150 : 100,
                'max_width' => $isHighRes ? 1200 : 800,
                'max_height' => $isHighRes ? 1200 : 800,
                'format' => 'webp',
                'quality_range' => $isHighRes ? '40-60' : '50-70'
            ];

            return [
                'analysis' => [
                    'dimensions' => $width . 'x' . $height,
                    'file_size' => round($fileSize / 1024 / 1024, 2) . 'MB',
                    'aspect_ratio' => round($aspectRatio, 2),
                    'is_high_resolution' => $isHighRes,
                    'is_large_file' => $isLargeFile
                ],
                'recommendations' => $recommendations,
                'suggested_settings' => $suggestedSettings
            ];

        } catch (\Exception $e) {
            return [
                'error' => 'Unable to analyze image: ' . $e->getMessage()
            ];
        }
    }
}
