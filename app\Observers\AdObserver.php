<?php

namespace App\Observers;

use App\Models\Ad;
use App\Models\User;
use App\Models\BrokerInterest;
use App\Enums\AdStatus;
use App\Jobs\RefundBrokerInterestsJob;
use App\Notifications\Ad\AdCreatedNotification;
use App\Notifications\Ad\AdStatusUpdatedNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class AdObserver
{
    /**
     * Handle the Ad "created" event.
     */
    public function created(Ad $ad): void
    {
        // if($ad->user->exists) {
        //     $ad->user?->notify(new AdCreatedNotification($ad));
        // } else {
        //     Notification::route('mail', $ad->seller_email)->notify(new AdCreatedNotification($ad));
        // }
    }

    /**
     * Handle the Ad "updated" event.
     */
    public function updated(Ad $ad): void
    {
        // تحديث عدد الإعلانات عند تغيير الحالة
        if ($ad->isDirty('status')) {
            $oldStatus = $ad->getOriginal('status');
            $newStatus = $ad->status;

            $user = User::where('email', $ad->seller_email)->first();

            if ($user) {
                // إذا تم تغيير الحالة من غير منشور إلى منشور
                if ($oldStatus !== AdStatus::PUBLISHED && $newStatus === AdStatus::PUBLISHED) {
                    $user->increment('Number_Ads');
                }

                // إذا تم تغيير الحالة من منشور إلى غير منشور
                if ($oldStatus === AdStatus::PUBLISHED && $newStatus !== AdStatus::PUBLISHED) {
                    if ($user->Number_Ads > 0) {
                        $user->decrement('Number_Ads');
                    }
                }
            }
        }

        // Log::info('Updated');
        // if($ad->isDirty('status') && $ad->user->exists) {
        //     $ad->user?->notify(new AdStatusUpdatedNotification($ad));
        // } elseif($ad->isDirty('status') && !$ad->user->exists) {
        //     Notification::route('mail', $ad->seller_email)->notify(new AdStatusUpdatedNotification($ad));
        // }
    }

    /**
     * Handle the Ad "deleting" event.
     * يتم استدعاؤها قبل حذف الإعلان
     */
    public function deleting(Ad $ad): void
    {
        Log::info("بدء معالجة حذف الإعلان: {$ad->title} (ID: {$ad->id})");

        // جلب جميع الاهتمامات النشطة قبل حذف الإعلان
        $activeInterests = BrokerInterest::where('ad_id', $ad->id)
                                        ->where('status', 'active')
                                        ->where('code_entered', false)
                                        ->get();

        Log::info("تم العثور على " . $activeInterests->count() . " اهتمام نشط للإعلان");

        // معالجة كل اهتمام فوراً قبل الحذف
        foreach ($activeInterests as $interest) {
            try {
                $daysSinceInterest = $interest->created_at->diffInDays(now());

                // حساب النقاط المسترجعة حسب التوقيت
                if ($daysSinceInterest < 7) {
                    // أقل من 7 أيام: خصم 10%
                    $refundPoints = (int) floor($interest->points_paid * 0.9);
                    $deductionPercent = 10;
                } else {
                    // 7 أيام أو أكثر: خصم 20%
                    $refundPoints = (int) floor($interest->points_paid * 0.8);
                    $deductionPercent = 20;
                }

                Log::info("معالجة الاهتمام {$interest->id}: {$daysSinceInterest} أيام، خصم {$deductionPercent}%، استرداد {$refundPoints} نقطة");

                // إضافة النقاط للمندوب
                $interest->broker->increment('points', $refundPoints);

                // تحديث حالة الاهتمام
                $interest->update([
                    'status' => 'ad_deleted',
                    'points_earned' => $refundPoints,
                ]);

                Log::info("تم استرداد {$refundPoints} نقطة للمندوب {$interest->broker->name} (خصم {$deductionPercent}%) بسبب حذف الإعلان: {$ad->title}");

            } catch (\Exception $e) {
                Log::error("خطأ في استرداد النقاط للاهتمام {$interest->id}: " . $e->getMessage());
            }
        }

        Log::info("تم الانتهاء من معالجة " . $activeInterests->count() . " اهتمام للإعلان المحذوف: {$ad->title} (ID: {$ad->id})");
    }

    /**
     * Handle the Ad "deleted" event.
     */
    public function deleted(Ad $ad): void
    {
        // تقليل العدد إذا كان الإعلان منشوراً
        if ($ad->status === AdStatus::PUBLISHED) {
            $user = User::where('email', $ad->seller_email)->first();
            if ($user && $user->Number_Ads > 0) {
                $user->decrement('Number_Ads');
            }
        }

        // حذف جميع الصور المرتبطة بالإعلان
        $ad->media()->each(function ($media) {
            try {
                // حذف الملف من التخزين
                if (file_exists(storage_path('app/' . $media->path))) {
                    unlink(storage_path('app/' . $media->path));
                }

                // حذف سجل الميديا من قاعدة البيانات
                $media->delete();

                Log::info("تم حذف صورة الإعلان: {$media->path}");
            } catch (\Exception $e) {
                Log::error("خطأ في حذف صورة الإعلان {$media->path}: " . $e->getMessage());
            }
        });

        Log::info("تم حذف الإعلان وجميع صوره: {$ad->title} (ID: {$ad->id})");
    }

    /**
     * Handle the Ad "restored" event.
     */
    public function restored(Ad $ad): void
    {
        // زيادة العدد إذا كان الإعلان منشوراً عند الاستعادة
        if ($ad->status === AdStatus::PUBLISHED) {
            $user = User::where('email', $ad->seller_email)->first();
            if ($user) {
                $user->increment('Number_Ads');
            }
        }
    }

    /**
     * Handle the Ad "force deleted" event.
     */
    public function forceDeleted(Ad $ad): void
    {
        // تقليل العدد إذا كان الإعلان منشوراً
        if ($ad->status === AdStatus::PUBLISHED) {
            $user = User::where('email', $ad->seller_email)->first();
            if ($user && $user->Number_Ads > 0) {
                $user->decrement('Number_Ads');
            }
        }
    }
}
