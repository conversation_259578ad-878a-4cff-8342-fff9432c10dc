<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BrokerInterest extends Model
{
    use HasFactory;

    protected $fillable = [
        'broker_id',
        'ad_id',
        'points_paid',
        'points_earned',
        'code_entered',
        'code_entered_at',
        'code_used_for_ad',
        'status',
        'withdrawal_requested_at',
        'expires_at',
    ];

    // حالات الاهتمام
    const STATUS_ACTIVE = 'active';
    const STATUS_EXPIRED = 'expired';
    const STATUS_REFUNDED = 'refunded';
    const STATUS_WITHDRAWN = 'withdrawn';
    const STATUS_AD_DELETED = 'ad_deleted';

    protected $casts = [
        'code_entered' => 'boolean',
        'code_used_for_ad' => 'boolean',
        'code_entered_at' => 'datetime',
        'withdrawal_requested_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * العلاقة مع المندوب
     */
    public function broker(): BelongsTo
    {
        return $this->belongsTo(User::class, 'broker_id');
    }

    /**
     * العلاقة مع الإعلان
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * حساب النقاط المطلوبة للاهتمام بناءً على سعر الإعلان
     */
    public static function calculateRequiredPoints(float $adPrice): int
    {
        // 0.1% من سعر الإعلان
        return (int) ceil($adPrice * 0.001);
    }

    /**
     * حساب النقاط المكتسبة عند إدخال الكود بناءً على التوقيت
     */
    public function calculateEarnedPoints(): int
    {
        $daysSinceInterest = $this->created_at->diffInDays(now());

        if ($daysSinceInterest <= 5) {
            // أول 5 أيام: إرجاع كامل 100%
            return $this->points_paid;
        } elseif ($daysSinceInterest <= 10) {
            // من 6 إلى 10 أيام: إرجاع مخصوم منه 10%
            return (int) floor($this->points_paid * 0.9);
        } else {
            // أكثر من 10 أيام: انتهت صلاحية الاهتمام
            return 0;
        }
    }

    /**
     * حساب النقاط المسترجعة عند حذف الإعلان
     */
    public function calculateRefundPoints(): int
    {
        $daysSinceInterest = $this->created_at->diffInDays(now());

        if ($daysSinceInterest < 5) {
            // أقل من 5 أيام: خصم 10%
            return (int) floor($this->points_paid * 0.9);
        } else {
            // 5 أيام أو أكثر: خصم 20%
            return (int) floor($this->points_paid * 0.8);
        }
    }



    /**
     * حساب النقاط المسترجعة عند إلغاء الاهتمام
     */
    public function calculateCancelRefundPoints(): int
    {
        $daysSinceInterest = $this->created_at->diffInDays(now());

        if ($daysSinceInterest < 7) {
            // أقل من 7 أيام: خصم 10% (استرداد 90%)
            return (int) floor($this->points_paid * 0.9);
        } else {
            // 7 أيام أو أكثر: خصم 20% (استرداد 80%)
            return (int) floor($this->points_paid * 0.8);
        }
    }

    /**
     * إدخال الكود وكسب النقاط
     */
    public function enterCode(): void
    {
        if (!$this->code_entered && $this->status === 'active') {
            $earnedPoints = $this->calculateEarnedPoints();

            // التحقق من انتهاء الصلاحية (أكثر من 10 أيام)
            if ($earnedPoints === 0) {
                $this->update(['status' => 'expired']);
                return;
            }

            $this->update([
                'code_entered' => true,
                'code_entered_at' => now(),
                'points_earned' => $earnedPoints,
            ]);

            // إضافة النقاط للمندوب
            $this->broker->increment('points', $earnedPoints);
        }
    }

    /**
     * استرداد النقاط عند حذف الإعلان
     */
    public function refundPointsForDeletedAd(): void
    {
        if ($this->status === 'active' && !$this->code_entered) {
            $refundPoints = $this->calculateRefundPoints();

            $this->update([
                'status' => 'ad_deleted',
                'points_earned' => $refundPoints,
            ]);

            // إضافة النقاط المسترجعة للمندوب
            $this->broker->increment('points', $refundPoints);

            Log::info("تم استرداد {$refundPoints} نقطة للمندوب {$this->broker->name} بسبب حذف الإعلان {$this->ad->title}");
        }
    }

    /**
     * Scope للاهتمامات المنتهية الصلاحية (أكثر من 10 أيام)
     */
    public function scopeExpired($query)
    {
        return $query->where('created_at', '<', now()->subDays(10))
                    ->where('status', 'active')
                    ->where('code_entered', false);
    }

    /**
     * التحقق من انتهاء صلاحية الاهتمام
     */
    public function isExpired(): bool
    {
        return $this->created_at->diffInDays(now()) > 10 &&
               $this->status === 'active' &&
               !$this->code_entered;
    }

    /**
     * التحقق من إذا كان الكود تم استخدامه لهذا الإعلان من قبل أي مندوب
     */
    public static function isCodeUsedForAd($adId): bool
    {
        return self::where('ad_id', $adId)
                   ->where('code_used_for_ad', true)
                   ->exists();
    }

    /**
     * طلب الانسحاب
     */
    public function requestWithdrawal(): array
    {
        if ($this->status !== 'active') {
            return ['success' => false, 'message' => 'لا يمكن الانسحاب من هذا الاهتمام'];
        }

        $daysFromInterest = now()->diffInDays($this->created_at);
        $pointsToReturn = $this->points_paid;

        if ($daysFromInterest > 7 && $daysFromInterest <= 10) {
            // خصم 20% إذا كان الانسحاب بعد 7 أيام
            $pointsToReturn = (int) floor($this->points_paid * 0.8);
        } elseif ($daysFromInterest > 10) {
            // لا يمكن الانسحاب بعد 10 أيام
            return ['success' => false, 'message' => 'انتهت فترة الانسحاب (10 أيام)'];
        }

        $this->update([
            'status' => 'withdrawn',
            'withdrawal_requested_at' => now(),
        ]);

        // إرجاع النقاط للمندوب
        $this->broker->increment('points', $pointsToReturn);

        return [
            'success' => true,
            'points_returned' => $pointsToReturn,
            'penalty' => $this->points_paid - $pointsToReturn
        ];
    }



    /**
     * انتهاء الاهتمام تلقائياً
     */
    public function expire(): void
    {
        if ($this->status === 'active') {
            $this->update(['status' => 'expired']);
        }
    }

    /**
     * Scope للاهتمامات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }



    /**
     * الحصول على الأيام المتبقية للانسحاب
     */
    public function getDaysLeftForWithdrawal(): int
    {
        $daysFromInterest = now()->diffInDays($this->created_at);
        return max(0, 10 - $daysFromInterest);
    }

    /**
     * الحصول على نسبة الخصم عند الانسحاب
     */
    public function getWithdrawalPenalty(): int
    {
        $daysFromInterest = now()->diffInDays($this->created_at);

        if ($daysFromInterest <= 7) {
            return 0; // لا يوجد خصم
        } elseif ($daysFromInterest <= 10) {
            return 20; // خصم 20%
        } else {
            return 100; // لا يمكن الانسحاب
        }
    }
}
