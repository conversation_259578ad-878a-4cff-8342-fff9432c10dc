<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('broker_interests', function (Blueprint $table) {
            // حذف الـ Foreign Key الحالي
            $table->dropForeign(['ad_id']);

            // تعديل العمود ليقبل null
            $table->uuid('ad_id')->nullable()->change();

            // إضافة Foreign Key جديد مع set null
            $table->foreign('ad_id')->references('id')->on('ads')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('broker_interests', function (Blueprint $table) {
            // حذف الـ Foreign Key
            $table->dropForeign(['ad_id']);

            // إرجاع العمود لحالته الأصلية (غير nullable)
            $table->uuid('ad_id')->nullable(false)->change();

            // إرجاع الـ Foreign Key الأصلي
            $table->foreign('ad_id')->references('id')->on('ads')->onDelete('cascade');
        });
    }
};
