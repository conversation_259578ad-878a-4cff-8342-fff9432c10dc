# نظام الأمان والحماية من الثغرات (Security System)

## نظرة عامة

تم تطوير نظام أمان شامل لحماية التطبيق من الثغرات الأمنية، خاصة XSS (Cross-Site Scripting) وحقن HTML/JavaScript في حقول الإدخال.

## المشاكل التي تم حلها

### 1. مشكلة HTML Tags في الـ Description
- **المشكلة**: كانت HTML tags تُحفظ في قاعدة البيانات وتظهر للمستخدمين
- **المخاطر**: إمكانية حقن JavaScript ضار، XSS attacks
- **الحل**: تنظيف شامل لجميع المدخلات قبل الحفظ

### 2. عدم توحيد التنظيف
- **المشكلة**: بعض الأماكن تستخدم تنظيف والأخرى لا
- **الحل**: نظام موحد للتنظيف في جميع أنحاء التطبيق

## المكونات الأمنية

### 1. SecurityHelper Class
**الملف**: `app/Helpers/SecurityHelper.php`

#### Methods المتاحة:
```php
// تنظيف HTML عام
SecurityHelper::cleanHtml($content);

// تنظيف متقدم
SecurityHelper::purifyHtml($content);

// تنظيف وصف الإعلانات
SecurityHelper::cleanAdDescription($description);

// تنظيف النصوص العامة
SecurityHelper::cleanTextInput($input);

// تنظيف URLs
SecurityHelper::cleanUrl($url);

// تنظيف Emails
SecurityHelper::cleanEmail($email);

// تنظيف أرقام الهاتف
SecurityHelper::cleanPhone($phone);
```

### 2. Custom Validation Rule
**الملف**: `app/Rules/CleanHtmlRule.php`

#### الفحوصات الأمنية:
- منع HTML tags
- منع JavaScript code
- منع JavaScript events (onclick, onload, etc.)
- منع URLs خطيرة (javascript:, vbscript:, data:)
- منع عناصر خطيرة (iframe, object, embed, form)

### 3. Updated Repositories

#### AdRepository (User):
```php
// في الإنشاء
$cleaned = SecurityHelper::cleanAdDescription($data['description']);

// في التحديث
'description' => SecurityHelper::cleanAdDescription($data['description'])
```

#### AdminAdRepository:
```php
// في تحديث الأدمن
'description' => SecurityHelper::cleanAdDescription($data['description'])
```

### 4. Updated Validation Requests

#### CreateAdRequest:
```php
'description' => ['required', 'string', 'min:10', new CleanHtmlRule()]
```

#### UpdateAdRequest:
```php
'description' => ['nullable', 'string', 'min:10', new CleanHtmlRule()]
```

#### UpdateAdAdminRequest:
```php
'description' => ['nullable', 'string', 'min:10', new CleanHtmlRule()]
```

## الحماية المطبقة

### 1. Input Validation
- **Frontend**: منع إدخال HTML في النماذج
- **Backend**: فحص شامل قبل الحفظ
- **Database**: تنظيف قبل الإدراج

### 2. Output Escaping
- **Views**: استخدام `{{ }}` بدلاً من `{!! !!}`
- **Safe Display**: عرض آمن للمحتوى

### 3. Content Sanitization
- **Strip Tags**: إزالة جميع HTML tags
- **HTML Entities**: تحويل الأحرف الخاصة
- **Trim Spaces**: إزالة المسافات الزائدة

## Commands للصيانة

### تنظيف البيانات الموجودة:
```bash
# معاينة التغييرات بدون تطبيق
php artisan ads:clean-descriptions --dry-run

# تطبيق التنظيف
php artisan ads:clean-descriptions
```

## أمثلة على الاستخدام

### 1. في الـ Controllers:
```php
use App\Helpers\SecurityHelper;

// تنظيف البيانات قبل الحفظ
$cleanDescription = SecurityHelper::cleanAdDescription($request->description);
```

### 2. في الـ Views:
```blade
<!-- آمن - لا يعرض HTML -->
{{ $ad->description }}

<!-- خطير - لا تستخدم -->
{!! $ad->description !!}
```

### 3. في الـ Validation:
```php
use App\Rules\CleanHtmlRule;

'description' => ['required', new CleanHtmlRule()]
```

## الثغرات المحمية

### 1. XSS (Cross-Site Scripting):
```html
<!-- قبل الحماية (خطير) -->
<script>alert('XSS')</script>

<!-- بعد الحماية (آمن) -->
alert('XSS')
```

### 2. HTML Injection:
```html
<!-- قبل الحماية (خطير) -->
<iframe src="malicious-site.com"></iframe>

<!-- بعد الحماية (آمن) -->
iframe src="malicious-site.com"
```

### 3. JavaScript Events:
```html
<!-- قبل الحماية (خطير) -->
<img src="x" onerror="alert('XSS')">

<!-- بعد الحماية (آمن) -->
img src="x" onerror="alert('XSS')"
```

## Best Practices

### 1. للمطورين:
- **استخدم دائماً** `SecurityHelper` للتنظيف
- **تجنب** `{!! !!}` في الـ views
- **اختبر** المدخلات الخطيرة
- **راجع** الكود بانتظام

### 2. للمحتوى:
- **نظف** جميع المدخلات
- **تحقق** من صحة البيانات
- **اختبر** السيناريوهات الخطيرة

### 3. للصيانة:
- **شغل** commands التنظيف دورياً
- **راقب** logs الأمان
- **حدث** قواعد الحماية

## Testing الأمان

### 1. اختبار XSS:
```php
// محاولة حقن JavaScript
$maliciousInput = '<script>alert("XSS")</script>';
$cleaned = SecurityHelper::cleanAdDescription($maliciousInput);
// النتيجة: alert("XSS")
```

### 2. اختبار HTML Injection:
```php
// محاولة حقن HTML
$maliciousInput = '<iframe src="evil.com"></iframe>';
$cleaned = SecurityHelper::cleanAdDescription($maliciousInput);
// النتيجة: iframe src="evil.com"
```

## Monitoring والمراقبة

### 1. Logs:
- مراقبة محاولات الحقن
- تسجيل المدخلات المشبوهة
- تتبع أنماط الهجمات

### 2. Alerts:
- تنبيهات عند محاولات الحقن
- إشعارات للمشرفين
- تقارير أمنية دورية

## الصيانة الدورية

### 1. أسبوعياً:
```bash
# فحص البيانات الجديدة
php artisan ads:clean-descriptions --dry-run
```

### 2. شهرياً:
```bash
# تنظيف شامل
php artisan ads:clean-descriptions
```

### 3. عند التحديثات:
- مراجعة قواعد الأمان
- اختبار الثغرات الجديدة
- تحديث الحماية

## نظام الأمان للأدمن

### 1. **SecurityHelper للأدمن**
```php
// تنظيف محدود للأدمن مع HTML آمن
SecurityHelper::cleanAdminDescription($description);
```

#### **HTML المسموح للأدمن:**
- `<p>`, `<br>` - فقرات وفواصل أسطر
- `<strong>`, `<b>` - نص عريض
- `<em>`, `<i>` - نص مائل
- `<u>` - نص مسطر
- `<ul>`, `<ol>`, `<li>` - قوائم
- `<h1>` إلى `<h6>` - عناوين

#### **المحتوى المحظور حتى للأدمن:**
- `<script>` - JavaScript
- `<iframe>`, `<object>`, `<embed>` - محتوى خارجي
- `<form>` - نماذج
- `onclick`, `onload`, etc. - أحداث JavaScript
- `javascript:`, `vbscript:`, `data:` - URLs خطيرة

### 2. **AdminHtmlRule**
**الملف**: `app/Rules/AdminHtmlRule.php`

```php
// في UpdateAdAdminRequest
'description' => ['nullable', 'string', 'min:10', new AdminHtmlRule()]
```

### 3. **الفرق بين المستخدمين والأدمن:**

#### **المستخدمون العاديون:**
```php
// تنظيف كامل - لا HTML مسموح
SecurityHelper::cleanAdDescription($description);
// النتيجة: نص خالي من HTML
```

#### **الأدمن:**
```php
// تنظيف محدود - HTML آمن مسموح
SecurityHelper::cleanAdminDescription($description);
// النتيجة: HTML آمن مع تنسيق
```

### 4. **العرض في الـ Views:**
```blade
<!-- عرض آمن مع HTML محدود -->
{!! nl2br(strip_tags($ad->description, '<p><br><strong><b><em><i><u><ul><ol><li><h1><h2><h3><h4><h5><h6>')) !!}
```

## ملاحظات مهمة

1. **لا تثق أبداً** في مدخلات المستخدمين
2. **نظف دائماً** قبل الحفظ (حتى للأدمن)
3. **اعرض بأمان** في الـ views
4. **اختبر بانتظام** الثغرات الأمنية
5. **حدث باستمرار** قواعد الحماية
6. **الأدمن ليس استثناء** من الأمان الأساسي
