<?php

namespace App\Console\Commands;

use App\Models\SponsoredAd;
use Illuminate\Console\Command;

class ManageSponsoredAds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:manage-sponsored';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إدارة الإعلانات الممولة - تحديث الدقائق المتبقية وإنهاء الرعايات المنتهية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('بدء إدارة الإعلانات الممولة...');

        // جلب جميع الإعلانات الممولة النشطة
        $activeSponsoredAds = SponsoredAd::active()->get();

        $this->info("تم العثور على {$activeSponsoredAds->count()} إعلان ممول نشط.");

        $expiredCount = 0;
        $updatedCount = 0;

        foreach ($activeSponsoredAds as $sponsoredAd) {
            $this->info("معالجة الإعلان الممول ID: {$sponsoredAd->id} - إعلان: {$sponsoredAd->ad->title}");

            // التحقق من انتهاء الصلاحية
            if ($sponsoredAd->isExpired()) {
                $sponsoredAd->expireSponsorship();
                $expiredCount++;
                $this->info("✓ انتهت رعاية الإعلان: {$sponsoredAd->ad->title} (ID: {$sponsoredAd->ad->id})");
            } else {
                $remainingMinutes = $sponsoredAd->getRemainingMinutes();
                $updatedCount++;
                $this->info("✓ الإعلان لا يزال نشطاً - الدقائق المتبقية: {$remainingMinutes} للإعلان: {$sponsoredAd->ad->title}");
            }
        }

        // البحث عن إعلانات منتهية الصلاحية لم يتم معالجتها
        $expiredAds = SponsoredAd::active()
            ->where('expires_at', '<=', now())
            ->get();

        foreach ($expiredAds as $expiredAd) {
            $expiredAd->expireSponsorship();
            $expiredCount++;
            $this->info("✓ تم إنهاء رعاية الإعلان المنتهي: {$expiredAd->ad->title}");
        }

        $this->info("✅ تمت المعالجة بنجاح:");
        $this->info("   - إعلانات محدثة: {$updatedCount}");
        $this->info("   - إعلانات منتهية: {$expiredCount}");

        return Command::SUCCESS;
    }
}
