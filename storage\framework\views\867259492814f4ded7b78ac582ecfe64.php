<?php
    use Illuminate\Support\Facades\Storage;
?>

<?php if(count($users) > 0): ?>
<div class="table-responsive">
    <table class="table table-bordered text-nowrap mb-0">
        <thead class="border-top">
            <tr>
                <th class="bg-transparent border-bottom-0" style="width: 5%;">الاسم الكامل</th>
                <th class="bg-transparent border-bottom-0">البريد الإلكتروني</th>
                <th class="bg-transparent border-bottom-0">الهاتف</th>
                <th class="bg-transparent border-bottom-0">الرقم القومي</th>
                <th class="bg-transparent border-bottom-0">الدولة</th>
                <?php if($userType === 'broker'): ?>
                <th class="bg-transparent border-bottom-0">نوع المندوب</th>
                <?php endif; ?>
                <?php if($userType === 'vip'): ?>
                <th class="bg-transparent border-bottom-0">رتبة VIP</th>
                <?php endif; ?>
                <th class="bg-transparent border-bottom-0" style="width: 10%;">الحالة</th>
                <th class="bg-transparent border-bottom-0" style="width: 5%;">الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr class="border-bottom">
                <td>
                    <div class="d-flex">
                        <span class="avatar bradius" style="background-image: url(<?php echo e($user->avatar ? Storage::url($user->avatar) : get_random_avatar()); ?>)"></span>
                        <div class="ms-3 mt-0 mt-sm-2 d-block">
                            <h6 class="mb-0 fs-14 fw-semibold"><?php echo e($user->name); ?></h6>
                            <?php if($userType === 'regular'): ?>
                                <small class="text-muted">مستخدم عادي</small>
                            <?php elseif($userType === 'broker'): ?>
                                <small class="text-warning">مندوب</small>
                            <?php elseif($userType === 'vip'): ?>
                                <small class="text-success">VIP</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </td>
                <td><span class="mt-sm-2 d-block"><?php echo e($user->email); ?></span></td>
                <td><span class="mt-sm-2 d-block"><?php echo e($user->mobile); ?></span></td>
                <td>
                    <?php if($user->national_id): ?>
                        <span class="mt-sm-2 d-block"><?php echo e($user->national_id); ?></span>
                    <?php else: ?>
                        <span class="mt-sm-2 d-block text-muted">غير محدد</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if($user->country): ?>
                        <span class="mt-sm-2 d-block"><?php echo e($user->country->emoji); ?> <?php echo e($user->country->name); ?></span>
                    <?php else: ?>
                        <span class="mt-sm-2 d-block">لا توجد دولة</span>
                    <?php endif; ?>
                </td>
                <?php if($userType === 'broker'): ?>
                <td>
                    <span class="badge bg-warning">مندوب معتمد</span>
                </td>
                <?php endif; ?>
                <?php if($userType === 'vip'): ?>
                <td>
                    <span class="badge bg-success">رتبة <?php echo e($user->rank); ?></span>
                </td>
                <?php endif; ?>
                <td>
                    <div class="d-flex">
                        <span class="badge bg-<?php echo e($user->is_active ? 'success' : 'danger'); ?> me-2">
                            <?php echo e($user->is_active ? 'نشط' : 'غير نشط'); ?>

                        </span>
                        <?php if($user->is_trusted): ?>
                            <span class="badge bg-info">موثق</span>
                        <?php endif; ?>
                    </div>
                </td>
                <td>
                    <div class="mt-sm-1 d-block">
                        <a href="<?php echo e(route('admin.users.show', $user->id)); ?>" class="btn text-dark btn-sm"
                           data-bs-toggle="tooltip" data-bs-original-title="عرض">
                            <span class="fa-regular fa-eye fs-14"></span>
                        </a>
                        <a href="<?php echo e(route('admin.users.edit', $user->id)); ?>" class="btn text-primary btn-sm"
                           data-bs-toggle="tooltip" data-bs-original-title="تعديل">
                            <span class="fa-regular fa-edit fs-14"></span>
                        </a>
                    </div>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
    
    <!-- Pagination -->
    <div class="mt-3">
        <?php echo e($users->links('pagination.admin')); ?>

    </div>
</div>
<?php else: ?>
<div class="text-center p-4">
    <img src="<?php echo e(asset('assets/images/icons/man.svg')); ?>" class="w-25" alt="empty">
    <h4 class="mt-3">
        <?php if($userType === 'regular'): ?>
            لا توجد مستخدمون عاديون
        <?php elseif($userType === 'broker'): ?>
            لا توجد مندوبون
        <?php elseif($userType === 'vip'): ?>
            لا توجد مستخدمون VIP
        <?php else: ?>
            لا توجد مستخدمون
        <?php endif; ?>
    </h4>
</div>
<?php endif; ?>
<?php /**PATH F:\car\bazaar\bazaar-main\22222222\bazaar\resources\views/users/admin/partials/users-table.blade.php ENDPATH**/ ?>