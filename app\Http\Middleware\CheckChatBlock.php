<?php

namespace App\Http\Middleware;

use App\Models\UserBlock;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckChatBlock
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // فقط للرسائل المرسلة
        if ($request->isMethod('post') && $request->route()->getName() === 'send.message') {
            $senderId = Auth::id();
            $receiverId = $request->input('id'); // معرف المستقبل

            if ($senderId && $receiverId) {
                // فحص إذا كان المرسل محظور من قبل المستقبل
                if (UserBlock::isBlocked($receiverId, $senderId)) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'لا يمكنك إرسال رسائل لهذا المستخدم. تم حظرك من قبله.'
                        ], 403);
                    }

                    return redirect()->back()->withErrors(['blocked' => 'لا يمكنك إرسال رسائل لهذا المستخدم. تم حظرك من قبله.']);
                }

                // فحص إذا كان المرسل يحظر المستقبل (لا يمكنه إرسال رسائل لمن يحظره)
                if (UserBlock::isBlocked($senderId, $receiverId)) {
                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => 'لا يمكنك إرسال رسائل لهذا المستخدم لأنك تحظره. قم بفك الحظر أولاً.'
                        ], 403);
                    }

                    return redirect()->back()->withErrors(['blocked' => 'لا يمكنك إرسال رسائل لهذا المستخدم لأنك تحظره. قم بفك الحظر أولاً.']);
                }
            }
        }

        return $next($request);
    }
}
