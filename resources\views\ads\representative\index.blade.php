@extends('partials.app')
@section('title', 'لوحة المندوبين - إعلانات تحتاج وساطة')
@section('description', 'لوحة تحكم المندوبين لعرض الإعلانات التي تحتاج وساطة وإدارة الصفقات')
@include('layouts.breadcrumb', ['admin' => false, 'pageTitle' => 'لوحة المندوبين'])

@php
use Illuminate\Support\Facades\Storage;
@endphp

@push('styles')
<style>
.broker-ad-card {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.broker-ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.ad-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #6c757d;
}

.ad-badges {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.ad-badges .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

.ad-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.ad-title a {
    color: #333;
    text-decoration: none;
    font-weight: 600;
    line-height: 1.4;
}

.ad-title a:hover {
    color: #007bff;
}

.ad-description {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.info-card {
    transition: all 0.2s ease;
}

.info-card:hover {
    transform: scale(1.02);
}

.stat-card {
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-content p {
    margin: 0;
    opacity: 0.9;
}

.filter-section .card {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.ads-section h4 {
    color: #333;
    font-weight: 600;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
</style>
@endpush

@section('content')
<div class="representative-dashboard-section pt-120 pb-120">
    <div class="container">
        <!-- إحصائيات المندوب -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="broker-stats-section">
                    <h2 class="section-title mb-4">
                        <i class="fas fa-handshake text-primary"></i>
                        مرحباً بك في لوحة المندوبين
                    </h2>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card bg-primary text-white">
                                <div class="stat-icon">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>{{ $myInterests }}</h3>
                                    <p>إعلانات مهتم بها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-success text-white">
                                <div class="stat-icon">
                                    <i class="fas fa-handshake"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>{{ $myDeals }}</h3>
                                    <p>صفقات مكتملة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-info text-white">
                                <div class="stat-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>{{ $needs_brokering->total() }}</h3>
                                    <p>إعلانات متاحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card bg-warning text-white">
                                <div class="stat-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>{{ number_format(auth()->user()->points) }}</h3>
                                    <p>رصيد النقاط</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلترة الإعلانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="{{ route('representative') }}" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">
                                    <i class="fas fa-search me-1"></i>
                                    البحث
                                </label>
                                <input type="text" name="search" class="form-control"
                                       placeholder="ابحث في العنوان أو الوصف..."
                                       value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">
                                    <i class="fas fa-tags me-1"></i>
                                    الفئة
                                </label>
                                <select name="category" class="form-select">
                                    <option value="">جميع الفئات</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    السعر من
                                </label>
                                <input type="number" name="min_price" class="form-control"
                                       placeholder="0" value="{{ request('min_price') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    السعر إلى
                                </label>
                                <input type="number" name="max_price" class="form-control"
                                       placeholder="1000000" value="{{ request('max_price') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>
                                        بحث
                                    </button>
                                    @if(request()->hasAny(['search', 'category', 'min_price', 'max_price']))
                                        <a href="{{ route('representative') }}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-times me-1"></i>
                                            مسح الفلاتر
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعلانات -->
        @if($needs_brokering->count())
            <div class="ads-section">
                <h4 class="mb-4">
                    <i class="fas fa-bullhorn"></i>
                    الإعلانات المتاحة للوساطة ({{ $needs_brokering->total() }} إعلان)
                </h4>
                <div class="row gy-4">
                    @foreach ($needs_brokering as $ad)
                        <div class="col-lg-4 col-md-6">
                            <div class="broker-ad-card">
                                <!-- صورة الإعلان -->
                                <div class="ad-image">
                                    @if($ad->media->count() > 0)
                                        <img src="{{ Storage::url($ad->media->first()->path) }}"
                                             alt="{{ $ad->title }}" class="img-fluid">
                                    @else
                                        <div class="no-image-placeholder">
                                            <i class="fas fa-image"></i>
                                            <span>لا توجد صورة</span>
                                        </div>
                                    @endif

                                    <!-- شارات الإعلان -->
                                    <div class="ad-badges">
                                        @php
                                            $codeUsedForThisAd = \App\Models\BrokerInterest::isCodeUsedForAd($ad->id);
                                        @endphp

                                        @if($codeUsedForThisAd)
                                            <span class="badge bg-success">
                                                <i class="fas fa-lock"></i> تم استخدام الكود
                                            </span>
                                        @endif

                                        @if($ad->is_sponsored)
                                            <span class="badge bg-warning">
                                                <i class="fas fa-star"></i> ممول
                                            </span>
                                        @endif
                                        @if($ad->mark_as_urgent)
                                            <span class="badge bg-danger">
                                                <i class="fas fa-exclamation"></i> عاجل
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- محتوى الإعلان -->
                                <div class="ad-content">
                                    <h5 class="ad-title">
                                        <a href="{{ route('auction-details', $ad->slug) }}" target="_blank">
                                            {{  \Illuminate\Support\Str::limit($ad->title, 50) }}
                                        </a>
                                    </h5>

                                    <p class="ad-description">
                                        {{ \Illuminate\Support\Str::limit($ad->description, 100) }}
                                    </p>

                                    <!-- معلومات الإعلان -->
                                    <div class="ad-info">
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <div class="info-card bg-light p-2 rounded">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-tag text-primary me-2"></i>
                                                        <div>
                                                            <small class="text-muted">السعر</small>
                                                            <div class="fw-bold text-primary">{{ number_format($ad->price) }} جنيه</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="info-card bg-success bg-opacity-10 p-2 rounded">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-coins text-success me-2"></i>
                                                        <div>
                                                            <small class="text-muted">العمولة</small>
                                                            <div class="fw-bold text-success">{{ number_format($ad->broker_commission) }} جنيه</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="info-card bg-info bg-opacity-10 p-2 rounded">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-star text-warning me-2"></i>
                                                        <div>
                                                            <small class="text-muted">النقاط المطلوبة</small>
                                                            <div class="fw-bold text-info">{{ number_format($ad->price * 0.001) }} نقطة</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <hr class="my-3">

                                        <div class="additional-info">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="text-muted">
                                                    <i class="fas fa-tags me-1"></i>
                                                    {{ $ad->category->name ?? 'غير محدد' }}
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="text-muted">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    {{ $ad->country->name ?? 'غير محدد' }}
                                                    @if($ad->state) - {{ $ad->state->name }} @endif
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    {{ $ad->user->name }}
                                                    @if($ad->user->is_trusted)
                                                        <i class="fas fa-check-circle text-primary ms-1" title="مستخدم موثق"></i>
                                                    @endif
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ $ad->created_at->diffForHumans() }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- أزرار العمل -->
                                    <div class="ad-actions mt-3">
                                        @php
                                            $codeUsedForThisAd = \App\Models\BrokerInterest::isCodeUsedForAd($ad->id);
                                            $hasInterest = \App\Models\BrokerInterest::where('broker_id', auth()->id())
                                                ->where('ad_id', $ad->id)->exists();
                                            $requiredPoints = \App\Models\BrokerInterest::calculateRequiredPoints($ad->price);
                                        @endphp

                                        <div class="d-grid gap-2">
                                            <a href="{{ route('auction-details', $ad->slug) }}"
                                               class="btn btn-outline-primary" target="_blank">
                                                <i class="fas fa-eye me-1"></i> عرض التفاصيل
                                            </a>

                                            @if($codeUsedForThisAd)
                                                <div class="alert alert-success mb-0 py-2">
                                                    <i class="fas fa-lock me-1"></i>
                                                    <strong>تم استخدام الكود</strong>
                                                    <br>
                                                    <small>لا يمكن إظهار الاهتمام بهذا الإعلان</small>
                                                </div>
                                            @elseif($hasInterest)
                                                <div class="alert alert-info mb-0 py-2">
                                                    <i class="fas fa-heart me-1"></i>
                                                    <strong>أنت مهتم بهذا الإعلان</strong>
                                                    <br>
                                                    <small>يمكنك الحصول على الكود من صاحب الإعلان</small>
                                                </div>
                                            @elseif(auth()->user()->points >= $requiredPoints)
                                                <form method="POST" action="{{ route('representative.store-broker-interest') }}"
                                                      onsubmit="return confirm('هل أنت متأكد من رغبتك في إظهار الاهتمام؟\nسيتم خصم {{ number_format($requiredPoints) }} نقطة من رصيدك.\nرصيدك بعد الخصم: {{ number_format(auth()->user()->points - $requiredPoints) }} نقطة')">
                                                    @csrf
                                                    <input type="hidden" name="ad_id" value="{{ $ad->id }}">
                                                    <button type="submit" class="btn btn-success w-100">
                                                        <i class="fas fa-heart me-1"></i>
                                                        أبدي اهتمام
                                                        <br>
                                                        <small>({{ number_format($requiredPoints) }} نقطة)</small>
                                                    </button>
                                                </form>
                                            @else
                                                <div class="alert alert-warning mb-0 py-2">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    <strong>نقاط غير كافية</strong>
                                                    <br>
                                                    <small>تحتاج {{ number_format($requiredPoints - auth()->user()->points) }} نقطة إضافية</small>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- التصفح -->
                <div class="row mt-5">
                    <div class="col-12">
                        {{ $needs_brokering->withQueryString()->links() }}
                    </div>
                </div>
            </div>
        @else
            <div class="no-ads-section text-center py-5">
                <div class="no-ads-icon mb-3">
                    <i class="fas fa-search fa-3x text-muted"></i>
                </div>
                <h4 class="text-muted">لا توجد إعلانات متاحة</h4>
                <p class="text-muted">
                    @if(request()->hasAny(['search', 'category', 'country', 'min_price', 'max_price']))
                        لا توجد إعلانات تطابق معايير البحث المحددة.
                        <br>
                        <a href="{{ route('representative') }}" class="btn btn-primary mt-2">
                            <i class="fas fa-refresh"></i> عرض جميع الإعلانات
                        </a>
                    @else
                        لا توجد إعلانات تحتاج وساطة حالياً.
                    @endif
                </p>
            </div>
        @endif
    </div>
</div>

<style>
.broker-stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.stat-card {
    padding: 1.5rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.stat-icon {
    font-size: 2rem;
    margin-left: 1rem;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
}

.stat-content p {
    margin: 0;
    opacity: 0.9;
}

.filter-section {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.broker-ad-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.broker-ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.ad-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #6c757d;
}

.ad-badges {
    position: absolute;
    top: 10px;
    right: 10px;
}

.ad-badges .badge {
    margin-left: 5px;
}

.ad-content {
    padding: 1.5rem;
}

.ad-title a {
    color: #333;
    text-decoration: none;
    font-weight: 600;
}

.ad-title a:hover {
    color: #007bff;
}

.ad-description {
    color: #666;
    margin: 0.5rem 0 1rem 0;
    line-height: 1.5;
}

.ad-info {
    margin: 1rem 0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.info-row .label {
    color: #666;
    font-size: 0.9rem;
}

.info-row .value {
    font-weight: 600;
}

.value.price {
    color: #28a745;
    font-size: 1.1rem;
}

.value.commission {
    color: #ffc107;
}

.ad-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.no-ads-section {
    background: white;
    border-radius: 15px;
    margin: 2rem 0;
}
</style>
@endsection
