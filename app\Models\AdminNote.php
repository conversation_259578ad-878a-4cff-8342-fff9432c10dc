<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdminNote extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'admin_id',
        'note',
        'type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the ad that owns the note.
     */
    public function ad(): BelongsTo
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * Get the admin that created the note.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * Get formatted type name.
     */
    public function getTypeNameAttribute(): string
    {
        return match($this->type) {
            'suggestion' => 'Suggestion',
            'rejection' => 'Rejection Reason',
            'general' => 'General Note',
            default => 'Note'
        };
    }

    /**
     * Get type color for display.
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            'suggestion' => 'info',
            'rejection' => 'danger',
            'general' => 'secondary',
            default => 'secondary'
        };
    }
}
