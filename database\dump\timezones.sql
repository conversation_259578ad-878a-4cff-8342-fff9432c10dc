-- -------------------------------------------------------------
-- TablePlus 5.4.0(504)
--
-- https://tableplus.com/
--
-- Database: bazaar
-- Generation Time: 2023-08-22 20:42:27.5440
-- -------------------------------------------------------------


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


DROP TABLE IF EXISTS `timezones`;
CREATE TABLE `timezones` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `country_id` bigint unsigned NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abbr` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `offset` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `offset_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tz_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `timezones_country_id_foreign` (`country_id`),
  KEY `timezones_name_index` (`name`),
  KEY `timezones_abbr_index` (`abbr`),
  KEY `timezones_offset_index` (`offset`),
  KEY `timezones_offset_name_index` (`offset_name`),
  CONSTRAINT `timezones_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=429 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `timezones` (`id`, `country_id`, `name`, `abbr`, `offset`, `offset_name`, `tz_name`, `created_at`, `updated_at`) VALUES
(1, 1, 'Europe/Andorra', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(2, 2, 'Asia/Dubai', 'GST', '14400', 'UTC+04:00', 'Gulf Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(3, 3, 'Asia/Kabul', 'AFT', '16200', 'UTC+04:30', 'Afghanistan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(4, 4, 'America/Antigua', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(5, 5, 'America/Anguilla', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(6, 6, 'Europe/Tirane', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(7, 7, 'Asia/Yerevan', 'AMT', '14400', 'UTC+04:00', 'Armenia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(8, 8, 'Africa/Luanda', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(9, 9, 'Antarctica/Casey', 'AWST', '39600', 'UTC+11:00', 'Australian Western Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(10, 9, 'Antarctica/Davis', 'DAVT', '25200', 'UTC+07:00', 'Davis Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(11, 9, 'Antarctica/DumontDUrville', 'DDUT', '36000', 'UTC+10:00', 'Dumont d\'Urville Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(12, 9, 'Antarctica/Mawson', 'MAWT', '18000', 'UTC+05:00', 'Mawson Station Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(13, 9, 'Antarctica/McMurdo', 'NZDT', '46800', 'UTC+13:00', 'New Zealand Daylight Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(14, 9, 'Antarctica/Palmer', 'CLST', '-10800', 'UTC-03:00', 'Chile Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(15, 9, 'Antarctica/Rothera', 'ROTT', '-10800', 'UTC-03:00', 'Rothera Research Station Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(16, 9, 'Antarctica/Syowa', 'SYOT', '10800', 'UTC+03:00', 'Showa Station Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(17, 9, 'Antarctica/Troll', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(18, 9, 'Antarctica/Vostok', 'VOST', '21600', 'UTC+06:00', 'Vostok Station Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(19, 10, 'America/Argentina/Buenos_Aires', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(20, 10, 'America/Argentina/Catamarca', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(21, 10, 'America/Argentina/Cordoba', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(22, 10, 'America/Argentina/Jujuy', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(23, 10, 'America/Argentina/La_Rioja', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(24, 10, 'America/Argentina/Mendoza', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(25, 10, 'America/Argentina/Rio_Gallegos', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(26, 10, 'America/Argentina/Salta', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(27, 10, 'America/Argentina/San_Juan', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(28, 10, 'America/Argentina/San_Luis', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(29, 10, 'America/Argentina/Tucuman', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(30, 10, 'America/Argentina/Ushuaia', 'ART', '-10800', 'UTC-03:00', 'Argentina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(31, 11, 'Pacific/Pago_Pago', 'SST', '-39600', 'UTC-11:00', 'Samoa Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(32, 12, 'Europe/Vienna', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(33, 13, 'Antarctica/Macquarie', 'MIST', '39600', 'UTC+11:00', 'Macquarie Island Station Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(34, 13, 'Australia/Adelaide', 'ACDT', '37800', 'UTC+10:30', 'Australian Central Daylight Saving Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(35, 13, 'Australia/Brisbane', 'AEST', '36000', 'UTC+10:00', 'Australian Eastern Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(36, 13, 'Australia/Broken_Hill', 'ACDT', '37800', 'UTC+10:30', 'Australian Central Daylight Saving Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(37, 13, 'Australia/Currie', 'AEDT', '39600', 'UTC+11:00', 'Australian Eastern Daylight Saving Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(38, 13, 'Australia/Darwin', 'ACST', '34200', 'UTC+09:30', 'Australian Central Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(39, 13, 'Australia/Eucla', 'ACWST', '31500', 'UTC+08:45', 'Australian Central Western Standard Time (Unofficial)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(40, 13, 'Australia/Hobart', 'AEDT', '39600', 'UTC+11:00', 'Australian Eastern Daylight Saving Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(41, 13, 'Australia/Lindeman', 'AEST', '36000', 'UTC+10:00', 'Australian Eastern Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(42, 13, 'Australia/Lord_Howe', 'LHST', '39600', 'UTC+11:00', 'Lord Howe Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(43, 13, 'Australia/Melbourne', 'AEDT', '39600', 'UTC+11:00', 'Australian Eastern Daylight Saving Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(44, 13, 'Australia/Perth', 'AWST', '28800', 'UTC+08:00', 'Australian Western Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(45, 13, 'Australia/Sydney', 'AEDT', '39600', 'UTC+11:00', 'Australian Eastern Daylight Saving Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(46, 14, 'America/Aruba', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(47, 15, 'Europe/Mariehamn', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(48, 16, 'Asia/Baku', 'AZT', '14400', 'UTC+04:00', 'Azerbaijan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(49, 17, 'Europe/Sarajevo', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(50, 18, 'America/Barbados', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(51, 19, 'Asia/Dhaka', 'BDT', '21600', 'UTC+06:00', 'Bangladesh Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(52, 20, 'Europe/Brussels', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(53, 21, 'Africa/Ouagadougou', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(54, 22, 'Europe/Sofia', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(55, 23, 'Asia/Bahrain', 'AST', '10800', 'UTC+03:00', 'Arabia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(56, 24, 'Africa/Bujumbura', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(57, 25, 'Africa/Porto-Novo', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(58, 26, 'America/St_Barthelemy', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(59, 27, 'Atlantic/Bermuda', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(60, 28, 'Asia/Brunei', 'BNT', '28800', 'UTC+08:00', 'Brunei Darussalam Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(61, 29, 'America/La_Paz', 'BOT', '-14400', 'UTC-04:00', 'Bolivia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(62, 30, 'America/Anguilla', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(63, 31, 'America/Araguaina', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(64, 31, 'America/Bahia', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(65, 31, 'America/Belem', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(66, 31, 'America/Boa_Vista', 'AMT', '-14400', 'UTC-04:00', 'Amazon Time (Brazil)[3', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(67, 31, 'America/Campo_Grande', 'AMT', '-14400', 'UTC-04:00', 'Amazon Time (Brazil)[3', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(68, 31, 'America/Cuiaba', 'BRT', '-14400', 'UTC-04:00', 'Brasilia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(69, 31, 'America/Eirunepe', 'ACT', '-18000', 'UTC-05:00', 'Acre Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(70, 31, 'America/Fortaleza', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(71, 31, 'America/Maceio', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(72, 31, 'America/Manaus', 'AMT', '-14400', 'UTC-04:00', 'Amazon Time (Brazil)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(73, 31, 'America/Noronha', 'FNT', '-7200', 'UTC-02:00', 'Fernando de Noronha Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(74, 31, 'America/Porto_Velho', 'AMT', '-14400', 'UTC-04:00', 'Amazon Time (Brazil)[3', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(75, 31, 'America/Recife', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(76, 31, 'America/Rio_Branco', 'ACT', '-18000', 'UTC-05:00', 'Acre Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(77, 31, 'America/Santarem', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(78, 31, 'America/Sao_Paulo', 'BRT', '-10800', 'UTC-03:00', 'Brasília Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(79, 32, 'America/Nassau', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(80, 33, 'Asia/Thimphu', 'BTT', '21600', 'UTC+06:00', 'Bhutan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(81, 34, 'Europe/Oslo', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(82, 35, 'Africa/Gaborone', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(83, 36, 'Europe/Minsk', 'MSK', '10800', 'UTC+03:00', 'Moscow Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(84, 37, 'America/Belize', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(85, 38, 'America/Atikokan', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(86, 38, 'America/Blanc-Sablon', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(87, 38, 'America/Cambridge_Bay', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(88, 38, 'America/Creston', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(89, 38, 'America/Dawson', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(90, 38, 'America/Dawson_Creek', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(91, 38, 'America/Edmonton', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(92, 38, 'America/Fort_Nelson', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America)', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(93, 38, 'America/Glace_Bay', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(94, 38, 'America/Goose_Bay', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(95, 38, 'America/Halifax', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(96, 38, 'America/Inuvik', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(97, 38, 'America/Iqaluit', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(98, 38, 'America/Moncton', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(99, 38, 'America/Nipigon', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(100, 38, 'America/Pangnirtung', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(101, 38, 'America/Rainy_River', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(102, 38, 'America/Rankin_Inlet', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(103, 38, 'America/Regina', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(104, 38, 'America/Resolute', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(105, 38, 'America/St_Johns', 'NST', '-12600', 'UTC-03:30', 'Newfoundland Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(106, 38, 'America/Swift_Current', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(107, 38, 'America/Thunder_Bay', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(108, 38, 'America/Toronto', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(109, 38, 'America/Vancouver', 'PST', '-28800', 'UTC-08:00', 'Pacific Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(110, 38, 'America/Whitehorse', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(111, 38, 'America/Winnipeg', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(112, 38, 'America/Yellowknife', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(113, 39, 'Indian/Cocos', 'CCT', '23400', 'UTC+06:30', 'Cocos Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(114, 40, 'Africa/Kinshasa', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(115, 40, 'Africa/Lubumbashi', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(116, 41, 'Africa/Bangui', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(117, 42, 'Africa/Brazzaville', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(118, 43, 'Europe/Zurich', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(119, 44, 'Africa/Abidjan', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(120, 45, 'Pacific/Rarotonga', 'CKT', '-36000', 'UTC-10:00', 'Cook Island Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(121, 46, 'America/Punta_Arenas', 'CLST', '-10800', 'UTC-03:00', 'Chile Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(122, 46, 'America/Santiago', 'CLST', '-10800', 'UTC-03:00', 'Chile Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(123, 46, 'Pacific/Easter', 'EASST', '-18000', 'UTC-05:00', 'Easter Island Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(124, 47, 'Africa/Douala', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(125, 48, 'Asia/Shanghai', 'CST', '28800', 'UTC+08:00', 'China Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(126, 48, 'Asia/Urumqi', 'XJT', '21600', 'UTC+06:00', 'China Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(127, 49, 'America/Bogota', 'COT', '-18000', 'UTC-05:00', 'Colombia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(128, 50, 'America/Costa_Rica', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(129, 51, 'America/Havana', 'CST', '-18000', 'UTC-05:00', 'Cuba Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(130, 52, 'Atlantic/Cape_Verde', 'CVT', '-3600', 'UTC-01:00', 'Cape Verde Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(131, 53, 'America/Curacao', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(132, 54, 'Indian/Christmas', 'CXT', '25200', 'UTC+07:00', 'Christmas Island Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(133, 55, 'Asia/Famagusta', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(134, 55, 'Asia/Nicosia', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(135, 56, 'Europe/Prague', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(136, 57, 'Europe/Berlin', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(137, 57, 'Europe/Busingen', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(138, 58, 'Africa/Djibouti', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(139, 59, 'Europe/Copenhagen', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(140, 60, 'America/Dominica', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(141, 61, 'America/Santo_Domingo', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(142, 62, 'Africa/Algiers', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(143, 63, 'America/Guayaquil', 'ECT', '-18000', 'UTC-05:00', 'Ecuador Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(144, 63, 'Pacific/Galapagos', 'GALT', '-21600', 'UTC-06:00', 'Galápagos Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(145, 64, 'Europe/Tallinn', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(146, 65, 'Africa/Cairo', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(147, 66, 'Africa/El_Aaiun', 'WEST', '3600', 'UTC+01:00', 'Western European Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(148, 67, 'Africa/Asmara', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(149, 68, 'Africa/Ceuta', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(150, 68, 'Atlantic/Canary', 'WET', '0', 'UTC±00', 'Western European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(151, 68, 'Europe/Madrid', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(152, 69, 'Africa/Addis_Ababa', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(153, 70, 'Europe/Helsinki', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(154, 71, 'Pacific/Fiji', 'FJT', '43200', 'UTC+12:00', 'Fiji Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(155, 72, 'Atlantic/Stanley', 'FKST', '-10800', 'UTC-03:00', 'Falkland Islands Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(156, 73, 'Pacific/Chuuk', 'CHUT', '36000', 'UTC+10:00', 'Chuuk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(157, 73, 'Pacific/Kosrae', 'KOST', '39600', 'UTC+11:00', 'Kosrae Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(158, 73, 'Pacific/Pohnpei', 'PONT', '39600', 'UTC+11:00', 'Pohnpei Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(159, 74, 'Atlantic/Faroe', 'WET', '0', 'UTC±00', 'Western European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(160, 75, 'Europe/Paris', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(161, 76, 'Africa/Libreville', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(162, 77, 'Europe/London', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(163, 78, 'America/Grenada', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(164, 79, 'Asia/Tbilisi', 'GET', '14400', 'UTC+04:00', 'Georgia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(165, 80, 'America/Cayenne', 'GFT', '-10800', 'UTC-03:00', 'French Guiana Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(166, 81, 'Europe/Guernsey', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(167, 82, 'Africa/Accra', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(168, 83, 'Europe/Gibraltar', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(169, 84, 'America/Danmarkshavn', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(170, 84, 'America/Nuuk', 'WGT', '-10800', 'UTC-03:00', 'West Greenland Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(171, 84, 'America/Scoresbysund', 'EGT', '-3600', 'UTC-01:00', 'Eastern Greenland Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(172, 84, 'America/Thule', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(173, 85, 'Africa/Banjul', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(174, 86, 'Africa/Conakry', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(175, 87, 'America/Guadeloupe', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(176, 88, 'Africa/Malabo', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(177, 89, 'Europe/Athens', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(178, 90, 'Atlantic/South_Georgia', 'GST', '-7200', 'UTC-02:00', 'South Georgia and the South Sandwich Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(179, 91, 'America/Guatemala', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(180, 92, 'Pacific/Guam', 'CHST', '36000', 'UTC+10:00', 'Chamorro Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(181, 93, 'Africa/Bissau', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(182, 94, 'America/Guyana', 'GYT', '-14400', 'UTC-04:00', 'Guyana Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(183, 95, 'Asia/Hong_Kong', 'HKT', '28800', 'UTC+08:00', 'Hong Kong Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(184, 96, 'Indian/Kerguelen', 'TFT', '18000', 'UTC+05:00', 'French Southern and Antarctic Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(185, 97, 'America/Tegucigalpa', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(186, 98, 'Europe/Zagreb', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(187, 99, 'America/Port-au-Prince', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(188, 100, 'Europe/Budapest', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(189, 101, 'Asia/Jakarta', 'WIB', '25200', 'UTC+07:00', 'Western Indonesian Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(190, 101, 'Asia/Jayapura', 'WIT', '32400', 'UTC+09:00', 'Eastern Indonesian Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(191, 101, 'Asia/Makassar', 'WITA', '28800', 'UTC+08:00', 'Central Indonesia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(192, 101, 'Asia/Pontianak', 'WIB', '25200', 'UTC+07:00', 'Western Indonesian Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(193, 102, 'Europe/Dublin', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(194, 103, 'Asia/Jerusalem', 'IST', '7200', 'UTC+02:00', 'Israel Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(195, 104, 'Europe/Isle_of_Man', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(196, 105, 'Asia/Kolkata', 'IST', '19800', 'UTC+05:30', 'Indian Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(197, 106, 'Indian/Chagos', 'IOT', '21600', 'UTC+06:00', 'Indian Ocean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(198, 107, 'Asia/Baghdad', 'AST', '10800', 'UTC+03:00', 'Arabia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(199, 108, 'Asia/Tehran', 'IRDT', '12600', 'UTC+03:30', 'Iran Daylight Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(200, 109, 'Atlantic/Reykjavik', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(201, 110, 'Europe/Rome', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(202, 111, 'Europe/Jersey', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(203, 112, 'America/Jamaica', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(204, 113, 'Asia/Amman', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(205, 114, 'Asia/Tokyo', 'JST', '32400', 'UTC+09:00', 'Japan Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(206, 115, 'Africa/Nairobi', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(207, 116, 'Asia/Bishkek', 'KGT', '21600', 'UTC+06:00', 'Kyrgyzstan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(208, 117, 'Asia/Phnom_Penh', 'ICT', '25200', 'UTC+07:00', 'Indochina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(209, 118, 'Pacific/Enderbury', 'PHOT', '46800', 'UTC+13:00', 'Phoenix Island Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(210, 118, 'Pacific/Kiritimati', 'LINT', '50400', 'UTC+14:00', 'Line Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(211, 118, 'Pacific/Tarawa', 'GILT', '43200', 'UTC+12:00', 'Gilbert Island Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(212, 119, 'Indian/Comoro', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(213, 120, 'America/St_Kitts', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(214, 121, 'Asia/Pyongyang', 'KST', '32400', 'UTC+09:00', 'Korea Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(215, 122, 'Asia/Seoul', 'KST', '32400', 'UTC+09:00', 'Korea Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(216, 123, 'Asia/Kuwait', 'AST', '10800', 'UTC+03:00', 'Arabia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(217, 124, 'America/Cayman', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(218, 125, 'Asia/Almaty', 'ALMT', '21600', 'UTC+06:00', 'Alma-Ata Time[1', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(219, 125, 'Asia/Aqtau', 'AQTT', '18000', 'UTC+05:00', 'Aqtobe Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(220, 125, 'Asia/Aqtobe', 'AQTT', '18000', 'UTC+05:00', 'Aqtobe Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(221, 125, 'Asia/Atyrau', 'MSD+1', '18000', 'UTC+05:00', 'Moscow Daylight Time+1', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(222, 125, 'Asia/Oral', 'ORAT', '18000', 'UTC+05:00', 'Oral Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(223, 125, 'Asia/Qostanay', 'QYZST', '21600', 'UTC+06:00', 'Qyzylorda Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(224, 125, 'Asia/Qyzylorda', 'QYZT', '18000', 'UTC+05:00', 'Qyzylorda Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(225, 126, 'Asia/Vientiane', 'ICT', '25200', 'UTC+07:00', 'Indochina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(226, 127, 'Asia/Beirut', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(227, 128, 'America/St_Lucia', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(228, 129, 'Europe/Vaduz', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(229, 130, 'Asia/Colombo', 'IST', '19800', 'UTC+05:30', 'Indian Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(230, 131, 'Africa/Monrovia', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(231, 132, 'Africa/Maseru', 'SAST', '7200', 'UTC+02:00', 'South African Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(232, 133, 'Europe/Vilnius', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(233, 134, 'Europe/Luxembourg', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(234, 135, 'Europe/Riga', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(235, 136, 'Africa/Tripoli', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(236, 137, 'Africa/Casablanca', 'WEST', '3600', 'UTC+01:00', 'Western European Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(237, 138, 'Europe/Monaco', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(238, 139, 'Europe/Chisinau', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(239, 140, 'Europe/Podgorica', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(240, 141, 'America/Marigot', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(241, 142, 'America/Anguilla', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(242, 143, 'Indian/Antananarivo', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(243, 144, 'Pacific/Kwajalein', 'MHT', '43200', 'UTC+12:00', 'Marshall Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(244, 144, 'Pacific/Majuro', 'MHT', '43200', 'UTC+12:00', 'Marshall Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(245, 145, 'Europe/Skopje', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(246, 146, 'Africa/Bamako', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(247, 147, 'Asia/Yangon', 'MMT', '23400', 'UTC+06:30', 'Myanmar Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(248, 148, 'Asia/Choibalsan', 'CHOT', '28800', 'UTC+08:00', 'Choibalsan Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(249, 148, 'Asia/Hovd', 'HOVT', '25200', 'UTC+07:00', 'Hovd Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(250, 148, 'Asia/Ulaanbaatar', 'ULAT', '28800', 'UTC+08:00', 'Ulaanbaatar Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(251, 149, 'Asia/Macau', 'CST', '28800', 'UTC+08:00', 'China Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(252, 150, 'Pacific/Saipan', 'ChST', '36000', 'UTC+10:00', 'Chamorro Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(253, 151, 'America/Martinique', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(254, 152, 'Africa/Nouakchott', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(255, 153, 'America/Montserrat', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(256, 154, 'Europe/Malta', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(257, 155, 'Indian/Mauritius', 'MUT', '14400', 'UTC+04:00', 'Mauritius Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(258, 156, 'Indian/Maldives', 'MVT', '18000', 'UTC+05:00', 'Maldives Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(259, 157, 'Africa/Blantyre', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(260, 158, 'America/Bahia_Banderas', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(261, 158, 'America/Cancun', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(262, 158, 'America/Chihuahua', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(263, 158, 'America/Hermosillo', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(264, 158, 'America/Matamoros', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(265, 158, 'America/Mazatlan', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(266, 158, 'America/Merida', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(267, 158, 'America/Mexico_City', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(268, 158, 'America/Monterrey', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(269, 158, 'America/Ojinaga', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(270, 158, 'America/Tijuana', 'PST', '-28800', 'UTC-08:00', 'Pacific Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(271, 159, 'Asia/Kuala_Lumpur', 'MYT', '28800', 'UTC+08:00', 'Malaysia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(272, 159, 'Asia/Kuching', 'MYT', '28800', 'UTC+08:00', 'Malaysia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(273, 160, 'Africa/Maputo', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(274, 161, 'Africa/Windhoek', 'WAST', '7200', 'UTC+02:00', 'West Africa Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(275, 162, 'Pacific/Noumea', 'NCT', '39600', 'UTC+11:00', 'New Caledonia Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(276, 163, 'Africa/Niamey', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(277, 164, 'Pacific/Norfolk', 'NFT', '43200', 'UTC+12:00', 'Norfolk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(278, 165, 'Africa/Lagos', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(279, 166, 'America/Managua', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(280, 167, 'Europe/Amsterdam', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(281, 168, 'Europe/Oslo', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(282, 169, 'Asia/Kathmandu', 'NPT', '20700', 'UTC+05:45', 'Nepal Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(283, 170, 'Pacific/Nauru', 'NRT', '43200', 'UTC+12:00', 'Nauru Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(284, 171, 'Pacific/Niue', 'NUT', '-39600', 'UTC-11:00', 'Niue Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(285, 172, 'Pacific/Auckland', 'NZDT', '46800', 'UTC+13:00', 'New Zealand Daylight Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(286, 172, 'Pacific/Chatham', 'CHAST', '49500', 'UTC+13:45', 'Chatham Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(287, 173, 'Asia/Muscat', 'GST', '14400', 'UTC+04:00', 'Gulf Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(288, 174, 'America/Panama', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(289, 175, 'America/Lima', 'PET', '-18000', 'UTC-05:00', 'Peru Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(290, 176, 'Pacific/Gambier', 'GAMT', '-32400', 'UTC-09:00', 'Gambier Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(291, 176, 'Pacific/Marquesas', 'MART', '-34200', 'UTC-09:30', 'Marquesas Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(292, 176, 'Pacific/Tahiti', 'TAHT', '-36000', 'UTC-10:00', 'Tahiti Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(293, 177, 'Pacific/Bougainville', 'BST', '39600', 'UTC+11:00', 'Bougainville Standard Time[6', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(294, 177, 'Pacific/Port_Moresby', 'PGT', '36000', 'UTC+10:00', 'Papua New Guinea Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(295, 178, 'Asia/Manila', 'PHT', '28800', 'UTC+08:00', 'Philippine Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(296, 179, 'Asia/Karachi', 'PKT', '18000', 'UTC+05:00', 'Pakistan Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(297, 180, 'Europe/Warsaw', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(298, 181, 'America/Miquelon', 'PMDT', '-10800', 'UTC-03:00', 'Pierre & Miquelon Daylight Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(299, 182, 'Pacific/Pitcairn', 'PST', '-28800', 'UTC-08:00', 'Pacific Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(300, 183, 'America/Puerto_Rico', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(301, 184, 'Asia/Gaza', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(302, 184, 'Asia/Hebron', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(303, 185, 'Atlantic/Azores', 'AZOT', '-3600', 'UTC-01:00', 'Azores Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(304, 185, 'Atlantic/Madeira', 'WET', '0', 'UTC±00', 'Western European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(305, 185, 'Europe/Lisbon', 'WET', '0', 'UTC±00', 'Western European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(306, 186, 'Pacific/Palau', 'PWT', '32400', 'UTC+09:00', 'Palau Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(307, 187, 'America/Asuncion', 'PYST', '-10800', 'UTC-03:00', 'Paraguay Summer Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(308, 188, 'Asia/Qatar', 'AST', '10800', 'UTC+03:00', 'Arabia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(309, 189, 'Indian/Reunion', 'RET', '14400', 'UTC+04:00', 'Réunion Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(310, 190, 'Europe/Bucharest', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(311, 191, 'Europe/Belgrade', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(312, 192, 'Asia/Anadyr', 'ANAT', '43200', 'UTC+12:00', 'Anadyr Time[4', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(313, 192, 'Asia/Barnaul', 'KRAT', '25200', 'UTC+07:00', 'Krasnoyarsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(314, 192, 'Asia/Chita', 'YAKT', '32400', 'UTC+09:00', 'Yakutsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(315, 192, 'Asia/Irkutsk', 'IRKT', '28800', 'UTC+08:00', 'Irkutsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(316, 192, 'Asia/Kamchatka', 'PETT', '43200', 'UTC+12:00', 'Kamchatka Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(317, 192, 'Asia/Khandyga', 'YAKT', '32400', 'UTC+09:00', 'Yakutsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(318, 192, 'Asia/Krasnoyarsk', 'KRAT', '25200', 'UTC+07:00', 'Krasnoyarsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(319, 192, 'Asia/Magadan', 'MAGT', '39600', 'UTC+11:00', 'Magadan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(320, 192, 'Asia/Novokuznetsk', 'KRAT', '25200', 'UTC+07:00', 'Krasnoyarsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(321, 192, 'Asia/Novosibirsk', 'NOVT', '25200', 'UTC+07:00', 'Novosibirsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(322, 192, 'Asia/Omsk', 'OMST', '21600', 'UTC+06:00', 'Omsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(323, 192, 'Asia/Sakhalin', 'SAKT', '39600', 'UTC+11:00', 'Sakhalin Island Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(324, 192, 'Asia/Srednekolymsk', 'SRET', '39600', 'UTC+11:00', 'Srednekolymsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(325, 192, 'Asia/Tomsk', 'MSD+3', '25200', 'UTC+07:00', 'Moscow Daylight Time+3', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(326, 192, 'Asia/Ust-Nera', 'VLAT', '36000', 'UTC+10:00', 'Vladivostok Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(327, 192, 'Asia/Vladivostok', 'VLAT', '36000', 'UTC+10:00', 'Vladivostok Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(328, 192, 'Asia/Yakutsk', 'YAKT', '32400', 'UTC+09:00', 'Yakutsk Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(329, 192, 'Asia/Yekaterinburg', 'YEKT', '18000', 'UTC+05:00', 'Yekaterinburg Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(330, 192, 'Europe/Astrakhan', 'SAMT', '14400', 'UTC+04:00', 'Samara Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(331, 192, 'Europe/Kaliningrad', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(332, 192, 'Europe/Kirov', 'MSK', '10800', 'UTC+03:00', 'Moscow Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(333, 192, 'Europe/Moscow', 'MSK', '10800', 'UTC+03:00', 'Moscow Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(334, 192, 'Europe/Samara', 'SAMT', '14400', 'UTC+04:00', 'Samara Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(335, 192, 'Europe/Saratov', 'MSD', '14400', 'UTC+04:00', 'Moscow Daylight Time+4', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(336, 192, 'Europe/Ulyanovsk', 'SAMT', '14400', 'UTC+04:00', 'Samara Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(337, 192, 'Europe/Volgograd', 'MSK', '14400', 'UTC+04:00', 'Moscow Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(338, 193, 'Africa/Kigali', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(339, 194, 'Asia/Riyadh', 'AST', '10800', 'UTC+03:00', 'Arabia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(340, 195, 'Pacific/Guadalcanal', 'SBT', '39600', 'UTC+11:00', 'Solomon Islands Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(341, 196, 'Indian/Mahe', 'SCT', '14400', 'UTC+04:00', 'Seychelles Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(342, 197, 'Africa/Khartoum', 'EAT', '7200', 'UTC+02:00', 'Eastern African Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(343, 198, 'Europe/Stockholm', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(344, 199, 'Asia/Singapore', 'SGT', '28800', 'UTC+08:00', 'Singapore Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(345, 200, 'Atlantic/St_Helena', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(346, 201, 'Europe/Ljubljana', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(347, 202, 'Arctic/Longyearbyen', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(348, 203, 'Europe/Bratislava', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(349, 204, 'Africa/Freetown', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(350, 205, 'Europe/San_Marino', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(351, 206, 'Africa/Dakar', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(352, 207, 'Africa/Mogadishu', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(353, 208, 'America/Paramaribo', 'SRT', '-10800', 'UTC-03:00', 'Suriname Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(354, 209, 'Africa/Juba', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(355, 210, 'Africa/Sao_Tome', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(356, 211, 'America/El_Salvador', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(357, 212, 'Asia/Damascus', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(358, 213, 'Africa/Mbabane', 'SAST', '7200', 'UTC+02:00', 'South African Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(359, 214, 'America/Grand_Turk', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(360, 215, 'Africa/Ndjamena', 'WAT', '3600', 'UTC+01:00', 'West Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(361, 216, 'Indian/Kerguelen', 'TFT', '18000', 'UTC+05:00', 'French Southern and Antarctic Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(362, 217, 'Africa/Lome', 'GMT', '0', 'UTC±00', 'Greenwich Mean Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(363, 218, 'Asia/Bangkok', 'ICT', '25200', 'UTC+07:00', 'Indochina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(364, 219, 'Asia/Dushanbe', 'TJT', '18000', 'UTC+05:00', 'Tajikistan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(365, 220, 'Pacific/Fakaofo', 'TKT', '46800', 'UTC+13:00', 'Tokelau Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(366, 221, 'Asia/Dili', 'TLT', '32400', 'UTC+09:00', 'Timor Leste Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(367, 222, 'Asia/Ashgabat', 'TMT', '18000', 'UTC+05:00', 'Turkmenistan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(368, 223, 'Africa/Tunis', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(369, 224, 'Pacific/Tongatapu', 'TOT', '46800', 'UTC+13:00', 'Tonga Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(370, 225, 'Europe/Istanbul', 'EET', '10800', 'UTC+03:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(371, 226, 'America/Port_of_Spain', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(372, 227, 'Pacific/Funafuti', 'TVT', '43200', 'UTC+12:00', 'Tuvalu Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(373, 228, 'Asia/Taipei', 'CST', '28800', 'UTC+08:00', 'China Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(374, 229, 'Africa/Dar_es_Salaam', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(375, 230, 'Europe/Kiev', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(376, 230, 'Europe/Simferopol', 'MSK', '10800', 'UTC+03:00', 'Moscow Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(377, 230, 'Europe/Uzhgorod', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(378, 230, 'Europe/Zaporozhye', 'EET', '7200', 'UTC+02:00', 'Eastern European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(379, 231, 'Africa/Kampala', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(380, 232, 'Pacific/Midway', 'SST', '-39600', 'UTC-11:00', 'Samoa Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(381, 232, 'Pacific/Wake', 'WAKT', '43200', 'UTC+12:00', 'Wake Island Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(382, 233, 'America/Adak', 'HST', '-36000', 'UTC-10:00', 'Hawaii–Aleutian Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(383, 233, 'America/Anchorage', 'AKST', '-32400', 'UTC-09:00', 'Alaska Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(384, 233, 'America/Boise', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(385, 233, 'America/Chicago', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(386, 233, 'America/Denver', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(387, 233, 'America/Detroit', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(388, 233, 'America/Indiana/Indianapolis', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(389, 233, 'America/Indiana/Knox', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(390, 233, 'America/Indiana/Marengo', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(391, 233, 'America/Indiana/Petersburg', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(392, 233, 'America/Indiana/Tell_City', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(393, 233, 'America/Indiana/Vevay', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(394, 233, 'America/Indiana/Vincennes', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(395, 233, 'America/Indiana/Winamac', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(396, 233, 'America/Juneau', 'AKST', '-32400', 'UTC-09:00', 'Alaska Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(397, 233, 'America/Kentucky/Louisville', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(398, 233, 'America/Kentucky/Monticello', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(399, 233, 'America/Los_Angeles', 'PST', '-28800', 'UTC-08:00', 'Pacific Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(400, 233, 'America/Menominee', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(401, 233, 'America/Metlakatla', 'AKST', '-32400', 'UTC-09:00', 'Alaska Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(402, 233, 'America/New_York', 'EST', '-18000', 'UTC-05:00', 'Eastern Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(403, 233, 'America/Nome', 'AKST', '-32400', 'UTC-09:00', 'Alaska Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(404, 233, 'America/North_Dakota/Beulah', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(405, 233, 'America/North_Dakota/Center', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(406, 233, 'America/North_Dakota/New_Salem', 'CST', '-21600', 'UTC-06:00', 'Central Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(407, 233, 'America/Phoenix', 'MST', '-25200', 'UTC-07:00', 'Mountain Standard Time (North America', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(408, 233, 'America/Sitka', 'AKST', '-32400', 'UTC-09:00', 'Alaska Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(409, 233, 'America/Yakutat', 'AKST', '-32400', 'UTC-09:00', 'Alaska Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(410, 233, 'Pacific/Honolulu', 'HST', '-36000', 'UTC-10:00', 'Hawaii–Aleutian Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(411, 234, 'America/Montevideo', 'UYT', '-10800', 'UTC-03:00', 'Uruguay Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(412, 235, 'Asia/Samarkand', 'UZT', '18000', 'UTC+05:00', 'Uzbekistan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(413, 235, 'Asia/Tashkent', 'UZT', '18000', 'UTC+05:00', 'Uzbekistan Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(414, 236, 'Europe/Vatican', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(415, 237, 'America/St_Vincent', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(416, 238, 'America/Caracas', 'VET', '-14400', 'UTC-04:00', 'Venezuelan Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(417, 239, 'America/Tortola', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(418, 240, 'America/St_Thomas', 'AST', '-14400', 'UTC-04:00', 'Atlantic Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(419, 241, 'Asia/Ho_Chi_Minh', 'ICT', '25200', 'UTC+07:00', 'Indochina Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(420, 242, 'Pacific/Efate', 'VUT', '39600', 'UTC+11:00', 'Vanuatu Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(421, 243, 'Pacific/Wallis', 'WFT', '43200', 'UTC+12:00', 'Wallis & Futuna Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(422, 244, 'Pacific/Apia', 'WST', '50400', 'UTC+14:00', 'West Samoa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(423, 245, 'Europe/Belgrade', 'CET', '3600', 'UTC+01:00', 'Central European Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(424, 246, 'Asia/Aden', 'AST', '10800', 'UTC+03:00', 'Arabia Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(425, 247, 'Indian/Mayotte', 'EAT', '10800', 'UTC+03:00', 'East Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(426, 248, 'Africa/Johannesburg', 'SAST', '7200', 'UTC+02:00', 'South African Standard Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(427, 249, 'Africa/Lusaka', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21'),
(428, 250, 'Africa/Harare', 'CAT', '7200', 'UTC+02:00', 'Central Africa Time', '2023-08-22 19:40:21', '2023-08-22 19:40:21');


/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;