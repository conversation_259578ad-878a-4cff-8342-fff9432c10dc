<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\UserRating;

class RatingController extends Controller
{
    public function rateUser(Request $request, $rated_id)
    {
        $rater = Auth::user();

        // التحقق من أن الحساب موثوق
        if (!$rater->is_trusted) {
            return back()->withErrors(['message' => 'حسابك غير موثوق ولا يمكنك التقييم.']);
        }

        // التحقق من عدم تقييم نفسه
        if ($rater->id === $rated_id) {
            return back()->withErrors(['message' => 'لا يمكنك تقييم نفسك.']);
        }

        $ratedUser = User::findOrFail($rated_id);

        // التحقق من وجود تقييم مسبق
        $existingRating = UserRating::where('rater_id', $rater->id)
                                    ->where('rated_id', $rated_id)
                                    ->first();

        if ($existingRating) {
            return back()->withErrors(['message' => 'لقد قمت بتقييم هذا المستخدم من قبل.']);
        }

        // التحقق من صحة البيانات
        $validated = $request->validate([
            'rating' => 'required|numeric|min:1|max:5',
            'comment' => 'nullable|string|max:500',
        ]);

        // حفظ التقييم
        $newRating = new UserRating();
        $newRating->rater_id = $rater->id;
        $newRating->rated_id = $ratedUser->id;
        $newRating->rating = round($validated['rating'], 1);
        $newRating->comment = $validated['comment'] ?? null;
        $newRating->save();

        // حساب المتوسط الجديد
        $avgRating = UserRating::where('rated_id', $ratedUser->id)->avg('rating');
        $count = UserRating::where('rated_id', $ratedUser->id)->count();

        // تحديث المستخدم
        $ratedUser->rating = round($avgRating, 1);
        $ratedUser->ratings_count = $count;
        $ratedUser->save();

        return redirect()->route('users.rate', $ratedUser->id)->with('success', 'تم التقييم بنجاح.');
    }

    public function showRatePage($id)
    {
        $user = User::findOrFail($id);
        $authUser = auth()->user();

        $canRate = $authUser && $authUser->is_trusted &&
                   $authUser->id !== $user->id &&
                   !UserRating::where('rater_id', $authUser->id)
                              ->where('rated_id', $user->id)
                              ->exists();

        $ratings = UserRating::where('rated_id', $user->id)
                             ->with('rater')
                             ->orderByDesc('created_at')
                             ->get();

        return view('users.rate', [
            'user' => $user,
            'canRate' => $canRate,
            'ratings' => $ratings
        ]);
    }
}
