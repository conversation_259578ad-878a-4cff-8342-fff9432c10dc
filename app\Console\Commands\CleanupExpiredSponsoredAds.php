<?php

namespace App\Console\Commands;

use App\Models\SponsoredAd;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CleanupExpiredSponsoredAds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sponsored-ads:cleanup {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup expired sponsored ads and update their status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        $this->info('Starting sponsored ads cleanup...');
        
        // البحث عن الإعلانات الممولة المنتهية الصلاحية
        $expiredAds = SponsoredAd::where('status', 'active')
            ->where('is_active', true)
            ->where('expires_at', '<=', now())
            ->get();

        if ($expiredAds->isEmpty()) {
            $this->info('No expired sponsored ads found.');
            return 0;
        }

        $this->info("Found {$expiredAds->count()} expired sponsored ads.");

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
            $this->table(
                ['ID', 'Ad Title', 'Cost', 'Expired At', 'Views', 'Clicks', 'CTR'],
                $expiredAds->map(function ($sponsoredAd) {
                    return [
                        $sponsoredAd->id,
                        $sponsoredAd->ad->title ?? 'N/A',
                        $sponsoredAd->cost,
                        $sponsoredAd->expires_at->format('Y-m-d H:i:s'),
                        $sponsoredAd->views_count,
                        $sponsoredAd->clicks_count,
                        $sponsoredAd->getCTRText()
                    ];
                })->toArray()
            );
            return 0;
        }

        $updatedCount = 0;
        $bar = $this->output->createProgressBar($expiredAds->count());
        $bar->start();

        foreach ($expiredAds as $sponsoredAd) {
            try {
                // تحديث حالة الإعلان الممول
                $sponsoredAd->update([
                    'status' => 'expired',
                    'is_active' => false
                ]);

                $updatedCount++;
                
                // تسجيل الإحصائيات النهائية
                $this->logFinalStats($sponsoredAd);
                
            } catch (\Exception $e) {
                $this->error("Failed to update sponsored ad {$sponsoredAd->id}: " . $e->getMessage());
            }
            
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        $this->info("Successfully updated {$updatedCount} expired sponsored ads.");
        
        // مسح الكاش
        $this->clearRelatedCache();
        
        $this->info('Cache cleared successfully.');
        
        return 0;
    }

    /**
     * تسجيل الإحصائيات النهائية للإعلان الممول
     */
    private function logFinalStats(SponsoredAd $sponsoredAd): void
    {
        $this->line("Sponsored Ad #{$sponsoredAd->id} final stats:");
        $this->line("  - Views: {$sponsoredAd->views_count}");
        $this->line("  - Clicks: {$sponsoredAd->clicks_count}");
        $this->line("  - CTR: {$sponsoredAd->getCTRText()}");
        $this->line("  - Cost: {$sponsoredAd->cost}");
        $this->line("  - Duration: {$sponsoredAd->total_minutes} minutes");
    }

    /**
     * مسح الكاش المرتبط بالإعلانات الممولة
     */
    private function clearRelatedCache(): void
    {
        // مسح كاش الإعلانات الممولة
        Cache::flush(); // في بيئة الإنتاج، يفضل مسح كاش محدد
        
        // يمكن إضافة مسح كاش أكثر تحديداً هنا
        // Cache::forget('sponsored_ads_*');
        // Cache::forget('related_sponsored_ads_*');
    }
}
