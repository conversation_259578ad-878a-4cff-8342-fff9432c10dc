<?php

namespace App\Http\Controllers;

use App\Models\BrokerApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\SimpleFileCompressionService;
use App\Services\AdvancedImageOptimizer;
use Illuminate\Support\Facades\Log;

class BrokerApplicationController extends Controller
{
    /**
     * عرض صفحة التقديم للمندوبين
     */
    public function create(): View
    {
        $user = auth()->user();

        // التحقق من وجود طلب سابق
        $existingApplication = BrokerApplication::where('user_id', $user->id)->first();

        return view('broker-application.create', compact('existingApplication'));
    }

    /**
     * حفظ طلب التقديم
     */
    public function store(Request $request): RedirectResponse
    {
        $user = auth()->user();

        // التحقق من عدم وجود طلب سابق أو إمكانية التقديم مرة أخرى
        $existingApplication = BrokerApplication::where('user_id', $user->id)->first();

        if ($existingApplication && !$existingApplication->canReapply()) {
            return back()->with('error', 'لا يمكنك التقديم في الوقت الحالي');
        }

        $request->validate([
            'full_name' => 'required|string|max:255',
            'education' => 'required|string',
            'experience' => 'required|string',
            'additional_notes' => 'nullable|string',
        ]);

        // حذف الطلب السابق إذا كان مرفوضاً ويمكن التقديم مرة أخرى
        if ($existingApplication && $existingApplication->canReapply()) {
            $existingApplication->delete();
        }

        BrokerApplication::create([
            'user_id' => $user->id,
            'full_name' => $request->full_name,
            'education' => $request->education,
            'experience' => $request->experience,
            'additional_notes' => $request->additional_notes,
            'status' => BrokerApplication::STATUS_PENDING,
        ]);

        return redirect()->route('broker-application.create')
                        ->with('success', 'تم إرسال طلبك بنجاح! سيتم مراجعته قريباً.');
    }

    /**
     * عرض صفحة رفع المستندات (المرحلة الثانية)
     */
    public function documents(): View
    {
        $user = auth()->user();
        $application = BrokerApplication::where('user_id', $user->id)
                                      ->where('status', BrokerApplication::STATUS_FIRST_STAGE_ACCEPTED)
                                      ->first();

        if (!$application) {
            abort(404, 'لم يتم العثور على طلب مقبول في المرحلة الأولى');
        }

        return view('broker-application.documents', compact('application'));
    }

    /**
     * حفظ المستندات
     */
    public function storeDocuments(Request $request): RedirectResponse
    {
        $user = auth()->user();
        $application = BrokerApplication::where('user_id', $user->id)
                                      ->where('status', BrokerApplication::STATUS_FIRST_STAGE_ACCEPTED)
                                      ->first();

        if (!$application) {
            return back()->with('error', 'لم يتم العثور على طلب مقبول في المرحلة الأولى');
        }

        // التحقق من حالة توثيق المستخدم
        $isVerified = $user->national_id &&
                     $user->identityVerification &&
                     $user->identityVerification->status === 'approved';

        // إذا كان المستخدم موثق، يجب أن يستخدم نفس الرقم القومي
        if ($isVerified) {
            // التحقق من أن الرقم المرسل يطابق الرقم الموثق
            if ($request->national_id !== $user->national_id) {
                return back()->withErrors([
                    'national_id' => 'لا يمكن تغيير الرقم القومي لأن هويتك موثقة مسبقاً بالرقم: ' . $user->national_id
                ])->withInput();
            }

            $validationRules = [
                'national_id' => 'required|string|max:20',
                'education_certificate' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',
                'experience_proof' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
                'id_card' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',
                'additional_documents.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            ];
        } else {
            // المستخدم غير موثق - التحقق من عدم تكرار الرقم
            $validationRules = [
                'national_id' => 'required|string|max:20|unique:users,national_id,' . $user->id,
                'education_certificate' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',
                'experience_proof' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
                'id_card' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120',
                'additional_documents.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120',
            ];
        }

        $request->validate($validationRules);

        $documents = [];
        $compressionService = new SimpleFileCompressionService();
        $settings = $compressionService->getOptimizedSettings('broker_documents');

        // رفع شهادة التعليم
        if ($request->hasFile('education_certificate')) {
            $result = $compressionService->compressAndStore($request->file('education_certificate'), 'broker-documents', $settings);
            if ($result['success']) {
                $documents['education_certificate'] = $result['path'];

                Log::info('Education certificate compressed', [
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%'
                ]);
            } else {
                return back()->withErrors(['education_certificate' => 'فشل في معالجة شهادة التعليم.']);
            }
        }

        // رفع إثبات الخبرة
        if ($request->hasFile('experience_proof')) {
            $result = $compressionService->compressAndStore($request->file('experience_proof'), 'broker-documents', $settings);
            if ($result['success']) {
                $documents['experience_proof'] = $result['path'];

                Log::info('Experience proof compressed', [
                    'original_size' => $compressionService->formatFileSize($result['original_size']),
                    'compressed_size' => $compressionService->formatFileSize($result['compressed_size']),
                    'compression_ratio' => $result['compression_ratio'] . '%'
                ]);
            } else {
                return back()->withErrors(['experience_proof' => 'فشل في معالجة إثبات الخبرة.']);
            }
        }

        // رفع البطاقة الشخصية
        if ($request->hasFile('id_card')) {
            $result = $compressionService->compressAndStore($request->file('id_card'), 'broker-documents', $settings);
            if ($result['success']) {
                $documents['id_card'] = $result['path'];
            } else {
                return back()->withErrors(['id_card' => 'فشل في معالجة البطاقة الشخصية.']);
            }
        }

        // رفع المستندات الإضافية
        if ($request->hasFile('additional_documents')) {
            $additionalDocs = [];
            foreach ($request->file('additional_documents') as $file) {
                $result = $compressionService->compressAndStore($file, 'broker-documents', $settings);
                if ($result['success']) {
                    $additionalDocs[] = $result['path'];
                } else {
                    return back()->withErrors(['additional_documents' => 'فشل في معالجة أحد المستندات الإضافية.']);
                }
            }
            $documents['additional_documents'] = $additionalDocs;
        }

        // تحديث الطلب مع الرقم القومي (لا نحدث المستخدم حتى الموافقة)
        $application->update([
            'national_id' => $request->national_id,
            'documents' => $documents,
            'status' => BrokerApplication::STATUS_SECOND_STAGE_ACCEPTED,
        ]);

        return redirect()->route('broker-application.create')
                        ->with('success', 'تم رفع المستندات بنجاح! سيتم مراجعتها قريباً.');
    }
}
