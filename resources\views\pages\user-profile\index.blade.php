@extends('partials.app')
@section('title', $user->name . ' - User Profile')
@section('description', 'View ' . $user->name . '\'s profile and listings')

@php
    use Illuminate\Support\Facades\Storage;
    // تعريف المتغيرات في بداية الملف
    $averageRating = $user->average_rating ?? 0; // التقييم من 1-5 نجوم
    $level = min(floor($user->Number_Ads / 20) + 1, 50); // المستوى من 1-50 حسب عدد الإعلانات
@endphp

@section('content')

<!-- Hero Section -->
<div class="profile-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="hero-content">
                    <nav aria-label="breadcrumb" class="hero-breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('live-auction') }}">Listings</a></li>
                            <li class="breadcrumb-item active">{{ $user->name }}</li>
                        </ol>
                    </nav>
                    <h1 class="hero-title">User Profile</h1>
                    <p class="hero-subtitle">View seller information and recent listings</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="profile-main-section">
    <div class="container">
        <div class="row">
            <!-- User Info Card -->
            <div class="col-lg-4">
                <div class="user-info-card">
                    <div class="user-avatar">
                        <img src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}" alt="{{ $user->name }}">
                        @if($user->is_trusted)
                            <div class="verified-badge-blue">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        @else
                            <div class="unverified-badge">
                                <i class="fas fa-shield-exclamation"></i>
                            </div>
                        @endif
                    </div>
                    
                    <div class="user-details">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 class="user-name mb-0">{{ $user->name }}</h3>
                            @auth
                                @if(auth()->id() !== $user->id)
                                    <button type="button" class="report-btn"
                                            data-type="App\Models\User"
                                            data-id="{{ $user->id }}"
                                            title="إبلاغ عن هذا المستخدم">
                                        <i class="fas fa-flag"></i>
                                        إبلاغ
                                    </button>
                                @endif
                            @endauth
                        </div>

                        <!-- User Badges -->
                        <div class="user-badges">
                            @if($user->is_broker ?? false)
                                <span class="badge badge-broker">
                                    <i class="fas fa-handshake"></i>
                                    Representative
                                </span>
                                @if($averageRating > 0)
                                    <div class="broker-rating">
                                        <div class="rating-stars">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= round($averageRating) ? 'filled' : '' }}"></i>
                                            @endfor
                                        </div>
                                        <span class="rating-text">{{ number_format($averageRating, 1) }} ({{ $user->total_ratings }})</span>
                                    </div>
                                @endif
                            @endif
                            
                            <span class="badge badge-rank rank-{{ $user->rank ?? 0 }}">
                                @switch($user->rank)
                                    @case(1)
                                        <i class="fas fa-crown"></i>
                                        VIP Member
                                    @break
                                    @case(2)
                                        <i class="fas fa-chart-line"></i>
                                        Trader
                                    @break
                                    @case(3)
                                        <i class="fas fa-building"></i>
                                        Company
                                    @break
                                    @case(4)
                                        <i class="fas fa-gem"></i>
                                        Premium
                                    @break
                                    @default
                                        <i class="fas fa-user"></i>
                                        Regular Member
                                @endswitch
                            </span>

                            <!-- شارة التوثيق -->
                            @if($user->is_trusted)
                                <span class="badge badge-verified">
                                    <i class="fas fa-shield-check"></i>
                                    Verified Identity
                                </span>
                            @else
                                <span class="badge badge-unverified">
                                    <i class="fas fa-shield-exclamation"></i>
                                    Unverified
                                </span>
                            @endif
                            
                            <span class="badge badge-level">Level {{ $level }}</span>
                        </div>

                        <!-- Premium Rank Display -->
                        @switch($user->rank ?? 0)
                            @case(1)
                                <div class="premium-rank-card vip-member">
                                    <div class="rank-header">
                                        <i class="fas fa-crown rank-icon"></i>
                                        <span class="rank-title">VIP MEMBER</span>
                                        <div class="rank-sparkles">
                                            <i class="fas fa-sparkles"></i>
                                            <i class="fas fa-sparkles"></i>
                                            <i class="fas fa-sparkles"></i>
                                        </div>
                                    </div>
                                    <div class="rank-description">
                                        <span class="rank-subtitle">Elite Status • Verified Excellence</span>
                                        <div class="rank-features">
                                            <span><i class="fas fa-check"></i> Priority Support</span>
                                            <span><i class="fas fa-check"></i> Exclusive Deals</span>
                                            <span><i class="fas fa-check"></i> Premium Features</span>
                                        </div>
                                    </div>
                                </div>
                            @break
                            @case(2)
                                <div class="premium-rank-card trader">
                                    <div class="rank-header">
                                        <i class="fas fa-chart-line rank-icon"></i>
                                        <span class="rank-title">TRADER</span>
                                        <div class="rank-sparkles">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                    <div class="rank-description">
                                        <span class="rank-subtitle">Professional Trader • Market Expert</span>
                                        <div class="rank-features">
                                            <span><i class="fas fa-check"></i> Advanced Analytics</span>
                                            <span><i class="fas fa-check"></i> Market Insights</span>
                                            <span><i class="fas fa-check"></i> Trading Tools</span>
                                        </div>
                                    </div>
                                </div>
                            @break
                            @case(3)
                                <div class="premium-rank-card company">
                                    <div class="rank-header">
                                        <i class="fas fa-building rank-icon"></i>
                                        <span class="rank-title">COMPANY</span>
                                        <div class="rank-sparkles">
                                            <i class="fas fa-certificate"></i>
                                        </div>
                                    </div>
                                    <div class="rank-description">
                                        <span class="rank-subtitle">Business Account • Corporate Solutions</span>
                                        <div class="rank-features">
                                            <span><i class="fas fa-check"></i> Bulk Operations</span>
                                            <span><i class="fas fa-check"></i> Business Support</span>
                                            <span><i class="fas fa-check"></i> Corporate Features</span>
                                        </div>
                                    </div>
                                </div>
                            @break
                            @case(4)
                                <div class="premium-rank-card premium">
                                    <div class="rank-header">
                                        <i class="fas fa-gem rank-icon"></i>
                                        <span class="rank-title">PREMIUM</span>
                                        <div class="rank-sparkles">
                                            <i class="fas fa-diamond"></i>
                                            <i class="fas fa-diamond"></i>
                                        </div>
                                    </div>
                                    <div class="rank-description">
                                        <span class="rank-subtitle">Premium Account • Ultimate Experience</span>
                                        <div class="rank-features">
                                            <span><i class="fas fa-check"></i> All Features</span>
                                            <span><i class="fas fa-check"></i> Premium Support</span>
                                            <span><i class="fas fa-check"></i> Exclusive Access</span>
                                        </div>
                                    </div>
                                </div>
                            @break
                            @default
                                <div class="premium-rank-card regular">
                                    <div class="rank-header">
                                        <i class="fas fa-user rank-icon"></i>
                                        <span class="rank-title">REGULAR MEMBER</span>
                                    </div>
                                    <div class="rank-description">
                                        <span class="rank-subtitle">Standard Account • Getting Started</span>
                                        <div class="rank-features">
                                            <span><i class="fas fa-check"></i> Basic Features</span>
                                            <span><i class="fas fa-check"></i> Community Access</span>
                                            <span><i class="fas fa-check"></i> Standard Support</span>
                                        </div>
                                    </div>
                                </div>
                        @endswitch

                        <!-- User Stats -->
                        <div class="user-stats">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="rating-display">
                                        <span class="stat-value">{{ number_format($averageRating, 1) }}</span>
                                        <div class="stars">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= round($averageRating) ? 'filled' : '' }}"></i>
                                            @endfor
                                        </div>
                                    </div>
                                    <span class="stat-label">{{ $user->total_ratings }} reviews</span>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-value">{{ $totalAds }}</span>
                                    <span class="stat-label">Current ads</span>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-value">{{ $user->created_at ? $user->created_at->format('F Y') : 'N/A' }}</span>
                                    <span class="stat-label">Member since</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Info -->
                        <div class="user-info">
                            @if($user->address)
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>{{ $user->address }}</span>
                                </div>
                            @endif
                        </div>
                        
                        <!-- Contact Button -->
                        @auth
                            @if(auth()->id() !== $user->id)
                                <div class="contact-actions">
                                    <a href="{{ url('/chatify/' . $user->id) }}" class="btn btn-primary">
                                        <i class="fas fa-comments"></i>
                                        Start Chat
                                    </a>
                                    <a href="{{ route('rate-user', $user->id) }}" class="btn btn-secondary">
                                        <i class="fas fa-star"></i>
                                        Rate User
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="contact-actions">
                                <a href="{{ route('user.login') }}" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i>
                                    Login to Contact
                                </a>
                            </div>
                        @endauth
                    </div>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="col-lg-8">
                <!-- Recent Ads Section -->
                <div class="content-section">
                    <div class="section-header">
                        <h4 class="section-title">
                            <i class="fas fa-bullhorn"></i>
                            Recent Listings (Last 30 Days)
                        </h4>
                        <p class="section-subtitle">{{ $recentAds->count() }} listings found</p>
                    </div>
                    
                    @if($recentAds->count() > 0)
                        <div class="ads-grid">
                            @foreach($recentAds as $ad)
                                <div class="ad-card">
                                    <div class="ad-image">
                                        <a href="{{ route('auction-details', $ad->slug) }}">
                                            @if($ad->media->count() > 0)
                                                <img src="{{ $ad->media->first()->url }}" alt="{{ $ad->title }}" loading="lazy">
                                            @else
                                                <div class="no-image">
                                                    <i class="fas fa-image"></i>
                                                    <span>No Image</span>
                                                </div>
                                            @endif
                                        </a>
                                        
                                        @if($ad->media->count() > 1)
                                            <div class="image-count">
                                                <i class="fas fa-images"></i>
                                                {{ $ad->media->count() }}
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <div class="ad-content">
                                        <h5 class="ad-title">
                                            <a href="{{ route('auction-details', $ad->slug) }}">{{ $ad->title }}</a>
                                        </h5>
                                        <div class="ad-price">{{ money($ad->price) }}</div>
                                        <div class="ad-meta">
                                            <span class="ad-category">
                                                <i class="fas fa-tag"></i>
                                                {{ $ad->category->name ?? 'Uncategorized' }}
                                            </span>
                                            <span class="ad-date">
                                                <i class="fas fa-clock"></i>
                                                {{ $ad->created_at->diffForHumans() }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <h5>No Recent Listings</h5>
                            <p>This user hasn't posted any listings in the last 30 days.</p>
                        </div>
                    @endif
                </div>
                
                <!-- Representative Brokered Ads Section (Only for Brokers) -->
                @if(($user->is_broker ?? false) && $dealsAds->count() > 0)
                    <div class="content-section">
                        <div class="section-header">
                            <h4 class="section-title">
                                <i class="fas fa-handshake"></i>
                                Brokered Listings
                            </h4>
                            <p class="section-subtitle">Listings this representative has successfully brokered</p>
                        </div>

                        <div class="ads-grid">
                            @foreach($dealsAds as $ad)
                                @if($ad)
                                    <div class="ad-card brokered-ad">
                                        <div class="ad-image">
                                            <a href="{{ route('auction-details', $ad->slug) }}">
                                                @if($ad->media->count() > 0)
                                                    <img src="{{ $ad->media->first()->url }}" alt="{{ $ad->title }}" loading="lazy">
                                                @else
                                                    <div class="no-image">
                                                        <i class="fas fa-image"></i>
                                                        <span>No Image</span>
                                                    </div>
                                                @endif
                                            </a>

                                            <!-- شارة الوساطة -->
                                            <div class="brokered-badge">
                                                <i class="fas fa-handshake"></i>
                                                Brokered
                                            </div>

                                            @if($ad->media->count() > 1)
                                                <div class="image-count">
                                                    <i class="fas fa-images"></i>
                                                    {{ $ad->media->count() }}
                                                </div>
                                            @endif
                                        </div>

                                        <div class="ad-content">
                                            <h5 class="ad-title">
                                                <a href="{{ route('auction-details', $ad->slug) }}">{{ $ad->title }}</a>
                                            </h5>
                                            <div class="ad-price">{{ money($ad->price) }}</div>
                                            <div class="ad-meta">
                                                <span class="ad-category">
                                                    <i class="fas fa-tag"></i>
                                                    {{ $ad->category->name ?? 'Uncategorized' }}
                                                </span>
                                                <span class="ad-seller">
                                                    <i class="fas fa-user"></i>
                                                    Original seller: {{ $ad->seller_name }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Representative Deals Summary Section (Only for Brokers) -->
                @if(($user->is_broker ?? false) && $deals->count() > 0)
                    <div class="content-section">
                        <div class="section-header">
                            <h4 class="section-title">
                                <i class="fas fa-chart-line"></i>
                                Deals Summary
                            </h4>
                            <p class="section-subtitle">Recent completed deals and transactions</p>
                        </div>
                        
                        <div class="deals-list">
                            @foreach($deals as $deal)
                                @if($deal->ad)
                                    <div class="deal-item">
                                        <div class="deal-image">
                                            @if($deal->ad->media->count() > 0)
                                                <img src="{{ $deal->ad->media->first()->url }}" alt="{{ $deal->ad->title }}">
                                            @else
                                                <div class="no-image">
                                                    <i class="fas fa-image"></i>
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <div class="deal-content">
                                            <h6 class="deal-title">{{ $deal->ad->title }}</h6>
                                            <div class="deal-price">{{ money($deal->ad->price) }}</div>
                                            <div class="deal-meta">
                                                <span class="deal-category">
                                                    <i class="fas fa-tag"></i>
                                                    {{ $deal->ad->category->name ?? 'Uncategorized' }}
                                                </span>
                                                <span class="deal-date">
                                                    <i class="fas fa-handshake"></i>
                                                    Deal completed {{ $deal->created_at->diffForHumans() }}
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="deal-status">
                                            <span class="badge badge-success">
                                                <i class="fas fa-check"></i>
                                                Completed
                                            </span>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Recent Ratings Section -->
                @if($recentRatings->count() > 0)
                    <div class="content-section">
                        <div class="section-header">
                            <h4 class="section-title">
                                <i class="fas fa-star"></i>
                                Recent Reviews
                            </h4>
                            <p class="section-subtitle">What others are saying about {{ $user->name }}</p>
                        </div>

                        <div class="ratings-list">
                            @foreach($recentRatings as $rating)
                                <div class="rating-item">
                                    <div class="rating-header">
                                        <div class="rater-info">
                                            <div class="rater-avatar">
                                                <img src="{{ $rating->rater->avatar ?? get_random_avatar() }}" alt="{{ $rating->rater->name }}">
                                            </div>
                                            <div class="rater-details">
                                                <h6 class="rater-name">{{ $rating->rater->name }}</h6>
                                                <span class="rating-date">{{ $rating->created_at->diffForHumans() }}</span>
                                            </div>
                                        </div>
                                        <div class="rating-stars">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $rating->rating ? 'filled' : '' }}"></i>
                                            @endfor
                                            <span class="rating-value">{{ $rating->rating }}/5</span>
                                        </div>
                                    </div>
                                    @if($rating->comment)
                                        <div class="rating-comment">
                                            <p>"{{ $rating->comment }}"</p>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>

                        @if($user->total_ratings > 6)
                            <div class="ratings-footer">
                                <a href="{{ route('rate-user', $user->id) }}" class="btn btn-outline">
                                    <i class="fas fa-eye"></i>
                                    View All {{ $user->total_ratings }} Reviews
                                </a>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* نفس تصميم Dashboard و Profile */

/* Hero Section */
.profile-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 0 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.profile-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.hero-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.hero-breadcrumb .breadcrumb-item.active {
    color: white;
}

.hero-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Main Section */
.profile-main-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

/* User Info Card */
.user-info-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.user-info-card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.user-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 1.5rem;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e9ecef;
}

.verified-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    border: 3px solid white;
}

.verified-badge-blue {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    animation: verifiedPulse 2s infinite;
}

@keyframes verifiedPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.5);
    }
}

.unverified-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    border: 3px solid white;
}

.user-name {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.user-badges {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

.badge-broker {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.badge-rank {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #8b4513;
}

/* رتب مختلفة بألوان مميزة */
.badge-rank.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #8b4513;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.badge-rank.rank-2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.badge-rank.rank-3 {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

.badge-rank.rank-4 {
    background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(142, 68, 173, 0.3);
}

.badge-verified {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.badge-unverified {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
}

.badge-level {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2c3e50;
}

.broker-rating {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    text-align: center;
}

.rating-stars {
    display: flex;
    justify-content: center;
    gap: 0.125rem;
    margin-bottom: 0.25rem;
}

.rating-stars i {
    font-size: 0.875rem;
    color: #dee2e6;
}

.rating-stars i.filled {
    color: #ffc107;
}

.rating-text {
    font-size: 0.75rem;
    color: #667eea;
    font-weight: 600;
}

.user-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
    text-align: left;
}

.stat-value {
    display: block;
    font-weight: 700;
    color: #2c3e50;
    font-size: 1rem;
}

.stat-label {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    display: flex;
    gap: 0.125rem;
}

.stars i {
    font-size: 0.75rem;
    color: #dee2e6;
    transition: color 0.3s ease;
}

.stars i.filled {
    color: #ffc107;
}

.user-info {
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #495057;
    font-size: 0.875rem;
}

.info-item i {
    color: #667eea;
    width: 16px;
    text-align: center;
}

.contact-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.contact-actions .btn {
    width: 100%;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.contact-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.contact-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.contact-actions .btn-secondary {
    background: transparent;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

.contact-actions .btn-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
}

/* Content Sections */
.content-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.content-section:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.section-title {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #667eea;
}

.section-subtitle {
    color: #6c757d;
    margin: 0;
    font-size: 0.875rem;
}

/* Ads Grid */
.ads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.ad-card {
    background: #f8f9fa;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.ad-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.ad-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ad-card:hover .ad-image img {
    transform: scale(1.05);
}

.brokered-ad {
    border-left: 4px solid #667eea;
}

.brokered-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ad-seller {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.75rem;
    font-style: italic;
}

.ad-seller i {
    color: #667eea;
    width: 12px;
    font-size: 0.7rem;
}

.no-image {
    width: 100%;
    height: 100%;
    background: #e9ecef;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    gap: 0.5rem;
}

.no-image i {
    font-size: 2rem;
    opacity: 0.5;
}

.no-image span {
    font-size: 0.875rem;
    opacity: 0.7;
}

.image-count {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.ad-content {
    padding: 1rem;
}

.ad-title {
    margin: 0 0 0.5rem 0;
    font-size: 0.95rem;
    font-weight: 600;
    line-height: 1.3;
}

.ad-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.ad-title a:hover {
    color: #667eea;
}

.ad-price {
    font-size: 1rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 0.75rem;
}

.ad-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.ad-category,
.ad-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.8rem;
}

.ad-category i,
.ad-date i {
    color: #667eea;
    width: 12px;
    font-size: 0.75rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.empty-icon i {
    font-size: 1.5rem;
    color: white;
}

.empty-state h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

/* Deals List */
.deals-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.deal-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.deal-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.deal-image {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.deal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.deal-image .no-image {
    background: #dee2e6;
    font-size: 1.5rem;
}

.deal-content {
    flex: 1;
}

.deal-title {
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
}

.deal-price {
    font-size: 0.9rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.deal-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.deal-category,
.deal-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.75rem;
}

.deal-category i,
.deal-date i {
    color: #667eea;
    width: 12px;
}

.deal-status {
    flex-shrink: 0;
}

.badge-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

/* Ratings Section */
.ratings-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.rating-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border-left: 4px solid #ffc107;
}

.rating-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.rating-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.rater-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rater-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.rater-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.rater-details {
    display: flex;
    flex-direction: column;
}

.rater-name {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.rating-date {
    font-size: 0.8rem;
    color: #6c757d;
}

.rating-stars {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.rating-stars i {
    font-size: 0.875rem;
    color: #dee2e6;
}

.rating-stars i.filled {
    color: #ffc107;
}

.rating-value {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #2c3e50;
}

.rating-comment {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    border-left: 3px solid #667eea;
}

.rating-comment p {
    margin: 0;
    color: #495057;
    font-style: italic;
    line-height: 1.5;
}

.ratings-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px solid #f0f0f0;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-hero-section {
        padding: 1.5rem 0 1rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .profile-main-section {
        padding: 2rem 0;
    }
    
    .user-info-card,
    .content-section {
        padding: 1.5rem;
    }
    
    .ads-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .deal-item {
        flex-direction: column;
        text-align: center;
    }
    
    .deal-image {
        width: 80px;
        height: 80px;
    }

    .rating-display {
        flex-direction: column;
        gap: 0.25rem;
    }

    .broker-rating {
        margin-top: 0.75rem;
    }

    .rating-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .rating-stars {
        align-self: flex-end;
    }

    .contact-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Premium Rank Cards */
.premium-rank-card {
    margin: 1.5rem 0;
    border-radius: 16px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 2px solid transparent;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.premium-rank-card.vip-member {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #f59e0b 100%);
    border-color: #d97706;
    color: #92400e;
}

.premium-rank-card.trader {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #1d4ed8 100%);
    border-color: #1e40af;
    color: white;
}

.premium-rank-card.company {
    background: linear-gradient(135deg, #10b981 0%, #34d399 50%, #**********%);
    border-color: #047857;
    color: white;
}

.premium-rank-card.premium {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 50%, #7c3aed 100%);
    border-color: #6d28d9;
    color: white;
}

.premium-rank-card.regular {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 50%, #4b5563 100%);
    border-color: #374151;
    color: white;
}

.rank-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.rank-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.rank-title {
    font-size: 1rem;
    font-weight: 700;
    letter-spacing: 1px;
    flex: 1;
}

.rank-sparkles {
    display: flex;
    gap: 0.25rem;
    opacity: 0.8;
}

.rank-sparkles i {
    font-size: 0.8rem;
    animation: sparkle 2s ease-in-out infinite;
}

.rank-sparkles i:nth-child(2) {
    animation-delay: 0.5s;
}

.rank-sparkles i:nth-child(3) {
    animation-delay: 1s;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.rank-description {
    text-align: left;
}

.rank-subtitle {
    font-size: 0.85rem;
    opacity: 0.9;
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.rank-features {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.rank-features span {
    font-size: 0.75rem;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rank-features i {
    font-size: 0.7rem;
    width: 12px;
}

/* VIP Member specific styles */
.premium-rank-card.vip-member .rank-header {
    position: relative;
}

.premium-rank-card.vip-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706, #f59e0b, #fbbf24);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
</style>
@endpush

<!-- Include Report Modal -->
@include('components.report-modal')

@endsection
