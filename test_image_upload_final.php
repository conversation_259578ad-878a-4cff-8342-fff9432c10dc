<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 Testing Final Image Upload Fixes\n";
echo "===================================\n\n";

try {
    // Test 1: Check if services work without advanced optimizer
    echo "1. Testing Basic Services:\n";
    
    $compressionService = new \App\Services\SimpleFileCompressionService();
    echo "   ✅ SimpleFileCompressionService: OK\n";
    
    // Test validation with larger file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_image_');
    file_put_contents($tempFile, str_repeat('x', 15 * 1024 * 1024)); // 15MB fake file
    
    $fakeFile = new \Illuminate\Http\UploadedFile(
        $tempFile,
        'test_image.jpg',
        'image/jpeg',
        null,
        true
    );
    
    $validation = $compressionService->validateImage($fakeFile, 25);
    echo "   ✅ 15MB file validation: " . ($validation['valid'] ? 'PASSED' : 'FAILED') . "\n";
    
    unlink($tempFile);
    echo "\n";

    // Test 2: Check Media model URL accessor
    echo "2. Testing Media Model URL Accessor:\n";
    
    // Create a test media record
    $media = new \App\Models\Media();
    $media->path = 'test/image.jpg';
    $media->url = 'test/image.jpg';
    
    $url = $media->url; // This should trigger the accessor
    echo "   ✅ Media URL accessor: " . (str_contains($url, 'storage') ? 'WORKING' : 'NEEDS_CHECK') . "\n";
    echo "   📄 Generated URL: {$url}\n";
    echo "\n";

    // Test 3: Check compression settings
    echo "3. Testing Compression Settings:\n";
    
    $imageTypes = ['ad_images', 'profile_avatar', 'identity_documents', 'broker_documents'];
    
    foreach ($imageTypes as $type) {
        $settings = $compressionService->getOptimizedSettings($type);
        echo "   📊 {$type}:\n";
        echo "      - Target size: " . round($settings['max_file_size']/1024, 0) . "KB\n";
        echo "      - Quality: {$settings['quality']}%\n";
        echo "      - Format: {$settings['format']}\n";
        echo "      - Dimensions: {$settings['max_width']}x{$settings['max_height']}\n";
    }
    echo "\n";

    // Test 4: Check storage directories
    echo "4. Testing Storage Setup:\n";
    
    $directories = ['ad', 'avatars', 'identity-verification', 'broker-documents'];
    foreach ($directories as $dir) {
        $path = storage_path("app/public/{$dir}");
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
            echo "   ✅ Created directory: {$dir}\n";
        } else {
            echo "   ✅ Directory exists: {$dir}\n";
        }
    }
    echo "\n";

    // Test 5: Check file permissions
    echo "5. Testing File Permissions:\n";
    
    $storagePath = storage_path('app/public');
    if (is_writable($storagePath)) {
        echo "   ✅ Storage directory is writable\n";
    } else {
        echo "   ❌ Storage directory is NOT writable\n";
    }
    
    $publicPath = public_path('storage');
    if (is_link($publicPath) || is_dir($publicPath)) {
        echo "   ✅ Storage link exists\n";
    } else {
        echo "   ⚠️  Storage link missing - run: php artisan storage:link\n";
    }
    echo "\n";

    // Test 6: Check PHP settings
    echo "6. Testing PHP Configuration:\n";
    
    $settings = [
        'memory_limit' => ini_get('memory_limit'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'max_execution_time' => ini_get('max_execution_time')
    ];
    
    foreach ($settings as $setting => $value) {
        echo "   📋 {$setting}: {$value}\n";
    }
    echo "\n";

    // Test 7: Check Intervention Image
    echo "7. Testing Image Processing:\n";
    
    try {
        $manager = new \Intervention\Image\ImageManager(['driver' => 'gd']);
        echo "   ✅ Intervention Image (GD): Available\n";
    } catch (\Exception $e) {
        echo "   ❌ Intervention Image (GD): " . $e->getMessage() . "\n";
    }
    
    // Test WebP support
    if (function_exists('imagewebp')) {
        echo "   ✅ WebP encoding: Available\n";
    } else {
        echo "   ⚠️  WebP encoding: Not available (will use JPEG)\n";
    }
    echo "\n";

    // Test 8: Simulate image compression
    echo "8. Testing Image Compression Simulation:\n";
    
    // Create a small test image
    $testImagePath = tempnam(sys_get_temp_dir(), 'test_img_') . '.jpg';
    
    // Create a simple 100x100 red image
    $image = imagecreate(100, 100);
    $red = imagecolorallocate($image, 255, 0, 0);
    imagefill($image, 0, 0, $red);
    imagejpeg($image, $testImagePath, 90);
    imagedestroy($image);
    
    $testFile = new \Illuminate\Http\UploadedFile(
        $testImagePath,
        'test.jpg',
        'image/jpeg',
        null,
        true
    );
    
    $settings = $compressionService->getOptimizedSettings('ad_images');
    $result = $compressionService->compressAndStore($testFile, 'test-compression', $settings);
    
    if ($result['success']) {
        echo "   ✅ Image compression test: SUCCESS\n";
        echo "   📊 Original size: " . $compressionService->formatFileSize($result['original_size']) . "\n";
        echo "   📊 Compressed size: " . $compressionService->formatFileSize($result['compressed_size']) . "\n";
        echo "   📊 Compression ratio: {$result['compression_ratio']}%\n";
        echo "   📊 Format: {$result['format']}\n";
        
        // Clean up test file
        \Illuminate\Support\Facades\Storage::disk('public')->delete($result['path']);
    } else {
        echo "   ❌ Image compression test: FAILED - " . ($result['error'] ?? 'Unknown error') . "\n";
    }
    
    unlink($testImagePath);
    echo "\n";

    echo "🎉 All Tests Completed!\n";
    echo "======================\n\n";

    echo "📋 Summary of Current Status:\n";
    echo "✅ Basic compression service working\n";
    echo "✅ File size limits increased to 25MB\n";
    echo "✅ Media URL accessor implemented\n";
    echo "✅ Storage directories ready\n";
    echo "✅ Fallback to basic compression (no advanced optimizer issues)\n\n";

    echo "🚀 What Should Work Now:\n";
    echo "1. Profile avatar uploads (compressed to ~80KB)\n";
    echo "2. Identity document uploads (compressed to ~400KB)\n";
    echo "3. Broker document uploads (compressed to ~400KB)\n";
    echo "4. Ad image uploads (compressed to ~200KB)\n";
    echo "5. All images should display properly in the frontend\n\n";

    echo "🔍 If Issues Persist:\n";
    echo "1. Check Laravel logs: tail -f storage/logs/laravel.log\n";
    echo "2. Check browser console for JavaScript errors\n";
    echo "3. Verify storage link: php artisan storage:link\n";
    echo "4. Check file permissions: chmod -R 755 storage/\n";
    echo "5. Clear cache: php artisan cache:clear\n";

} catch (\Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n✅ Testing completed!\n";
