<?php

namespace App\Services;

use App\Models\Ad;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class RelatedAdsService
{
    public function getRelatedAdsWithSponsored(Ad $currentAd, int $limit = 24): EloquentCollection
    {
        // زيادة العدد الافتراضي وتحسين التوزيع
        $relatedAds = $this->getRelatedAds($currentAd, $limit);
        $sponsoredAds = $this->getSponsoredAds($currentAd, min(8, intval($limit / 3))); // حد أقصى 8 إعلانات ممولة

        return $this->mergeRelatedWithSponsored($relatedAds, $sponsoredAds, $limit);
    }

    public function getRelatedAds(Ad $currentAd, int $limit = 24): EloquentCollection
    {
        return Cache::remember("related_ads_{$currentAd->id}_{$limit}", now()->addMinutes(15), function () use ($currentAd, $limit) {
            $relatedAds = collect();

            // 1. نفس الفئة الفرعية (أولوية عالية) - 40%
            $categoryAds = $this->getAdsByCategory($currentAd, (int) ($limit * 0.4));
            $relatedAds = $relatedAds->merge($categoryAds);

            // 2. نفس الفئة الرئيسية - 25%
            $parentCategoryAds = $this->getAdsByParentCategory($currentAd, (int) ($limit * 0.25), $relatedAds->pluck('id'));
            $relatedAds = $relatedAds->merge($parentCategoryAds);

            // 3. نفس المنطقة - 15%
            $locationAds = $this->getAdsByLocation($currentAd, (int) ($limit * 0.15), $relatedAds->pluck('id'));
            $relatedAds = $relatedAds->merge($locationAds);

            // 4. نفس النطاق السعري - 10%
            $priceRangeAds = $this->getAdsByPriceRange($currentAd, (int) ($limit * 0.1), $relatedAds->pluck('id'));
            $relatedAds = $relatedAds->merge($priceRangeAds);

            // 5. إعلانات مشابهة بالخصائص - 10%
            $remainingLimit = $limit - $relatedAds->count();
            if ($remainingLimit > 0) {
                $attributeAds = $this->getAdsByAttributes($currentAd, $remainingLimit, $relatedAds->pluck('id'));
                $relatedAds = $relatedAds->merge($attributeAds);
            }

            // ترتيب ذكي: الأحدث أولاً مع خلط خفيف للتنويع
            return new EloquentCollection(
                $relatedAds->unique('id')
                          ->sortByDesc('created_at')
                          ->take($limit)
                          ->shuffle()
                          ->values()
            );
        });
    }

    private function getAdsByCategory(Ad $currentAd, int $limit): EloquentCollection
    {
        return Ad::query()
            ->where('id', '!=', $currentAd->id)
            ->where('sub_category_id', $currentAd->sub_category_id)
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->with(['media', 'category', 'user:id,name,is_trusted', 'sponsoredAd'])
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getAdsByParentCategory(Ad $currentAd, int $limit, $excludeIds): EloquentCollection
    {
        return Ad::query()
            ->where('id', '!=', $currentAd->id)
            ->where('category_id', $currentAd->category_id)
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->whereNotIn('id', $excludeIds)
            ->with(['media', 'category', 'user:id,name,is_trusted', 'sponsoredAd'])
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getAdsByLocation(Ad $currentAd, int $limit, $excludeIds): EloquentCollection
    {
        return Ad::query()
            ->where('id', '!=', $currentAd->id)
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->whereNotIn('id', $excludeIds)
            ->where(function($query) use ($currentAd) {
                // نفس المدينة أولاً، ثم نفس المحافظة، ثم نفس الدولة
                $query->where('city_id', $currentAd->city_id)
                      ->orWhere('state_id', $currentAd->state_id)
                      ->orWhere('country_id', $currentAd->country_id);
            })
            ->with(['media', 'category', 'user:id,name,is_trusted', 'sponsoredAd'])
            ->orderByRaw("CASE
                WHEN city_id = ? THEN 1
                WHEN state_id = ? THEN 2
                WHEN country_id = ? THEN 3
                ELSE 4 END", [$currentAd->city_id, $currentAd->state_id, $currentAd->country_id])
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getAdsByPriceRange(Ad $currentAd, int $limit, $excludeIds): EloquentCollection
    {
        if ($currentAd->price <= 0) {
            return new EloquentCollection();
        }

        return Ad::query()
            ->where('id', '!=', $currentAd->id)
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->whereNotIn('id', $excludeIds)
            ->whereBetween('price', [
                $currentAd->price * 0.7, // نطاق أوسع للحصول على نتائج أكثر
                $currentAd->price * 1.5
            ])
            ->with(['media', 'category', 'user:id,name,is_trusted', 'sponsoredAd'])
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getAdsByAttributes(Ad $currentAd, int $limit, $excludeIds): EloquentCollection
    {
        // البحث بناءً على خصائص الإعلان المشتركة
        $currentAttributes = $currentAd->attributes()->pluck('attribute_value', 'attribute_name');

        if ($currentAttributes->isEmpty()) {
            return new EloquentCollection();
        }

        return Ad::query()
            ->where('id', '!=', $currentAd->id)
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->whereNotIn('id', $excludeIds)
            ->whereHas('attributes', function($query) use ($currentAttributes) {
                $query->whereIn('attribute_name', $currentAttributes->keys()->toArray())
                      ->whereIn('attribute_value', $currentAttributes->values()->toArray());
            })
            ->with(['media', 'category', 'user:id,name,is_trusted', 'sponsoredAd'])
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getSponsoredAds(Ad $currentAd, int $limit): EloquentCollection
    {
        return Ad::query()
            ->whereHas('sponsoredAd', function ($query) {
                $query->where('is_active', true)
                      ->where('status', 'active')
                      ->where('expires_at', '>', now()); // فحص مضاعف للأمان
            })
            ->where('status', \App\Enums\AdStatus::PUBLISHED)
            ->where('id', '!=', $currentAd->id)
            ->where(function($query) use ($currentAd) {
                // البحث في نفس الفئة أو الفئات المرتبطة
                $query->where('category_id', $currentAd->category_id)
                      ->orWhere('sub_category_id', $currentAd->sub_category_id);
            })
            ->with(['category', 'user:id,name,is_trusted', 'media', 'sponsoredAd'])
            ->orderByDesc(function ($q) {
                // ترتيب حسب أولوية الرعاية
                $q->select('priority')
                  ->from('sponsored_ads')
                  ->whereColumn('sponsored_ads.ad_id', 'ads.id')
                  ->limit(1);
            })
            ->orderByDesc(function ($q) {
                // ثم حسب التكلفة
                $q->select('cost')
                  ->from('sponsored_ads')
                  ->whereColumn('sponsored_ads.ad_id', 'ads.id')
                  ->limit(1);
            })
            ->take($limit)
            ->get();
    }

    private function mergeRelatedWithSponsored(EloquentCollection $relatedAds, EloquentCollection $sponsoredAds, int $totalLimit): EloquentCollection
    {
        if ($sponsoredAds->isEmpty()) {
            return new EloquentCollection($relatedAds->take($totalLimit)->values());
        }

        $merged = collect();
        $relatedIndex = 0;
        $sponsoredIndex = 0;

        // توزيع أفضل: إعلان ممول كل 5 إعلانات عادية
        for ($i = 0; $i < $totalLimit; $i++) {
            // إضافة إعلان ممول في المواضع: 3, 8, 13, 18, إلخ
            if (($i + 1) % 5 === 3 && $sponsoredIndex < $sponsoredAds->count()) {
                $merged->push($sponsoredAds[$sponsoredIndex++]);
            } elseif ($relatedIndex < $relatedAds->count()) {
                $merged->push($relatedAds[$relatedIndex++]);
            } elseif ($sponsoredIndex < $sponsoredAds->count()) {
                // إذا انتهت الإعلانات العادية، أضف الممولة المتبقية
                $merged->push($sponsoredAds[$sponsoredIndex++]);
            }
        }

        return new EloquentCollection($merged->values());
    }
}
