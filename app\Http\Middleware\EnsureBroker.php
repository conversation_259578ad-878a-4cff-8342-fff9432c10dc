<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureBroker
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // التحقق من تسجيل الدخول
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً.');
        }

        // التحقق من أن المستخدم مندوب
        if (!($user->is_broker ?? false)) {
            return redirect()->route('home')->with('error', 'هذه الصفحة مخصصة للمندوبين فقط.');
        }

        return $next($request);
    }
}
