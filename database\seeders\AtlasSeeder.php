<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AtlasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Insert countries into the database
        $this->command->info('Seeding countries...');
        $countries_sql = file_get_contents(database_path('dump/countries.sql'));
        DB::unprepared($countries_sql);
        $this->command->info('Seeding countries completed.');

        // Insert timezones into the database
        $this->command->info('Seeding timezones...');
        $timezones_sql = file_get_contents(database_path('dump/timezones.sql'));
        DB::unprepared($timezones_sql);
        $this->command->info('Seeding timezones completed.');

        // Insert states into the database
        $this->command->info('Seeding states...');
        $states_sql = file_get_contents(database_path('dump/states.sql'));
        DB::unprepared($states_sql);
        $this->command->info('Seeding states completed.');

        // Insert cities into the database (بالتقسيم لتجنب المشاكل)
        $this->command->info('Seeding cities...');
        
        // عدد الملفات بعد التقسيم (غير الرقم حسب عدد الأجزاء)
        $totalParts = 10;

        for ($i = 1; $i <= $totalParts; $i++) {
            $partFile = database_path("dump/cities_part{$i}.sql");
            if (file_exists($partFile)) {
                DB::unprepared(file_get_contents($partFile));
                $this->command->info("Seeding cities part {$i} completed.");
            } else {
                $this->command->info("Warning: cities_part{$i}.sql not found, skipping.");
            }
        }

        $this->command->info('Seeding cities completed.');
    }
}
