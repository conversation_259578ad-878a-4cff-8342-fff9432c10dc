<?php

namespace App\Services\Media;

use App\Abstracts\BaseMediaStorageService;
use App\Contracts\Services\MediaStorageServiceInterface;
use App\Enums\StorageDiskType;
use Illuminate\Http\UploadedFile;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManagerStatic as ImageManager;
use Illuminate\Support\Facades\Log;

class LocalStorage extends BaseMediaStorageService implements MediaStorageServiceInterface
{
public function __construct()
    {
        parent::__construct(StorageDiskType::LOCAL);
    }

    /**
     * Resize image.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param int $width
     * @param int $height
     * @return void
     */
    protected function resizeImage(UploadedFile $file, string $directory, int $width, int $height): void
    {
        // حفظ الصورة بدون معالجة للسرعة وتجنب مشاكل ImageMagick/GD
        $directory = preg_replace('/^public/', '', $directory);

        // تأكد من وجود المجلد
        $fullPath = public_path('storage' . $directory);
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // حفظ الصورة الأصلية
        $filename = $file->hashName();
        $savePath = $fullPath . '/' . $filename;

        // نسخ الملف مباشرة
        if (copy($file->getRealPath(), $savePath)) {
            Log::info('Image saved successfully', [
                'path' => $savePath,
                'size' => filesize($savePath),
                'original_name' => $file->getClientOriginalName()
            ]);
        } else {
            Log::error('Failed to copy image file', [
                'file' => $file->getClientOriginalName(),
                'directory' => $directory
            ]);
            throw new \Exception('Failed to save image file');
        }
    }
}
