<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use App\Models\Ad;
use App\Models\ProductReview;

class ProductReviewController extends Controller
{
    /**
     * Store a new product review.
     *
     * @param Request $request
     * @param string $adId
     * @return RedirectResponse
     */
    public function store(Request $request, string $adId): RedirectResponse
    {
        $reviewer = Auth::user();

        // التحقق من تسجيل الدخول
        if (!$reviewer) {
            return back()->withErrors(['message' => 'يجب تسجيل الدخول أولاً.']);
        }

        // التحقق من أن الحساب موثوق
        if (!$reviewer->is_trusted) {
            return back()->withErrors(['message' => 'حسابك غير موثوق ولا يمكنك التقييم.']);
        }

        // العثور على الإعلان
        $ad = Ad::findOrFail($adId);

        // التحقق من عدم تقييم منتجه الخاص
        if ($reviewer->id === $ad->user_id) {
            return back()->withErrors(['message' => 'لا يمكنك تقييم منتجك الخاص.']);
        }

        // التحقق من وجود تقييم مسبق
        $existingReview = ProductReview::where('reviewer_id', $reviewer->id)
                                      ->where('ad_id', $adId)
                                      ->first();

        if ($existingReview) {
            return back()->withErrors(['message' => 'لقد قمت بتقييم هذا المنتج من قبل.']);
        }

        // التحقق من صحة البيانات
        $validated = $request->validate([
            'rating' => 'required|numeric|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        // حفظ التقييم
        $newReview = new ProductReview();
        $newReview->reviewer_id = $reviewer->id;
        $newReview->ad_id = $ad->id;
        $newReview->rating = round($validated['rating'], 1);
        $newReview->comment = $validated['comment'] ?? null;
        $newReview->save();

        return redirect()->route('auction-details', $ad->slug)->with('success', 'تم إضافة التقييم بنجاح.');
    }

    /**
     * Get reviews for a specific ad (API endpoint).
     *
     * @param string $adId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReviews(string $adId)
    {
        $ad = Ad::findOrFail($adId);

        $reviews = ProductReview::where('ad_id', $adId)
                               ->with('reviewer:id,name,avatar,is_trusted')
                               ->orderBy('created_at', 'desc')
                               ->get();

        return response()->json([
            'success' => true,
            'reviews' => $reviews->map(function ($review) {
                return [
                    'id' => $review->id,
                    'rating' => $review->rating,
                    'comment' => $review->comment,
                    'created_at' => $review->created_at->format('Y-m-d H:i'),
                    'reviewer' => [
                        'name' => $review->reviewer->name,
                        'avatar' => $review->reviewer->avatar ?? get_random_avatar(),
                        'is_trusted' => $review->reviewer->is_trusted,
                    ],
                ];
            }),
            'average_rating' => $ad->average_rating,
            'total_reviews' => $ad->total_reviews,
        ]);
    }
}
