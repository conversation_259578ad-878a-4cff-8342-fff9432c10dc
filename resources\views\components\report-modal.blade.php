<!-- Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">
                    <i class="fas fa-flag text-danger me-2"></i>
                    إبلاغ عن محتوى غير مناسب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="reportForm">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم مراجعة إبلاغك من قبل فريق الإدارة. يرجى اختيار السبب المناسب وتقديم تفاصيل إضافية إذا لزم الأمر.
                    </div>

                    <div class="mb-3">
                        <label for="reportReason" class="form-label">سبب الإبلاغ *</label>
                        <select class="form-select" id="reportReason" name="reason" required>
                            <option value="">اختر السبب</option>
                            <option value="spam">محتوى مزعج أو غير مرغوب فيه</option>
                            <option value="inappropriate">محتوى غير لائق</option>
                            <option value="fake">محتوى مزيف أو مضلل</option>
                            <option value="harassment">تحرش أو إساءة</option>
                            <option value="violence">عنف أو تهديد</option>
                            <option value="copyright">انتهاك حقوق الطبع والنشر</option>
                            <option value="fraud">احتيال أو نصب</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="reportDescription" class="form-label">تفاصيل إضافية (اختياري)</label>
                        <textarea class="form-control" id="reportDescription" name="description" rows="4" 
                                  placeholder="يرجى تقديم تفاصيل إضافية حول سبب الإبلاغ..."></textarea>
                        <div class="form-text">الحد الأقصى: 1000 حرف</div>
                    </div>

                    <input type="hidden" id="reportableType" name="reportable_type">
                    <input type="hidden" id="reportableId" name="reportable_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-flag me-2"></i>إرسال الإبلاغ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.report-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.report-btn:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.report-btn i {
    margin-right: 0.25rem;
}

.report-btn-sm {
    font-size: 0.75rem;
    padding: 0.125rem 0.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const reportModal = document.getElementById('reportModal');
    const reportForm = document.getElementById('reportForm');
    
    // Handle report button clicks
    document.addEventListener('click', function(e) {
        if (e.target.closest('.report-btn')) {
            e.preventDefault();
            const btn = e.target.closest('.report-btn');
            const type = btn.dataset.type;
            const id = btn.dataset.id;
            
            if (type && id) {
                openReportModal(type, id);
            }
        }
    });
    
    // Open report modal
    function openReportModal(type, id) {
        document.getElementById('reportableType').value = type;
        document.getElementById('reportableId').value = id;
        
        // Reset form
        reportForm.reset();
        document.getElementById('reportableType').value = type;
        document.getElementById('reportableId').value = id;
        
        // Show modal
        const modal = new bootstrap.Modal(reportModal);
        modal.show();
    }
    
    // Handle form submission
    reportForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
        
        fetch('/reports', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showAlert('success', data.message);
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(reportModal);
                modal.hide();
                
                // Reset form
                reportForm.reset();
            } else {
                showAlert('danger', data.message || 'حدث خطأ أثناء إرسال الإبلاغ');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ أثناء إرسال الإبلاغ. يرجى المحاولة مرة أخرى.');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
    
    // Show alert function
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Find a container to show the alert
        let container = document.querySelector('.alert-container');
        if (!container) {
            container = document.querySelector('.container, .container-fluid, main, body');
            if (container) {
                const alertContainer = document.createElement('div');
                alertContainer.className = 'alert-container position-fixed top-0 start-50 translate-middle-x mt-3';
                alertContainer.style.zIndex = '9999';
                container.appendChild(alertContainer);
                container = alertContainer;
            }
        }
        
        if (container) {
            container.innerHTML = alertHtml;
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }
});
</script>
