<?php

namespace App\Http\Controllers\Admin\Ad;

use App\Contracts\Repositories\AdminAdRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\Ad\FilterAdminAdsRequest;
use App\Http\Requests\Ad\UpdateAdAdminRequest;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class AdController extends Controller
{
    /**
     * Instantiate new controller instance
     */
    public function __construct(protected AdminAdRepositoryInterface $adminAdRepository)
    {
    }

    /**
     * Display a listing of the resource.
     *
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function index(FilterAdminAdsRequest $query): View
    {
        $filters = $query->validated();

        return view('ads.admin.index', [
            // Regular users with broker data
            'regularWithBrokerPendingAds' => $this->adminAdRepository->getAdsByUserType('regular_with_broker', 'pending', 10, $filters),
            'regularWithBrokerAllAds' => $this->adminAdRepository->getAdsByUserType('regular_with_broker', 'all', 10, $filters),
            'regularWithBrokerPendingCount' => $this->adminAdRepository->getAdsCountByUserType('regular_with_broker', 'pending'),
            'regularWithBrokerAllCount' => $this->adminAdRepository->getAdsCountByUserType('regular_with_broker', 'all'),

            // Regular users without broker data
            'regularWithoutBrokerPendingAds' => $this->adminAdRepository->getAdsByUserType('regular_without_broker', 'pending', 10, $filters),
            'regularWithoutBrokerAllAds' => $this->adminAdRepository->getAdsByUserType('regular_without_broker', 'all', 10, $filters),
            'regularWithoutBrokerPendingCount' => $this->adminAdRepository->getAdsCountByUserType('regular_without_broker', 'pending'),
            'regularWithoutBrokerAllCount' => $this->adminAdRepository->getAdsCountByUserType('regular_without_broker', 'all'),

            // VIP users data
            'vipPendingAds' => $this->adminAdRepository->getAdsByUserType('vip', 'pending', 10, $filters),
            'vipAllAds' => $this->adminAdRepository->getAdsByUserType('vip', 'all', 10, $filters),
            'vipPendingCount' => $this->adminAdRepository->getAdsCountByUserType('vip', 'pending'),
            'vipAllCount' => $this->adminAdRepository->getAdsCountByUserType('vip', 'all'),
        ]);
    }

    /**
     * Display a pending of the resource.
     * 
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function pending(FilterAdminAdsRequest $query): View
    {
        return view('ads.admin.index', [
            'ads' => $this->adminAdRepository->getAds(10, 'all', $query->validated())
        ]);
    }

    /**
     * Display a active of the resource.
     * 
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function active(FilterAdminAdsRequest $request): View
    {
        return view('ads.admin.status.active', [
            'ads' => $this->adminAdRepository->getAds(10, 'active', $request->validated())
        ]);
    }

    /**
     * Display a upcoming of the resource.
     * 
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function upcoming(FilterAdminAdsRequest $request): View
    {
        return view('ads.admin.status.upcoming', [
            'ads' => $this->adminAdRepository->getAds(10, 'upcoming', $request->validated())
        ]);
    }

    /**
     * Display a expired of the resource.
     * 
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function expired(FilterAdminAdsRequest $request): View
    {
        return view('ads.admin.status.expired', [
            'ads' => $this->adminAdRepository->getAds(10, 'expired', $request->validated())
        ]);
    }

    /**
     * Display a rejected of the resource.
     * 
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function rejected(FilterAdminAdsRequest $request): View
    {
        return view('ads.admin.status.rejected', [
            'ads' => $this->adminAdRepository->getAds(10, 'rejected', $request->validated())
        ]);
    }

    /**
     * Display a reported ad of the resource.
     * 
     * @param \App\Http\Requests\Ad\FilterAdminAdsRequest $query
     * @return \Illuminate\Contracts\View\View
     */
    public function reported(FilterAdminAdsRequest $request): View
    {
        return view('ads.admin.status.reported', [
            'reportedAds' => $this->adminAdRepository->getReportedAds(10, $request->validated())
        ]);
    }

    /**
     * Display a reported ad of the resource.
     * 
     * @param string $adSlug
     * @return \Illuminate\Contracts\View\View
     */
    public function reportAd(string $adSlug): View
    {
        return view('ads.admin.report', [
            'reportAd' => $this->adminAdRepository->getReportedAd($adSlug)
        ]);
    }



    /**
     * Display the specified resource.
     *
     * @param  string  $ad
     * @return \Illuminate\Contracts\View\View
     */
    public function show(string $adSlug): View
    {
        return view('ads.admin.show', [
            'ad' => $this->adminAdRepository->getAdBySlug($adSlug)
        ]);
    }

    /**
     * Edit the specified resource in storage.
     *
     * @param  string  $ad
     * @return \Illuminate\Contracts\View\View
     */
    public function edit(string $adSlug): View
    {
        return view('ads.admin.edit', [
            'ad' => $this->adminAdRepository->getAdBySlug($adSlug)
        ]);
    }

    /**
     * Update the specified resource in storage.
     * 
     * @param  string  $ad
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Requests\Ad\UpdateAdAdminRequest
     */
    public function update(string $adSlug, UpdateAdAdminRequest $request): RedirectResponse
    {
        $this->adminAdRepository->updateAd($adSlug, $request->validated());

        // إذا تم النشر، توجيه إلى صفحة الإعلانات
        if (isset($request->validated()['status']) && $request->validated()['status'] == \App\Enums\AdStatus::PUBLISHED->value) {
            return redirect()->route('admin.ads.index')->with('success', 'Ad published successfully.');
        }

        return redirect()->route('admin.ads.show', $adSlug)->with('success', 'Ad updated successfully.');
    }

    /**
     * Delete the specified resource in storage.
     * 
     * @param  string  $ad
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $adSlug): RedirectResponse
    {
        $this->adminAdRepository->deleteAd($adSlug);
        return redirect()->route('admin.ads.index', $adSlug)->with('success', 'Ad deleted successfully.');
    }
}
