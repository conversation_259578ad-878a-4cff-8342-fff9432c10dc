<?php

namespace App\View\Components;

use App\Enums\PaymentGateway;
use App\Models\Bid;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PayableForm extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(public Bid $bid)
    {
        //
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.payable-form', [
            'methods' => PaymentGateway::all(),
        ]);
    }
}
