<?php

use App\Http\Controllers\Api\CategoryController as ApiCategoryController;
use App\Http\Controllers\Api\CountryController as ApiCountryController;
use App\Http\Controllers\Api\SponsoredAdTrackingController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Category API Routes
Route::get('/categories', [ApiCategoryController::class, 'getRootCategories'])->name('categories.root');
Route::get('/categories/tree', [ApiCategoryController::class, 'getCategoryTree'])->name('categories.tree');
Route::get('/categories/{slug}', [ApiCategoryController::class, 'getCategoryWithChildren'])->name('categories.show');
Route::get('/categories/{slug}/children', [ApiCategoryController::class, 'getChildren'])->name('categories.children');
Route::get('/categories/{slug}/descendants', [ApiCategoryController::class, 'getDescendants'])->name('categories.descendants');
Route::get('/categories/{slug}/breadcrumb', [ApiCategoryController::class, 'getBreadcrumb'])->name('categories.breadcrumb');

// Related Ads API Routes (without CSRF protection)
Route::prefix('related-ads')->name('related-ads.')->group(function () {
    Route::post('/load-more', [\App\Http\Controllers\Api\RelatedAdsController::class, 'loadMore'])->name('load-more');
    Route::post('/search', [\App\Http\Controllers\Api\RelatedAdsController::class, 'search'])->name('search');
});

// Legacy routes (for backward compatibility)
Route::get('/subcategories/{slug}', [ApiCategoryController::class, 'getSubCategories'])->name('categories.sub-categories');

Route::get('/states/{iso2code}', [ApiCountryController::class, 'getStates'])->name('countries.states');
Route::get('/cities/{iso2code}/{stateCode}', [ApiCountryController::class, 'getCities'])->name('countries.cities');

// Sponsored Ads Tracking Routes
Route::prefix('sponsored-ads')->group(function () {
    Route::post('track-view', [SponsoredAdTrackingController::class, 'trackView']);
    Route::post('track-click', [SponsoredAdTrackingController::class, 'trackClick']);
    Route::post('{ad}/view', [SponsoredAdTrackingController::class, 'trackView']);
    Route::post('{ad}/click', [SponsoredAdTrackingController::class, 'trackClick']);
    Route::get('{ad}/stats', [SponsoredAdTrackingController::class, 'getStats']);
    Route::get('top-performing', [SponsoredAdTrackingController::class, 'getTopPerforming']);
});

// New nested set endpoints with attributes
Route::get('/nested-categories', function() {
    return response()->json([
        'success' => true,
        'data' => \App\Models\Category::whereIsRoot()->with('children')->get()
    ]);
});

Route::get('/category-attributes/{slug}', function($slug) {
    $category = \App\Models\Category::where('slug', $slug)->first();

    if (!$category) {
        return response()->json(['success' => false, 'message' => 'Category not found'], 404);
    }

    return response()->json([
        'success' => true,
        'data' => [
            'category' => $category,
            'attributes' => $category->attributes
        ]
    ]);
});