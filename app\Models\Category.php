<?php

namespace App\Models;

use App\Traits\HasSlug;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Kalnoy\Nestedset\NodeTrait;

class Category extends Model
{
    use HasFactory, HasSlug, NodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'icon',
        'image',
        'description',
    ];

    /**
     * Get the ads for the category.
     */
    public function ads()
    {
        return $this->hasMany(\App\Models\Ad::class);
    }

    public function attributes()
    {
        return $this->hasMany(CategoryAttribute::class)->orderBy('sort_order');
    }

    /**
     * Get all ads including from descendant categories.
     */
    public function allAds()
    {
        $categoryIds = $this->descendants()->pluck('id')->push($this->id);
        return \App\Models\Ad::whereIn('category_id', $categoryIds);
    }

    /**
     * Check if category has specific attributes/properties.
     */
    public function hasAttributes()
    {
        // This can be extended later to check for category-specific attributes
        return false;
    }

    /**
     * Get category attributes based on category type.
     */
    public function getAttributesForCategory()
    {
        // This will be used to return specific attributes for each category
        // For example: car attributes, real estate attributes, etc.
        return [];
    }
}
