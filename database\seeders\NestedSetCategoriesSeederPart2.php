<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class NestedSetCategoriesSeederPart2 extends Seeder
{
    public function createHomeFurnitureHierarchy()
    {
        // Create root category: Home & Office Furniture - Decor
        $homeFurniture = Category::create([
            'name' => 'أثاث منزلي ومكتبي - ديكور',
            'slug' => 'home-office-furniture-decor',
            'description' => 'أثاث منزلي ومكتبي وديكور',
            'icon' => 'assets/categories/icon/furniture.svg',
            'image' => 'assets/categories/bg/furniture.png'
        ]);
        
        $furnitureCategories = [
            'office-furniture' => 'أثاث مكتبي',
            'furniture' => 'أثاث',
            'home-decoration-accessories' => 'ديكور منزلي وإكسسوارات',
            'bathroom-kitchen-tools-accessories' => 'أدوات وإكسسوارات حمام ومطبخ',
            'fabric-bedding' => 'أقمشة وفراش',
            'garden-outdoor' => 'حديقة - خارجي',
            'lighting' => 'إضاءة',
            'multiple-other-furniture' => 'أثاث متنوع/أخرى'
        ];
        
        foreach ($furnitureCategories as $slug => $name) {
            $homeFurniture->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "🪑 Home & Office Furniture hierarchy created\n";
    }
    
    public function createElectronicsAppliancesHierarchy()
    {
        // Create root category: Electronics & Home Appliances
        $electronics = Category::create([
            'name' => 'إلكترونيات وأجهزة منزلية',
            'slug' => 'electronics-home-appliances',
            'description' => 'إلكترونيات وأجهزة منزلية',
            'icon' => 'assets/categories/icon/electronics.svg',
            'image' => 'assets/categories/bg/electronics.png'
        ]);
        
        $electronicsCategories = [
            'tv-audio-video' => 'تلفزيون - صوت - فيديو',
            'computers-accessories' => 'كمبيوتر - إكسسوارات',
            'video-games-consoles' => 'ألعاب فيديو - أجهزة ألعاب',
            'cameras-imaging' => 'كاميرات - تصوير',
            'home-appliances' => 'أجهزة منزلية'
        ];
        
        foreach ($electronicsCategories as $slug => $name) {
            $electronics->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "📺 Electronics & Appliances hierarchy created\n";
    }
    
    public function createFashionBeautyHierarchy()
    {
        // Create root category: Fashion & Beauty
        $fashion = Category::create([
            'name' => 'أزياء وجمال',
            'slug' => 'fashion-beauty',
            'description' => 'أزياء وجمال',
            'icon' => 'assets/categories/icon/fashion.svg',
            'image' => 'assets/categories/bg/fashion.png'
        ]);
        
        $fashionCategories = [
            'womens-clothing' => 'ملابس نسائية',
            'mens-clothing' => 'ملابس رجالية',
            'womens-accessories-cosmetics-personal-care' => 'إكسسوارات نسائية - مستحضرات تجميل - عناية شخصية',
            'mens-accessories-personal-care' => 'إكسسوارات رجالية - عناية شخصية'
        ];
        
        foreach ($fashionCategories as $slug => $name) {
            $fashion->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "👗 Fashion & Beauty hierarchy created\n";
    }
    
    public function createPetsHierarchy()
    {
        // Create root category: Pets - Birds - Ornamental fish
        $pets = Category::create([
            'name' => 'حيوانات أليفة - طيور - أسماك زينة',
            'slug' => 'pets-birds-ornamental-fish',
            'description' => 'حيوانات أليفة وطيور وأسماك زينة',
            'icon' => 'assets/categories/icon/pets.svg',
            'image' => 'assets/categories/bg/pets.png'
        ]);
        
        $petCategories = [
            'dogs' => 'كلاب',
            'cats' => 'قطط',
            'birds' => 'طيور',
            'ornamental-fish' => 'أسماك زينة',
            'horses' => 'خيول',
            'cows-sheep-camels' => 'أبقار وأغنام وجمال',
            'animal-birds-fish-dry-food' => 'طعام جاف للحيوانات والطيور والأسماك',
            'animal-bird-fish-care-tools-accessories' => 'أدوات وإكسسوارات رعاية الحيوانات والطيور والأسماك',
            'other-pets-animals' => 'حيوانات أليفة أخرى'
        ];
        
        foreach ($petCategories as $slug => $name) {
            $pets->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "🐕 Pets hierarchy created\n";
    }
    
    public function createKidsBabiesHierarchy()
    {
        // Create root category: Kids & Babies
        $kidsBabies = Category::create([
            'name' => 'أطفال ورضع',
            'slug' => 'kids-babies',
            'description' => 'مستلزمات الأطفال والرضع',
            'icon' => 'assets/categories/icon/baby.svg',
            'image' => 'assets/categories/bg/baby.png'
        ]);
        
        $babyCategories = [
            'baby-mom-healthcare' => 'رعاية صحية للطفل والأم',
            'baby-clothing' => 'ملابس أطفال',
            'baby-feeding-tools' => 'أدوات إطعام الأطفال',
            'baby-beds-strollers-carseats' => 'أسرة أطفال - عربات - مقاعد سيارة',
            'toys' => 'ألعاب',
            'other-baby-items' => 'مستلزمات أطفال أخرى',
            'baby-walkers-chairs-feeding-chairs' => 'مشايات أطفال - كراسي أطفال - كراسي إطعام'
        ];
        
        foreach ($babyCategories as $slug => $name) {
            $kidsBabies->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "👶 Kids & Babies hierarchy created\n";
    }
    
    public function createHobbiesHierarchy()
    {
        // Create root category: Books, Sports & Hobbies
        $hobbies = Category::create([
            'name' => 'كتب ورياضة وهوايات',
            'slug' => 'books-sports-hobbies',
            'description' => 'كتب ورياضة وهوايات',
            'icon' => 'assets/categories/icon/hobbies.svg',
            'image' => 'assets/categories/bg/hobbies.png'
        ]);
        
        $hobbyCategories = [
            'antiques-collectibles' => 'تحف - مقتنيات',
            'bicycles' => 'دراجات هوائية',
            'books' => 'كتب',
            'board-card-games' => 'ألعاب لوحية - ألعاب ورق',
            'movies-music' => 'أفلام - موسيقى',
            'musical-instruments' => 'آلات موسيقية',
            'sports-equipment' => 'معدات رياضية',
            'study-tools' => 'أدوات دراسية',
            'tickets-vouchers' => 'تذاكر - قسائم',
            'luggage' => 'حقائب سفر',
            'other-items' => 'أشياء أخرى'
        ];
        
        foreach ($hobbyCategories as $slug => $name) {
            $hobbies->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "📚 Hobbies hierarchy created\n";
    }
    
    public function createBusinessIndustrialHierarchy()
    {
        // Create root category: Business - Industrial - Agriculture
        $business = Category::create([
            'name' => 'أعمال - صناعي - زراعي',
            'slug' => 'business-industrial-agriculture',
            'description' => 'أعمال وصناعة وزراعة',
            'icon' => 'assets/categories/icon/business.svg',
            'image' => 'assets/categories/bg/business.png'
        ]);
        
        $businessCategories = [
            'agriculture' => 'زراعة',
            'construction' => 'إنشاءات',
            'industrial-equipment' => 'معدات صناعية',
            'medical-equipment' => 'معدات طبية',
            'restaurants-equipment' => 'معدات مطاعم',
            'whole-business-for-sale' => 'أعمال كاملة للبيع',
            'other-business-industrial-agriculture' => 'أعمال وصناعة وزراعة أخرى'
        ];
        
        foreach ($businessCategories as $slug => $name) {
            $business->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "🏭 Business & Industrial hierarchy created\n";
    }
    
    public function createServicesHierarchy()
    {
        // Create root category: Services
        $services = Category::create([
            'name' => 'خدمات',
            'slug' => 'services',
            'description' => 'جميع أنواع الخدمات',
            'icon' => 'assets/categories/icon/services.svg',
            'image' => 'assets/categories/bg/services.png'
        ]);
        
        $serviceCategories = [
            'business-services' => 'خدمات أعمال',
            'car-services' => 'خدمات سيارات',
            'events-services' => 'خدمات فعاليات',
            'health-beauty-services' => 'خدمات صحة وجمال',
            'home-services-maintenance' => 'خدمات منزلية وصيانة',
            'medical-services' => 'خدمات طبية',
            'movers-services' => 'خدمات نقل',
            'pets-services' => 'خدمات حيوانات أليفة',
            'education-services' => 'خدمات تعليمية',
            'other-services' => 'خدمات أخرى'
        ];
        
        foreach ($serviceCategories as $slug => $name) {
            $services->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }
        
        echo "🔧 Services hierarchy created\n";
    }
}
