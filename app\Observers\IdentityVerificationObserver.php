<?php

namespace App\Observers;

use App\Models\IdentityVerification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class IdentityVerificationObserver
{
    /**
     * Handle the IdentityVerification "created" event.
     */
    public function created(IdentityVerification $identityVerification): void
    {
        Log::info("تم إنشاء طلب توثيق هوية جديد للمستخدم: {$identityVerification->user->name} (ID: {$identityVerification->id})");
    }

    /**
     * Handle the IdentityVerification "updated" event.
     */
    public function updated(IdentityVerification $identityVerification): void
    {
        // تسجيل تغيير الحالة
        if ($identityVerification->isDirty('status')) {
            $oldStatus = $identityVerification->getOriginal('status');
            $newStatus = $identityVerification->status;
            
            Log::info("تم تغيير حالة طلب توثيق الهوية (ID: {$identityVerification->id}) من {$oldStatus} إلى {$newStatus}");
        }
    }

    /**
     * Handle the IdentityVerification "deleted" event.
     */
    public function deleted(IdentityVerification $identityVerification): void
    {
        try {
            // حذف صورة البطاقة الأمامية
            if ($identityVerification->front_image) {
                if (Storage::disk('public')->exists($identityVerification->front_image)) {
                    Storage::disk('public')->delete($identityVerification->front_image);
                    Log::info("تم حذف صورة البطاقة الأمامية: {$identityVerification->front_image}");
                }
            }

            // حذف صورة البطاقة الخلفية
            if ($identityVerification->back_image) {
                if (Storage::disk('public')->exists($identityVerification->back_image)) {
                    Storage::disk('public')->delete($identityVerification->back_image);
                    Log::info("تم حذف صورة البطاقة الخلفية: {$identityVerification->back_image}");
                }
            }

            Log::info("تم حذف طلب توثيق الهوية وجميع صوره (ID: {$identityVerification->id}) للمستخدم: {$identityVerification->user->name}");

        } catch (\Exception $e) {
            Log::error("خطأ في حذف صور طلب توثيق الهوية (ID: {$identityVerification->id}): " . $e->getMessage());
        }
    }

    /**
     * Handle the IdentityVerification "restored" event.
     */
    public function restored(IdentityVerification $identityVerification): void
    {
        //
    }

    /**
     * Handle the IdentityVerification "force deleted" event.
     */
    public function forceDeleted(IdentityVerification $identityVerification): void
    {
        // نفس منطق الحذف العادي
        $this->deleted($identityVerification);
    }
}
