<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Ad;
use App\Models\SponsoredAd;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم تجريبي
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // إنشاء فئة تجريبية
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'lft' => 1,
            'rgt' => 2,
            'depth' => 0,
        ]);

        // إنشاء إعلان تجريبي
        $ad = Ad::create([
            'title' => 'Test Ad',
            'slug' => 'test-ad-' . time(),
            'description' => 'This is a test ad',
            'price' => 1000,
            'user_id' => $user->id,
            'category_id' => $category->id,
            'status' => 'published',
            'expires_at' => now()->addDays(30),
        ]);

        // إنشاء رعاية تجريبية
        SponsoredAd::create([
            'ad_id' => $ad->id,
            'cost' => 500,
            'total_minutes' => 300, // 5 ساعات
            'status' => 'pending',
            'is_active' => false,
        ]);

        $this->command->info('تم إنشاء بيانات تجريبية بنجاح!');
    }
}
