@extends('partials.admin')
@section('title', 'Users Management')

@push('styles')
<style>
    .nav-tabs .nav-link {
        border: 1px solid transparent;
        border-radius: 0.375rem 0.375rem 0 0;
        color: #6c757d;
        font-weight: 500;
    }

    .nav-tabs .nav-link.active {
        color: #495057;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
    }

    .nav-tabs .nav-link:hover {
        border-color: #e9ecef #e9ecef #dee2e6;
        isolation: isolate;
    }

    .badge {
        font-size: 0.75em;
    }

    .avatar {
        width: 40px;
        height: 40px;
        background-size: cover;
        background-position: center;
        border-radius: 50%;
        display: inline-block;
    }
</style>
@endpush

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'users'])

<div class="main-content app-content mt-0">
    <div class="side-app">

        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Users Management', 'hasBack' => true, 'backTitle' => 'Dashboard', 'backUrl' => route('admin.dashboard')])

            <!-- Users Management Tabs -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <h3 class="card-title mb-0">إدارة المستخدمين</h3>
                            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إضافة مستخدم جديد
                            </a>
                        </div>
                        <div class="">
                           <x-filter-admin-user-card />
                        </div>
                        <div class="card-body">
                            <!-- Navigation Tabs -->
                            <ul class="nav nav-tabs" id="usersManagementTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="regular-users-tab" data-bs-toggle="tab"
                                            data-bs-target="#regular-users" type="button" role="tab">
                                        <i class="fas fa-user"></i>
                                        المستخدمون العاديون
                                        <span class="badge bg-info ms-1">{{ $regularUsersCount ?? 0 }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="broker-users-tab" data-bs-toggle="tab"
                                            data-bs-target="#broker-users" type="button" role="tab">
                                        <i class="fas fa-handshake"></i>
                                        المندوبون
                                        <span class="badge bg-warning ms-1">{{ $brokerUsersCount ?? 0 }}</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="vip-users-tab" data-bs-toggle="tab"
                                            data-bs-target="#vip-users" type="button" role="tab">
                                        <i class="fas fa-crown"></i>
                                        المستخدمون VIP
                                        <span class="badge bg-success ms-1">{{ $vipUsersCount ?? 0 }}</span>
                                    </button>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content mt-3" id="usersManagementTabsContent">
                                <!-- Regular Users Tab -->
                                <div class="tab-pane fade show active" id="regular-users" role="tabpanel">
                                    @include('users.admin.partials.users-table', ['users' => $regularUsers ?? collect(), 'userType' => 'regular'])
                                </div>

                                <!-- Broker Users Tab -->
                                <div class="tab-pane fade" id="broker-users" role="tabpanel">
                                    @include('users.admin.partials.users-table', ['users' => $brokerUsers ?? collect(), 'userType' => 'broker'])
                                </div>

                                <!-- VIP Users Tab -->
                                <div class="tab-pane fade" id="vip-users" role="tabpanel">
                                    @include('users.admin.partials.users-table', ['users' => $vipUsers ?? collect(), 'userType' => 'vip'])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- CONTAINER END -->
    </div>
</div>


@endsection
@push('scripts')
<script src="/plugin/select2/select2.full.min.js"></script>
<script src="/assets/js/select2.js"></script>
    
@endpush