# نظام الإعلانات الممولة - Sponsored Ads System

## نظرة عامة

تم تطوير نظام شامل للإعلانات الممولة يتيح للمستخدمين ترقية إعلاناتهم للحصول على رؤية أفضل وتفاعل أكبر. النظام مصمم ليكون مرناً وقابلاً للتحكم مع إحصائيات مفصلة.

## الميزات الرئيسية

### 1. دمج الإعلانات الممولة في نتائج البحث
- **النسبة**: كل 4 إعلانات عادية يظهر إعلان ممول واحد
- **الفلترة**: الإعلانات الممولة تُفلتر حسب الفئة الرئيسية فقط
- **الترتيب**: حسب أعلى سعر مدفوع مع عشوائية إضافية

### 2. الإعلانات الممولة في صفحة التفاصيل
- **العدد**: 3 إعلانات ممولة مرتبطة
- **المعايير**: نفس الفئة الرئيسية للإعلان المعروض
- **الموقع**: في الـ sidebar الجانبي

### 3. تتبع الإحصائيات
- **المشاهدات**: تسجيل تلقائي عند ظهور الإعلان
- **النقرات**: تسجيل عند النقر على الإعلان
- **معدل النقر (CTR)**: حساب تلقائي ومحدث

### 4. إدارة الوقت والانتهاء
- **التحقق الفوري**: فحص الانتهاء في الوقت الفعلي
- **التنظيف التلقائي**: مهمة مجدولة كل ساعة
- **الحالات**: pending, active, expired

## الملفات المضافة/المحدثة

### 1. الخدمات (Services)
```
app/Services/SponsoredAdService.php
```
- دمج الإعلانات الممولة مع العادية
- جلب الإعلانات المرتبطة
- إدارة الإحصائيات والكاش

### 2. قاعدة البيانات
```
database/migrations/2025_07_28_170000_add_analytics_to_sponsored_ads.php
```
- إضافة أعمدة الإحصائيات: views_count, clicks_count, ctr
- إضافة فهارس للأداء

### 3. النماذج (Models)
```
app/Models/SponsoredAd.php (محدث)
```
- methods للإحصائيات
- تحديث CTR تلقائياً
- methods للزيادة الآمنة

### 4. المستودعات (Repositories)
```
app/Repositories/Ad/User/AdRepository.php (محدث)
```
- دمج SponsoredAdService
- method للإعلانات المرتبطة
- تحديث pagination للدمج

### 5. المتحكمات (Controllers)
```
app/Http/Controllers/User/Ad/AdController.php (محدث)
app/Http/Controllers/Api/SponsoredAdTrackingController.php (جديد)
```
- تمرير الإعلانات الممولة للعرض
- API endpoints للتتبع

### 6. العروض (Views)
```
resources/views/components/sponsored-ad-card.blade.php (جديد)
resources/views/pages/live-auction/index.blade.php (محدث)
resources/views/pages/live-auction/show.blade.php (محدث)
```
- مكون للإعلان الممول
- تحديث العروض لاستخدام النظام الجديد

### 7. الأنماط (Styles)
```
public/assets/css/sponsored-ads.css (جديد)
```
- تصميم مميز للإعلانات الممولة
- responsive design

### 8. الأوامر (Commands)
```
app/Console/Commands/CleanupExpiredSponsoredAds.php (جديد)
app/Console/Kernel.php (محدث)
```
- تنظيف الإعلانات المنتهية
- مجدولة كل ساعة

### 9. المسارات (Routes)
```
routes/api.php (محدث)
```
- API endpoints للتتبع

## API Endpoints

### تتبع المشاهدات
```
POST /api/sponsored-ads/{ad}/view
```

### تتبع النقرات
```
POST /api/sponsored-ads/{ad}/click
```

### الحصول على الإحصائيات
```
GET /api/sponsored-ads/{ad}/stats
```

### أفضل الإعلانات أداءً
```
GET /api/sponsored-ads/top-performing?limit=10
```

## كيفية العمل

### 1. في صفحة البحث (live-auction)
```php
// في AdRepository
$regularAds = $query->get();
$mergedAds = $this->sponsoredAdService->mergeWithRegularAds($regularAds, $filters);
```

### 2. في صفحة التفاصيل
```php
// في AdController
$relatedSponsoredAds = $this->adRepository->getRelatedSponsoredAds($ad, 3);
```

### 3. تتبع المشاهدات (JavaScript)
```javascript
// تلقائي عند ظهور الإعلان
const observer = new IntersectionObserver((entries) => {
    // إرسال طلب تسجيل المشاهدة
});
```

### 4. تتبع النقرات
```javascript
function trackSponsoredClick(adId) {
    fetch(`/api/sponsored-ads/${adId}/click`, {
        method: 'POST',
        // ...
    });
}
```

## الأوامر المفيدة

### تشغيل تنظيف الإعلانات المنتهية
```bash
php artisan sponsored-ads:cleanup
```

### معاينة التنظيف بدون تغيير
```bash
php artisan sponsored-ads:cleanup --dry-run
```

### تشغيل المهام المجدولة
```bash
php artisan schedule:run
```

## الإعدادات والتخصيص

### تغيير نسبة الإعلانات الممولة
في `SponsoredAdService::mergeWithRegularAds()`:
```php
$regularChunks = $regularAds->chunk(4); // غير الرقم 4
```

### تغيير عدد الإعلانات المرتبطة
في `AdController::show()`:
```php
$relatedSponsoredAds = $this->adRepository->getRelatedSponsoredAds($ad, 3); // غير الرقم 3
```

### تغيير مدة الكاش
في `SponsoredAdService`:
```php
Cache::remember($cacheKey, now()->addMinutes(10), function () { // غير المدة
```

## الأمان والأداء

### 1. الكاش
- كاش للإعلانات الممولة (10 دقائق)
- كاش للإعلانات المرتبطة (15 دقيقة)
- مسح الكاش عند التحديث

### 2. الفهارس
- فهرس مركب على (status, is_active, expires_at)
- فهرس على (views_count, clicks_count)

### 3. التحقق من الصحة
- التحقق من وجود الإعلان الممول
- التحقق من حالة النشاط
- معالجة الأخطاء

## الاختبار

### 1. اختبار الدمج
```php
// تأكد من ظهور الإعلانات الممولة كل 4 إعلانات
$ads = $adRepository->getLatestAdsWithAttributes($filters, 20);
```

### 2. اختبار التتبع
```javascript
// تأكد من إرسال طلبات التتبع
console.log('View tracked for ad:', adId);
```

### 3. اختبار الانتهاء
```bash
# تشغيل الأمر مع dry-run
php artisan sponsored-ads:cleanup --dry-run
```

## الصيانة

### 1. مراقبة الأداء
- مراقبة استخدام الكاش
- مراقبة استعلامات قاعدة البيانات
- مراقبة معدلات النقر

### 2. التنظيف الدوري
- الأمر المجدول يعمل كل ساعة
- يمكن تشغيله يدوياً عند الحاجة

### 3. النسخ الاحتياطي
- نسخ احتياطي لجدول sponsored_ads
- نسخ احتياطي للإحصائيات

## الدعم والتطوير

للمساعدة أو التطوير الإضافي، راجع:
- الكود المصدري في الملفات المذكورة
- لوحة الإدارة للإحصائيات
- سجلات النظام للأخطاء

---

## 🚀 التحديثات الجديدة - الإصدار 2.0.0

### ✨ الميزات المضافة حديثاً

#### 1. نظام الإعلانات ذات الصلة الذكي
- **خوارزمية متقدمة**: 40% نفس الفئة الفرعية، 30% الفئة الأب، 20% الموقع، 10% السعر
- **دمج ذكي**: نسبة 3:1 (3 إعلانات عادية : 1 إعلان ممول)
- **كاش محسن**: TTL 15-20 دقيقة مع تحديث تلقائي

#### 2. نظام الاستهداف المتقدم
- **الاستهداف الجغرافي**: حسب الدولة، الولاية، والمدينة
- **نظام الأولوية**: 5 مستويات (1=عالية جداً، 5=منخفضة جداً)
- **استهداف الفئات**: JSON field للفئات المستهدفة
- **مواقع العرض**: تحكم في البحث وصفحات التفاصيل

#### 3. لوحة تحكم إدارية محسنة
- **فلترة متقدمة**: حسب الأولوية، الدولة، الحالة
- **تحديث AJAX**: تغيير الحالة والأولوية فورياً
- **إحصائيات مفصلة**: عرض شامل للأداء

#### 4. نظام التحليلات الشامل
- **SponsoredAdAnalyticsController**: تحليلات متقدمة
- **تقارير مفصلة**: حسب الأولوية، الموقع، الفئات
- **تصدير CSV**: تصدير البيانات للتحليل الخارجي
- **رسوم بيانية**: مخططات تفاعلية

#### 5. تتبع متقدم للمستخدمين
- **Intersection Observer**: تتبع دقيق للمشاهدات
- **تحليلات سلوكية**: وقت المشاهدة، موقع النقر
- **معلومات الجهاز**: نوع الجهاز، الاتصال، الموقع

### 🏗️ الملفات الجديدة/المحدثة

#### الخدمات المضافة
```
app/Services/RelatedAdsService.php (جديد)
app/Http/Controllers/Admin/SponsoredAdAnalyticsController.php (جديد)
```

#### قاعدة البيانات
```
database/migrations/2025_07_28_181538_add_priority_and_location_to_sponsored_ads.php
```

#### الواجهات والتصميم
```
public/assets/css/related-ads.css (جديد)
public/assets/js/related-ads-tracking.js (جديد)
```

#### المسارات المحدثة
```
routes/admin.php (محدث - إضافة analytics routes)
routes/api.php (محدث - إضافة tracking endpoints)
```

### 📊 الإحصائيات الجديدة

#### معلومات شاملة
- إجمالي الإيرادات حسب الفترة
- معدل التحويل (CTR) المتقدم
- تحليل الأداء حسب الأولوية
- إحصائيات جغرافية مفصلة

#### تقارير متقدمة
- أفضل الإعلانات أداءً
- مقارنة الأداء بالمتوسط
- تحليل الاتجاهات الزمنية
- تقارير مخصصة حسب التاريخ

### 🎨 تحسينات التصميم

#### واجهة محسنة
- تصميم متجاوب للجوال
- انيميشن CSS3 متقدم
- مؤشرات بصرية للإعلانات الممولة
- تأثيرات hover تفاعلية

#### تجربة مستخدم محسنة
- تحميل سريع للصور
- تفاعل سلس مع النقرات
- مؤشرات تحميل ذكية
- دعم الوضع المظلم

---

**تم التطوير والتحديث بواسطة**: Augment Agent
**التاريخ**: يوليو 2025
**الإصدار**: 2.0.0 - Enhanced with Advanced Analytics & Related Ads Intelligence
