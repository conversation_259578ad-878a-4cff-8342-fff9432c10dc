<?php

namespace App\Http\Controllers\Admin\User;

use App\Contracts\Repositories\UserRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Requests\User\CreateAdminUserRequest;
use App\Http\Requests\User\FilterAdminUserRequest;
use App\Http\Requests\User\UpdateAdminUserRequest;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
     /**
     * Instantiate new controller instance
     */
    public function __construct(protected UserRepositoryInterface $userRepository)
    {}

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(FilterAdminUserRequest $query): View
    {
        $filters = $query->validated();

        return view('users.admin.index', [
            // Regular users data
            'regularUsers' => $this->userRepository->getUsersByTypeForAdmin('regular', 10, $filters),
            'regularUsersCount' => $this->userRepository->getUsersCountByType('regular'),

            // Broker users data
            'brokerUsers' => $this->userRepository->getUsersByTypeForAdmin('broker', 10, $filters),
            'brokerUsersCount' => $this->userRepository->getUsersCountByType('broker'),

            // VIP users data
            'vipUsers' => $this->userRepository->getUsersByTypeForAdmin('vip', 10, $filters),
            'vipUsersCount' => $this->userRepository->getUsersCountByType('vip'),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('users.admin.create');
    }

    /**
     * Store a newly created resource in storage.
     * 
     * @param \App\Http\Requests\User\CreateAdminUserRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(CreateAdminUserRequest $request)
    {
        $this->userRepository->createUser($request->validated());

        return redirect()->route('admin.users.index')->with('success', 'User created successfully');
    }

    /**
     * Display the specified resource.
     * 
     * @param string $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show(string $id): View
    {
        return view('users.admin.show', [
            'user' => $this->userRepository->getUserForAdmin($id),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     * 
     * @param string $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit(string $id): View
    {
        return view('users.admin.edit', [
            'user' => $this->userRepository->getUserForAdmin($id),
        ]);
    }

    /**
     * Update the specified resource in storage.
     * 
     * @param \App\Http\Requests\User\UpdateAdminUserRequest $request
     * @param string $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateAdminUserRequest $request, string $id)
    {
        $user = $this->userRepository->getUserForAdmin($id);
        $data = $request->validated();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists and not default
            if ($user->avatar && $user->avatar !== config('chatify.user_avatar.default')) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        // التحقق من تحويل المستخدم إلى مندوب أو العكس
        $wasBroker = $user->is_broker;
        $willBeBroker = $data['is_broker'] ?? false;

        // إذا تم تحويله من مستخدم عادي إلى مندوب
        if (!$wasBroker && $willBeBroker) {
            $data['broker_activated_at'] = now();
            // إعطاء فترة سماح 30 يوم للمندوب الجديد
            $data['last_successful_code_at'] = null;

            Log::info("User converted to broker", [
                'user_id' => $id,
                'user_name' => $user->name,
                'broker_activated_at' => now(),
                'admin_id' => Auth::guard('admin_web')->id()
            ]);
        }

        // إذا تم تحويله من مندوب إلى مستخدم عادي
        if ($wasBroker && !$willBeBroker) {
            $data['broker_activated_at'] = null;
            $data['last_successful_code_at'] = null;

            Log::info("Broker converted to regular user", [
                'user_id' => $id,
                'user_name' => $user->name,
                'admin_id' => Auth::guard('admin_web')->id()
            ]);
        }

        $this->userRepository->updateUser($id, $data);

        $message = 'User updated successfully';
        if (!$wasBroker && $willBeBroker) {
            $message .= '. تم تحويل المستخدم إلى مندوب مع فترة سماح 30 يوم.';
        } elseif ($wasBroker && !$willBeBroker) {
            $message .= '. تم تحويل المندوب إلى مستخدم عادي وإزالة جميع بيانات المندوب.';
        }

        return redirect()->route('admin.users.edit', $id)->with('success', $message);
    }

    /**
     * تفعيل المندوب (إعطاء نشاط جديد)
     */
    public function activateBroker(string $id)
    {
        try {
            $user = $this->userRepository->getUserForAdmin($id);

            if (!$user->is_broker) {
                return back()->with('error', 'المستخدم ليس مندوب. يجب تحويله إلى مندوب أولاً.');
            }

            // تحديث تاريخ آخر كود ناجح إلى الوقت الحالي
            $user->update([
                'last_successful_code_at' => now(),
            ]);
            Log::info("Admin activated broker", [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'last_successful_code_at' => now(),
                'admin_id' => Auth::guard('admin_web')->id(),
                'admin_name' => Auth::guard('admin_web')->user()->name ?? 'Unknown'
            ]);

            return back()->with('success', 'تم تنشيط المندوب بنجاح. سيحصل على 30 يوم نشاط جديد من اليوم.');
        } catch (\Exception $e) {
            Log::error("Error activating broker", [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'admin_id' => Auth::guard('admin_web')->id()
            ]);
            return back()->with('error', 'حدث خطأ أثناء تنشيط المندوب: ' . $e->getMessage());
        }
    }

    /**
     * إلغاء تنشيط المندوب
     */
    public function deactivateBroker(string $id)
    {
        try {
            $user = $this->userRepository->getUserForAdmin($id);

            if (!$user->is_broker) {
                return back()->with('error', 'المستخدم ليس مندوب.');
            }

            // وضع تاريخ قديم لجعل المندوب غير نشط
            $oldDate = \Carbon\Carbon::parse('2000-01-01 00:00:00');

            $user->update([
                'last_successful_code_at' => $oldDate,
            ]);

            Log::info("Admin deactivated broker", [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'old_last_successful_code_at' => $user->last_successful_code_at,
                'new_last_successful_code_at' => $oldDate,
                'admin_id' => Auth::guard('admin_web')->id(),
                'admin_name' => Auth::guard('admin_web')->user()->name ?? 'Unknown'
            ]);

            return back()->with('success', 'تم إلغاء تنشيط المندوب بنجاح. أصبح غير نشط الآن.');
        } catch (Exception $e) {
            Log::error("Error deactivating broker", [
                'user_id' => $id,
                'error' => $e->getMessage(),
                'admin_id' => Auth::guard('admin_web')->id()
            ]);
            return back()->with('error', 'حدث خطأ أثناء إلغاء تنشيط المندوب: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     * 
     * @param string $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id): RedirectResponse
    {
        $this->userRepository->deleteUser($id);

        return redirect()->route('admin.users.index')->with('success', 'User deleted successfully');
    }

    /**
     * Request password reset.
     * 
     * @param string $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function requestPasswordReset(string $id): RedirectResponse
    {
        $this->userRepository->sendPasswordResetLink($id);

        return redirect()->route('admin.users.edit', $id)->with('success', 'Password reset link sent to user');
    }

    /**
     * Impersonate a user (Login as User)
     *
     * @param string $id
     * @return RedirectResponse
     */
    public function impersonate(string $id): RedirectResponse
    {
        // التحقق من أن المستخدم الحالي هو أدمن
        if (!Auth::guard('admin_web')->check()) {
            return redirect()->route('admin.login')->with('error', 'Unauthorized access');
        }

        // البحث عن المستخدم
        $user = User::findOrFail($id);

        // حفظ معرف الأدمن في السيشن
        session(['impersonated_by' => Auth::guard('admin_web')->id()]);

        // تسجيل الدخول كالمستخدم
        Auth::guard('web')->login($user);

        return redirect()->route('user.dashboard')->with('success', "You are now logged in as {$user->name}");
    }

    /**
     * Leave impersonation and return to admin
     *
     * @return RedirectResponse
     */
    public function leaveImpersonate(): RedirectResponse
    {
        // التحقق من وجود impersonation
        if (!session('impersonated_by')) {
            return redirect()->route('user.dashboard')->with('error', 'You are not impersonating anyone');
        }

        // الحصول على معرف الأدمن
        $adminId = session('impersonated_by');

        // إزالة السيشن
        session()->forget('impersonated_by');

        // تسجيل الخروج من المستخدم
        Auth::guard('web')->logout();

        // تسجيل الدخول كأدمن مرة أخرى
        Auth::guard('admin_web')->loginUsingId($adminId);

        return redirect()->route('admin.users.index')->with('success', 'You have returned to admin panel');
    }
}
