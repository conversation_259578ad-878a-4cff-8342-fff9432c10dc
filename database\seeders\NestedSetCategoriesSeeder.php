<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Category;
use App\Models\CategoryAttribute;

class NestedSetCategoriesSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing data
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        CategoryAttribute::truncate();
        Category::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create all main categories
        $this->createVehiclesHierarchy();
        $this->createPropertiesHierarchy();
        $this->createMobilesTabletsHierarchy();
        $this->createJobsHierarchy();
        $this->createHomeFurnitureHierarchy();
        $this->createElectronicsAppliancesHierarchy();
        $this->createFashionBeautyHierarchy();
        $this->createPetsHierarchy();
        $this->createKidsBabiesHierarchy();
        $this->createHobbiesHierarchy();
        $this->createBusinessIndustrialHierarchy();
        $this->createServicesHierarchy();

        // Fix the tree structure
        Category::fixTree();

        // Add attributes to leaf categories
        $this->addCategoryAttributes();

        echo "✅ Complete Nested Set Categories created successfully!\n";
        $this->printCategoryTree();
    }
    
    private function createVehiclesHierarchy()
    {
        // Create root category: Vehicles
        $vehicles = Category::create([
            'name' => 'المركبات',
            'slug' => 'vehicles',
            'description' => 'جميع أنواع المركبات',
            'icon' => 'assets/categories/icon/car.svg',
            'image' => 'assets/categories/bg/vehicles.png'
        ]);

        // Cars for Sale
        $vehicles->children()->create([
            'name' => 'سيارات للبيع',
            'slug' => 'cars-for-sale',
            'description' => 'سيارات للبيع'
        ]);

        // Cars for Rent
        $vehicles->children()->create([
            'name' => 'سيارات للإيجار',
            'slug' => 'cars-for-rent',
            'description' => 'سيارات للإيجار'
        ]);

        // Tyres, Batteries, Oils, & Accessories
        $vehicles->children()->create([
            'name' => 'إطارات وبطاريات وزيوت وإكسسوارات',
            'slug' => 'tyres-batteries-oils-accessories',
            'description' => 'إطارات وبطاريات وزيوت وإكسسوارات السيارات'
        ]);

        // Car Spare Parts
        $vehicles->children()->create([
            'name' => 'قطع غيار السيارات',
            'slug' => 'car-spare-parts',
            'description' => 'قطع غيار السيارات'
        ]);

        // Motorcycles & Accessories
        $vehicles->children()->create([
            'name' => 'موتوسيكلات وإكسسوارات',
            'slug' => 'motorcycles-accessories',
            'description' => 'موتوسيكلات وإكسسواراتها'
        ]);

        // Boats - Watercraft
        $vehicles->children()->create([
            'name' => 'قوارب ومراكب مائية',
            'slug' => 'boats-watercraft',
            'description' => 'قوارب ومراكب مائية'
        ]);

        // Heavy Trucks, Buses & Other Vehicles
        $vehicles->children()->create([
            'name' => 'شاحنات ثقيلة وحافلات ومركبات أخرى',
            'slug' => 'heavy-trucks-buses-other',
            'description' => 'شاحنات ثقيلة وحافلات ومركبات أخرى'
        ]);

        echo "🚗 Vehicles hierarchy created\n";
    }
    
    private function createPropertiesHierarchy()
    {
        // Create root category: Properties
        $properties = Category::create([
            'name' => 'العقارات',
            'slug' => 'properties',
            'description' => 'عقارات للبيع والإيجار',
            'icon' => 'assets/categories/icon/real-estate.svg',
            'image' => 'assets/categories/bg/real-estate.png'
        ]);

        // Apartments for Sale
        $properties->children()->create([
            'name' => 'شقق للبيع',
            'slug' => 'apartments-for-sale',
            'description' => 'شقق للبيع'
        ]);

        // Apartments for Rent
        $properties->children()->create([
            'name' => 'شقق للإيجار',
            'slug' => 'apartments-for-rent',
            'description' => 'شقق للإيجار'
        ]);

        // Villas For Sale
        $properties->children()->create([
            'name' => 'فيلات للبيع',
            'slug' => 'villas-for-sale',
            'description' => 'فيلات للبيع'
        ]);

        // Villas For Rent
        $properties->children()->create([
            'name' => 'فيلات للإيجار',
            'slug' => 'villas-for-rent',
            'description' => 'فيلات للإيجار'
        ]);

        // Vacation Homes for Sale
        $properties->children()->create([
            'name' => 'بيوت عطلات للبيع',
            'slug' => 'vacation-homes-for-sale',
            'description' => 'بيوت عطلات للبيع'
        ]);

        // Vacation Homes for Rent
        $properties->children()->create([
            'name' => 'بيوت عطلات للإيجار',
            'slug' => 'vacation-homes-for-rent',
            'description' => 'بيوت عطلات للإيجار'
        ]);

        // Commercial for Sale
        $properties->children()->create([
            'name' => 'عقارات تجارية للبيع',
            'slug' => 'commercial-for-sale',
            'description' => 'عقارات تجارية للبيع'
        ]);

        // Commercial for Rent
        $properties->children()->create([
            'name' => 'عقارات تجارية للإيجار',
            'slug' => 'commercial-for-rent',
            'description' => 'عقارات تجارية للإيجار'
        ]);

        // Buildings & Lands
        $properties->children()->create([
            'name' => 'مباني وأراضي',
            'slug' => 'buildings-lands',
            'description' => 'مباني وأراضي'
        ]);

        echo "🏠 Properties hierarchy created\n";
    }

    private function createMobilesTabletsHierarchy()
    {
        // Create root category: Mobiles & Tablets
        $mobilesTablets = Category::create([
            'name' => 'موبايلات وتابلت وإكسسوارات',
            'slug' => 'mobiles-tablets',
            'description' => 'موبايلات وتابلت وإكسسواراتها',
            'icon' => 'assets/categories/icon/mobile.svg',
            'image' => 'assets/categories/bg/mobile.png'
        ]);

        // Mobile Phones
        $mobilesTablets->children()->create([
            'name' => 'موبايلات',
            'slug' => 'mobile-phones',
            'description' => 'هواتف ذكية'
        ]);

        // Tablets
        $mobilesTablets->children()->create([
            'name' => 'تابلت',
            'slug' => 'tablets',
            'description' => 'أجهزة لوحية'
        ]);

        // Mobile & Tablet Accessories
        $mobilesTablets->children()->create([
            'name' => 'إكسسوارات موبايل وتابلت',
            'slug' => 'mobile-tablet-accessories',
            'description' => 'إكسسوارات الموبايل والتابلت'
        ]);

        // Mobile Numbers
        $mobilesTablets->children()->create([
            'name' => 'أرقام موبايل',
            'slug' => 'mobile-numbers',
            'description' => 'أرقام موبايل مميزة'
        ]);

        echo "📱 Mobiles & Tablets hierarchy created\n";
    }
    
    private function createJobsHierarchy()
    {
        // Create root category: Jobs
        $jobs = Category::create([
            'name' => 'وظائف',
            'slug' => 'jobs',
            'description' => 'فرص عمل ووظائف',
            'icon' => 'assets/categories/icon/jobs.svg',
            'image' => 'assets/categories/bg/jobs.png'
        ]);

        // Job categories
        $jobCategories = [
            'accounting-finance-banking' => 'محاسبة ومالية ومصرفية',
            'engineering' => 'هندسة',
            'designers' => 'مصممين',
            'customer-service-call-center' => 'خدمة عملاء ومراكز اتصال',
            'workers-technicians' => 'عمال وفنيين',
            'management-consulting' => 'إدارة واستشارات',
            'drivers-delivery' => 'سائقين وتوصيل',
            'education' => 'تعليم',
            'hr' => 'موارد بشرية',
            'tourism-travel-hospitality' => 'سياحة وسفر وضيافة',
            'it-telecom' => 'تكنولوجيا المعلومات والاتصالات',
            'marketing-public-relations' => 'تسويق وعلاقات عامة',
            'medical-healthcare-nursing' => 'طبي ورعاية صحية وتمريض',
            'sales' => 'مبيعات',
            'secretarial' => 'سكرتارية',
            'guards-security' => 'حراسة وأمن',
            'legal-lawyers' => 'قانوني ومحاماة'
        ];

        foreach ($jobCategories as $slug => $name) {
            $jobs->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => "وظائف في مجال {$name}"
            ]);
        }

        echo "💼 Jobs hierarchy created\n";
    }

    private function createHomeFurnitureHierarchy()
    {
        // Create root category: Home & Office Furniture - Decor
        $homeFurniture = Category::create([
            'name' => 'أثاث منزلي ومكتبي - ديكور',
            'slug' => 'home-office-furniture-decor',
            'description' => 'أثاث منزلي ومكتبي وديكور',
            'icon' => 'assets/categories/icon/furniture.svg',
            'image' => 'assets/categories/bg/furniture.png'
        ]);

        $furnitureCategories = [
            'office-furniture' => 'أثاث مكتبي',
            'furniture' => 'أثاث',
            'home-decoration-accessories' => 'ديكور منزلي وإكسسوارات',
            'bathroom-kitchen-tools-accessories' => 'أدوات وإكسسوارات حمام ومطبخ',
            'fabric-bedding' => 'أقمشة وفراش',
            'garden-outdoor' => 'حديقة - خارجي',
            'lighting' => 'إضاءة',
            'multiple-other-furniture' => 'أثاث متنوع/أخرى'
        ];

        foreach ($furnitureCategories as $slug => $name) {
            $homeFurniture->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "🪑 Home & Office Furniture hierarchy created\n";
    }

    private function createElectronicsAppliancesHierarchy()
    {
        // Create root category: Electronics & Home Appliances
        $electronics = Category::create([
            'name' => 'إلكترونيات وأجهزة منزلية',
            'slug' => 'electronics-home-appliances',
            'description' => 'إلكترونيات وأجهزة منزلية',
            'icon' => 'assets/categories/icon/electronics.svg',
            'image' => 'assets/categories/bg/electronics.png'
        ]);

        $electronicsCategories = [
            'tv-audio-video' => 'تلفزيون - صوت - فيديو',
            'computers-accessories' => 'كمبيوتر - إكسسوارات',
            'video-games-consoles' => 'ألعاب فيديو - أجهزة ألعاب',
            'cameras-imaging' => 'كاميرات - تصوير',
            'home-appliances' => 'أجهزة منزلية'
        ];

        foreach ($electronicsCategories as $slug => $name) {
            $electronics->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "📺 Electronics & Appliances hierarchy created\n";
    }

    private function createFashionBeautyHierarchy()
    {
        // Create root category: Fashion & Beauty
        $fashion = Category::create([
            'name' => 'أزياء وجمال',
            'slug' => 'fashion-beauty',
            'description' => 'أزياء وجمال',
            'icon' => 'assets/categories/icon/fashion.svg',
            'image' => 'assets/categories/bg/fashion.png'
        ]);

        $fashionCategories = [
            'womens-clothing' => 'ملابس نسائية',
            'mens-clothing' => 'ملابس رجالية',
            'womens-accessories-cosmetics-personal-care' => 'إكسسوارات نسائية - مستحضرات تجميل - عناية شخصية',
            'mens-accessories-personal-care' => 'إكسسوارات رجالية - عناية شخصية'
        ];

        foreach ($fashionCategories as $slug => $name) {
            $fashion->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "👗 Fashion & Beauty hierarchy created\n";
    }

    private function createPetsHierarchy()
    {
        // Create root category: Pets - Birds - Ornamental fish
        $pets = Category::create([
            'name' => 'حيوانات أليفة - طيور - أسماك زينة',
            'slug' => 'pets-birds-ornamental-fish',
            'description' => 'حيوانات أليفة وطيور وأسماك زينة',
            'icon' => 'assets/categories/icon/pets.svg',
            'image' => 'assets/categories/bg/pets.png'
        ]);

        $petCategories = [
            'dogs' => 'كلاب',
            'cats' => 'قطط',
            'birds' => 'طيور',
            'ornamental-fish' => 'أسماك زينة',
            'horses' => 'خيول',
            'cows-sheep-camels' => 'أبقار وأغنام وجمال',
            'animal-birds-fish-dry-food' => 'طعام جاف للحيوانات والطيور والأسماك',
            'animal-bird-fish-care-tools-accessories' => 'أدوات وإكسسوارات رعاية الحيوانات والطيور والأسماك',
            'other-pets-animals' => 'حيوانات أليفة أخرى'
        ];

        foreach ($petCategories as $slug => $name) {
            $pets->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "🐕 Pets hierarchy created\n";
    }

    private function createKidsBabiesHierarchy()
    {
        // Create root category: Kids & Babies
        $kidsBabies = Category::create([
            'name' => 'أطفال ورضع',
            'slug' => 'kids-babies',
            'description' => 'مستلزمات الأطفال والرضع',
            'icon' => 'assets/categories/icon/baby.svg',
            'image' => 'assets/categories/bg/baby.png'
        ]);

        $babyCategories = [
            'baby-mom-healthcare' => 'رعاية صحية للطفل والأم',
            'baby-clothing' => 'ملابس أطفال',
            'baby-feeding-tools' => 'أدوات إطعام الأطفال',
            'baby-beds-strollers-carseats' => 'أسرة أطفال - عربات - مقاعد سيارة',
            'toys' => 'ألعاب',
            'other-baby-items' => 'مستلزمات أطفال أخرى',
            'baby-walkers-chairs-feeding-chairs' => 'مشايات أطفال - كراسي أطفال - كراسي إطعام'
        ];

        foreach ($babyCategories as $slug => $name) {
            $kidsBabies->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "👶 Kids & Babies hierarchy created\n";
    }

    private function createHobbiesHierarchy()
    {
        // Create root category: Books, Sports & Hobbies
        $hobbies = Category::create([
            'name' => 'كتب ورياضة وهوايات',
            'slug' => 'books-sports-hobbies',
            'description' => 'كتب ورياضة وهوايات',
            'icon' => 'assets/categories/icon/hobbies.svg',
            'image' => 'assets/categories/bg/hobbies.png'
        ]);

        $hobbyCategories = [
            'antiques-collectibles' => 'تحف - مقتنيات',
            'bicycles' => 'دراجات هوائية',
            'books' => 'كتب',
            'board-card-games' => 'ألعاب لوحية - ألعاب ورق',
            'movies-music' => 'أفلام - موسيقى',
            'musical-instruments' => 'آلات موسيقية',
            'sports-equipment' => 'معدات رياضية',
            'study-tools' => 'أدوات دراسية',
            'tickets-vouchers' => 'تذاكر - قسائم',
            'luggage' => 'حقائب سفر',
            'other-items' => 'أشياء أخرى'
        ];

        foreach ($hobbyCategories as $slug => $name) {
            $hobbies->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "📚 Hobbies hierarchy created\n";
    }

    private function createBusinessIndustrialHierarchy()
    {
        // Create root category: Business - Industrial - Agriculture
        $business = Category::create([
            'name' => 'أعمال - صناعي - زراعي',
            'slug' => 'business-industrial-agriculture',
            'description' => 'أعمال وصناعة وزراعة',
            'icon' => 'assets/categories/icon/business.svg',
            'image' => 'assets/categories/bg/business.png'
        ]);

        $businessCategories = [
            'agriculture' => 'زراعة',
            'construction' => 'إنشاءات',
            'industrial-equipment' => 'معدات صناعية',
            'medical-equipment' => 'معدات طبية',
            'restaurants-equipment' => 'معدات مطاعم',
            'whole-business-for-sale' => 'أعمال كاملة للبيع',
            'other-business-industrial-agriculture' => 'أعمال وصناعة وزراعة أخرى'
        ];

        foreach ($businessCategories as $slug => $name) {
            $business->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "🏭 Business & Industrial hierarchy created\n";
    }

    private function createServicesHierarchy()
    {
        // Create root category: Services
        $services = Category::create([
            'name' => 'خدمات',
            'slug' => 'services',
            'description' => 'جميع أنواع الخدمات',
            'icon' => 'assets/categories/icon/services.svg',
            'image' => 'assets/categories/bg/services.png'
        ]);

        $serviceCategories = [
            'business-services' => 'خدمات أعمال',
            'car-services' => 'خدمات سيارات',
            'events-services' => 'خدمات فعاليات',
            'health-beauty-services' => 'خدمات صحة وجمال',
            'home-services-maintenance' => 'خدمات منزلية وصيانة',
            'medical-services' => 'خدمات طبية',
            'movers-services' => 'خدمات نقل',
            'pets-services' => 'خدمات حيوانات أليفة',
            'education-services' => 'خدمات تعليمية',
            'other-services' => 'خدمات أخرى'
        ];

        foreach ($serviceCategories as $slug => $name) {
            $services->children()->create([
                'name' => $name,
                'slug' => $slug,
                'description' => $name
            ]);
        }

        echo "🔧 Services hierarchy created\n";
    }
    
    private function addCategoryAttributes()
    {
        // Add basic attributes for main categories only
        $this->addCarsForSaleAttributes();
        $this->addCarsForRentAttributes();
        $this->addMobilePhonesAttributes();
        $this->addApartmentsForSaleAttributes();

        echo "📊 Basic category attributes added\n";
    }
    
    private function addCarsForSaleAttributes()
    {
        $cars = Category::where('slug', 'syarat-llbyaa')->first();
        if (!$cars) {
            echo "Cars for sale category not found\n";
            return;
        }
        
        $attributes = [
            [
                'attribute_name' => 'brand',
                'attribute_label' => 'نوع السيارة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'toyota' => 'تويوتا',
                    'honda' => 'هوندا',
                    'bmw' => 'BMW',
                    'mercedes' => 'مرسيدس',
                    'hyundai' => 'هيونداي',
                    'chevrolet' => 'شيفروليه',
                    'nissan' => 'نيسان',
                    'kia' => 'كيا',
                    'ford' => 'فورد',
                    'volkswagen' => 'فولكس واجن'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'year',
                'attribute_label' => 'سنة الصنع',
                'attribute_type' => 'select',
                'attribute_options' => $this->generateYearOptions(),
                'sort_order' => 2,
                'is_required' => true
            ],
            [
                'attribute_name' => 'condition',
                'attribute_label' => 'حالة السيارة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'new' => 'جديدة',
                    'used' => 'مستعملة',
                    'factory' => 'فابريكا',
                    'accident' => 'راشة',
                    'showroom' => 'معرض',
                    'agency' => 'وكالة'
                ],
                'sort_order' => 3,
                'is_required' => true
            ],
            [
                'attribute_name' => 'fuel_type',
                'attribute_label' => 'نوع الوقود',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'petrol' => 'بنزين',
                    'diesel' => 'ديزل',
                    'hybrid' => 'هايبرد',
                    'electric' => 'كهربائي',
                    'cng' => 'غاز طبيعي',
                    'lpg' => 'غاز البترول المسال'
                ],
                'sort_order' => 4,
                'is_required' => false
            ],
            [
                'attribute_name' => 'transmission',
                'attribute_label' => 'ناقل الحركة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'manual' => 'يدوي',
                    'automatic' => 'أوتوماتيك',
                    'cvt' => 'CVT',
                    'tiptronic' => 'تيبترونيك',
                    'dsg' => 'DSG'
                ],
                'sort_order' => 5,
                'is_required' => false
            ],
            [
                'attribute_name' => 'color',
                'attribute_label' => 'لون السيارة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'white' => 'أبيض',
                    'black' => 'أسود',
                    'silver' => 'فضي',
                    'gray' => 'رمادي',
                    'red' => 'أحمر',
                    'blue' => 'أزرق',
                    'green' => 'أخضر',
                    'gold' => 'ذهبي',
                    'brown' => 'بني',
                    'yellow' => 'أصفر'
                ],
                'sort_order' => 6,
                'is_required' => false
            ],
            [
                'attribute_name' => 'mileage',
                'attribute_label' => 'الكيلومترات',
                'attribute_type' => 'input',
                'attribute_options' => null,
                'sort_order' => 7,
                'is_required' => false
            ],
            [
                'attribute_name' => 'engine_size',
                'attribute_label' => 'حجم المحرك',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '1000' => '1.0 لتر',
                    '1200' => '1.2 لتر',
                    '1400' => '1.4 لتر',
                    '1600' => '1.6 لتر',
                    '1800' => '1.8 لتر',
                    '2000' => '2.0 لتر',
                    '2500' => '2.5 لتر',
                    '3000' => '3.0 لتر',
                    '3500' => '3.5 لتر',
                    '4000' => '4.0 لتر',
                    '5000' => '5.0 لتر'
                ],
                'sort_order' => 8,
                'is_required' => false
            ],
            [
                'attribute_name' => 'body_type',
                'attribute_label' => 'نوع الهيكل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'sedan' => 'سيدان',
                    'hatchback' => 'هاتشباك',
                    'suv' => 'SUV',
                    'coupe' => 'كوبيه',
                    'convertible' => 'كونفرتيبل',
                    'wagon' => 'ستيشن واجن',
                    'pickup' => 'بيك أب',
                    'van' => 'فان',
                    'crossover' => 'كروس أوفر'
                ],
                'sort_order' => 9,
                'is_required' => false
            ],
            [
                'attribute_name' => 'doors',
                'attribute_label' => 'عدد الأبواب',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '2' => 'بابان',
                    '3' => '3 أبواب',
                    '4' => '4 أبواب',
                    '5' => '5 أبواب'
                ],
                'sort_order' => 10,
                'is_required' => false
            ]
        ];
        
        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $cars->id], $attr));
        }
    }

    private function addCarsForRentAttributes()
    {
        $cars = Category::where('slug', 'syarat-llaygar')->first();
        if (!$cars) {
            echo "Cars for rent category not found\n";
            return;
        }

        $attributes = [
            [
                'attribute_name' => 'brand',
                'attribute_label' => 'نوع السيارة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'toyota' => 'تويوتا',
                    'honda' => 'هوندا',
                    'bmw' => 'BMW',
                    'mercedes' => 'مرسيدس',
                    'hyundai' => 'هيونداي',
                    'chevrolet' => 'شيفروليه',
                    'nissan' => 'نيسان',
                    'kia' => 'كيا'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'year',
                'attribute_label' => 'سنة الصنع',
                'attribute_type' => 'select',
                'attribute_options' => $this->generateYearOptions(),
                'sort_order' => 2,
                'is_required' => true
            ],
            [
                'attribute_name' => 'rental_period',
                'attribute_label' => 'فترة الإيجار',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'daily' => 'يومي',
                    'weekly' => 'أسبوعي',
                    'monthly' => 'شهري',
                    'yearly' => 'سنوي'
                ],
                'sort_order' => 3,
                'is_required' => true
            ],
            [
                'attribute_name' => 'fuel_type',
                'attribute_label' => 'نوع الوقود',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'petrol' => 'بنزين',
                    'diesel' => 'ديزل',
                    'hybrid' => 'هايبرد',
                    'electric' => 'كهربائي'
                ],
                'sort_order' => 4,
                'is_required' => false
            ],
            [
                'attribute_name' => 'transmission',
                'attribute_label' => 'ناقل الحركة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'manual' => 'يدوي',
                    'automatic' => 'أوتوماتيك'
                ],
                'sort_order' => 5,
                'is_required' => false
            ],
            [
                'attribute_name' => 'seats',
                'attribute_label' => 'عدد المقاعد',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '2' => 'مقعدان',
                    '4' => '4 مقاعد',
                    '5' => '5 مقاعد',
                    '7' => '7 مقاعد',
                    '8+' => '8 مقاعد أو أكثر'
                ],
                'sort_order' => 6,
                'is_required' => false
            ]
        ];

        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $cars->id], $attr));
        }
    }

    private function addMotorcycleAttributes()
    {
        $motorcycles = Category::where('slug', 'motosyklat')->first();
        if (!$motorcycles) {
            echo "Motorcycles category not found with slug 'motosyklat'\n";
            return;
        }

        $attributes = [
            [
                'attribute_name' => 'brand',
                'attribute_label' => 'نوع الموتوسيكل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'dayang' => 'دايون',
                    'halawa' => 'حلاوة',
                    'yamaha' => 'ياماها',
                    'honda' => 'هوندا',
                    'suzuki' => 'سوزوكي',
                    'kawasaki' => 'كاواساكي',
                    'ducati' => 'دوكاتي',
                    'harley' => 'هارلي ديفيدسون'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'engine_size',
                'attribute_label' => 'حجم المحرك',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '50' => '50 سي سي',
                    '125' => '125 سي سي',
                    '150' => '150 سي سي',
                    '200' => '200 سي سي',
                    '250' => '250 سي سي',
                    '400' => '400 سي سي',
                    '600' => '600 سي سي',
                    '750' => '750 سي سي',
                    '1000' => '1000 سي سي',
                    '1200' => '1200 سي سي'
                ],
                'sort_order' => 2,
                'is_required' => true
            ],
            [
                'attribute_name' => 'type',
                'attribute_label' => 'نوع الموتوسيكل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'sport' => 'سبورت',
                    'cruiser' => 'كروزر',
                    'touring' => 'تورينج',
                    'adventure' => 'أدفنتشر',
                    'street' => 'ستريت',
                    'scooter' => 'سكوتر',
                    'dirt' => 'دراجة ترابية',
                    'cafe_racer' => 'كافيه ريسر'
                ],
                'sort_order' => 3,
                'is_required' => false
            ],
            [
                'attribute_name' => 'condition',
                'attribute_label' => 'حالة الموتوسيكل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'new' => 'جديد',
                    'used' => 'مستعمل',
                    'needs_repair' => 'يحتاج إصلاح',
                    'for_parts' => 'للقطع الغيار',
                    'restored' => 'مرمم'
                ],
                'sort_order' => 4,
                'is_required' => true
            ],
            [
                'attribute_name' => 'year',
                'attribute_label' => 'سنة الصنع',
                'attribute_type' => 'select',
                'attribute_options' => $this->generateYearOptions(),
                'sort_order' => 5,
                'is_required' => false
            ],
            [
                'attribute_name' => 'color',
                'attribute_label' => 'اللون',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'black' => 'أسود',
                    'white' => 'أبيض',
                    'red' => 'أحمر',
                    'blue' => 'أزرق',
                    'green' => 'أخضر',
                    'yellow' => 'أصفر',
                    'orange' => 'برتقالي',
                    'silver' => 'فضي'
                ],
                'sort_order' => 6,
                'is_required' => false
            ]
        ];

        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $motorcycles->id], $attr));
        }
    }

    private function addMobilePhonesAttributes()
    {
        $mobiles = Category::where('slug', 'mobaylat')->first();
        if (!$mobiles) {
            echo "Mobile phones category not found\n";
            return;
        }

        $attributes = [
            [
                'attribute_name' => 'brand',
                'attribute_label' => 'نوع الموبايل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'iphone' => 'آيفون',
                    'samsung' => 'سامسونج',
                    'huawei' => 'هواوي',
                    'xiaomi' => 'شاومي',
                    'oppo' => 'أوبو',
                    'vivo' => 'فيفو',
                    'oneplus' => 'ون بلس',
                    'google' => 'جوجل',
                    'sony' => 'سوني',
                    'lg' => 'إل جي'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'storage',
                'attribute_label' => 'مساحة التخزين',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '16' => '16 جيجا',
                    '32' => '32 جيجا',
                    '64' => '64 جيجا',
                    '128' => '128 جيجا',
                    '256' => '256 جيجا',
                    '512' => '512 جيجا',
                    '1024' => '1 تيرا'
                ],
                'sort_order' => 2,
                'is_required' => false
            ],
            [
                'attribute_name' => 'ram',
                'attribute_label' => 'حجم الرام',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '1' => '1 جيجا',
                    '2' => '2 جيجا',
                    '3' => '3 جيجا',
                    '4' => '4 جيجا',
                    '6' => '6 جيجا',
                    '8' => '8 جيجا',
                    '12' => '12 جيجا',
                    '16' => '16 جيجا',
                    '18' => '18 جيجا'
                ],
                'sort_order' => 3,
                'is_required' => false
            ],
            [
                'attribute_name' => 'condition',
                'attribute_label' => 'حالة الموبايل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'new' => 'جديد',
                    'like_new' => 'كالجديد',
                    'good' => 'جيد',
                    'fair' => 'مقبول',
                    'poor' => 'يحتاج إصلاح',
                    'broken' => 'معطل'
                ],
                'sort_order' => 4,
                'is_required' => true
            ],
            [
                'attribute_name' => 'color',
                'attribute_label' => 'لون الموبايل',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'black' => 'أسود',
                    'white' => 'أبيض',
                    'blue' => 'أزرق',
                    'red' => 'أحمر',
                    'gold' => 'ذهبي',
                    'silver' => 'فضي',
                    'green' => 'أخضر',
                    'purple' => 'بنفسجي',
                    'pink' => 'وردي',
                    'gray' => 'رمادي'
                ],
                'sort_order' => 5,
                'is_required' => false
            ],
            [
                'attribute_name' => 'battery',
                'attribute_label' => 'البطارية (مللي أمبير)',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '2000' => '2000 مللي أمبير',
                    '2500' => '2500 مللي أمبير',
                    '3000' => '3000 مللي أمبير',
                    '3500' => '3500 مللي أمبير',
                    '4000' => '4000 مللي أمبير',
                    '4500' => '4500 مللي أمبير',
                    '5000' => '5000 مللي أمبير',
                    '5500' => '5500 مللي أمبير',
                    '6000' => '6000 مللي أمبير'
                ],
                'sort_order' => 6,
                'is_required' => false
            ],
            [
                'attribute_name' => 'screen_size',
                'attribute_label' => 'حجم الشاشة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '4.0' => '4.0 بوصة',
                    '4.5' => '4.5 بوصة',
                    '5.0' => '5.0 بوصة',
                    '5.5' => '5.5 بوصة',
                    '6.0' => '6.0 بوصة',
                    '6.1' => '6.1 بوصة',
                    '6.5' => '6.5 بوصة',
                    '6.7' => '6.7 بوصة',
                    '7.0' => '7.0 بوصة'
                ],
                'sort_order' => 7,
                'is_required' => false
            ]
        ];

        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $mobiles->id], $attr));
        }
    }

    private function addLaptopAttributes()
    {
        $laptops = Category::where('slug', 'labtobat')->first();
        if (!$laptops) {
            echo "Laptops category not found with slug 'labtobat'\n";
            return;
        }

        $attributes = [
            [
                'attribute_name' => 'brand',
                'attribute_label' => 'نوع اللابتوب',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'hp' => 'HP',
                    'dell' => 'Dell',
                    'mac' => 'Mac',
                    'lenovo' => 'Lenovo',
                    'asus' => 'Asus',
                    'acer' => 'Acer',
                    'toshiba' => 'Toshiba',
                    'msi' => 'MSI',
                    'alienware' => 'Alienware',
                    'surface' => 'Microsoft Surface'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'processor',
                'attribute_label' => 'نوع المعالج',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'intel_i3' => 'Intel i3',
                    'intel_i5' => 'Intel i5',
                    'intel_i7' => 'Intel i7',
                    'intel_i9' => 'Intel i9',
                    'amd_ryzen3' => 'AMD Ryzen 3',
                    'amd_ryzen5' => 'AMD Ryzen 5',
                    'amd_ryzen7' => 'AMD Ryzen 7',
                    'amd_ryzen9' => 'AMD Ryzen 9',
                    'm1' => 'Apple M1',
                    'm2' => 'Apple M2',
                    'm3' => 'Apple M3',
                    'm4' => 'Apple M4'
                ],
                'sort_order' => 2,
                'is_required' => false
            ],
            [
                'attribute_name' => 'ram',
                'attribute_label' => 'حجم الرام',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '4' => '4 جيجا',
                    '8' => '8 جيجا',
                    '16' => '16 جيجا',
                    '32' => '32 جيجا',
                    '64' => '64 جيجا',
                    '128' => '128 جيجا'
                ],
                'sort_order' => 3,
                'is_required' => false
            ],
            [
                'attribute_name' => 'storage',
                'attribute_label' => 'مساحة التخزين',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '128_ssd' => '128 جيجا SSD',
                    '256_ssd' => '256 جيجا SSD',
                    '512_ssd' => '512 جيجا SSD',
                    '1024_ssd' => '1 تيرا SSD',
                    '2048_ssd' => '2 تيرا SSD',
                    '500_hdd' => '500 جيجا HDD',
                    '1000_hdd' => '1 تيرا HDD',
                    '2000_hdd' => '2 تيرا HDD'
                ],
                'sort_order' => 4,
                'is_required' => false
            ],
            [
                'attribute_name' => 'screen_size',
                'attribute_label' => 'حجم الشاشة',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '11' => '11 بوصة',
                    '13' => '13 بوصة',
                    '14' => '14 بوصة',
                    '15' => '15 بوصة',
                    '16' => '16 بوصة',
                    '17' => '17 بوصة',
                    '18' => '18 بوصة'
                ],
                'sort_order' => 5,
                'is_required' => false
            ],
            [
                'attribute_name' => 'condition',
                'attribute_label' => 'حالة اللابتوب',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'new' => 'جديد',
                    'like_new' => 'كالجديد',
                    'good' => 'جيد',
                    'fair' => 'مقبول',
                    'needs_repair' => 'يحتاج إصلاح',
                    'for_parts' => 'للقطع الغيار'
                ],
                'sort_order' => 6,
                'is_required' => true
            ]
        ];

        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $laptops->id], $attr));
        }
    }

    private function addApartmentsForSaleAttributes()
    {
        $apartments = Category::where('slug', 'shkk-llbyaa')->first();
        if (!$apartments) {
            echo "Apartments for sale category not found\n";
            return;
        }

        $attributes = [
            [
                'attribute_name' => 'purpose',
                'attribute_label' => 'الغرض',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'sale' => 'للبيع',
                    'rent' => 'للإيجار',
                    'rent_furnished' => 'للإيجار مفروش'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'rooms',
                'attribute_label' => 'عدد الغرف',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '1' => 'غرفة واحدة',
                    '2' => 'غرفتان',
                    '3' => '3 غرف',
                    '4' => '4 غرف',
                    '5' => '5 غرف',
                    '6' => '6 غرف',
                    '7+' => '7 غرف أو أكثر'
                ],
                'sort_order' => 2,
                'is_required' => true
            ],
            [
                'attribute_name' => 'bathrooms',
                'attribute_label' => 'عدد الحمامات',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '1' => 'حمام واحد',
                    '2' => 'حمامان',
                    '3' => '3 حمامات',
                    '4' => '4 حمامات',
                    '5+' => '5 حمامات أو أكثر'
                ],
                'sort_order' => 3,
                'is_required' => false
            ],
            [
                'attribute_name' => 'area',
                'attribute_label' => 'المساحة (متر مربع)',
                'attribute_type' => 'input',
                'attribute_options' => null,
                'sort_order' => 4,
                'is_required' => false
            ],
            [
                'attribute_name' => 'floor',
                'attribute_label' => 'الدور',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'ground' => 'الدور الأرضي',
                    '1' => 'الدور الأول',
                    '2' => 'الدور الثاني',
                    '3' => 'الدور الثالث',
                    '4' => 'الدور الرابع',
                    '5' => 'الدور الخامس',
                    '6+' => 'الدور السادس أو أعلى'
                ],
                'sort_order' => 5,
                'is_required' => false
            ],
            [
                'attribute_name' => 'finishing',
                'attribute_label' => 'التشطيب',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'finished' => 'مشطبة',
                    'semi_finished' => 'نصف تشطيب',
                    'core_shell' => 'على الطوب',
                    'luxury' => 'تشطيب فاخر',
                    'super_luxury' => 'تشطيب سوبر لوكس'
                ],
                'sort_order' => 6,
                'is_required' => false
            ]
        ];

        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $apartments->id], $attr));
        }
    }

    private function addVillaAttributes()
    {
        $villas = Category::where('slug', 'fylat')->first();
        if (!$villas) {
            echo "Villas category not found with slug 'fylat'\n";
            return;
        }

        $attributes = [
            [
                'attribute_name' => 'purpose',
                'attribute_label' => 'الغرض',
                'attribute_type' => 'select',
                'attribute_options' => [
                    'sale' => 'للبيع',
                    'rent' => 'للإيجار',
                    'rent_furnished' => 'للإيجار مفروش'
                ],
                'sort_order' => 1,
                'is_required' => true
            ],
            [
                'attribute_name' => 'rooms',
                'attribute_label' => 'عدد الغرف',
                'attribute_type' => 'select',
                'attribute_options' => [
                    '3' => '3 غرف',
                    '4' => '4 غرف',
                    '5' => '5 غرف',
                    '6' => '6 غرف',
                    '7' => '7 غرف',
                    '8+' => '8 غرف أو أكثر'
                ],
                'sort_order' => 2,
                'is_required' => true
            ],
            [
                'attribute_name' => 'land_area',
                'attribute_label' => 'مساحة الأرض (متر مربع)',
                'attribute_type' => 'input',
                'attribute_options' => null,
                'sort_order' => 3,
                'is_required' => false
            ],
            [
                'attribute_name' => 'built_area',
                'attribute_label' => 'مساحة البناء (متر مربع)',
                'attribute_type' => 'input',
                'attribute_options' => null,
                'sort_order' => 4,
                'is_required' => false
            ]
        ];

        foreach ($attributes as $attr) {
            CategoryAttribute::create(array_merge(['category_id' => $villas->id], $attr));
        }
    }

    private function generateYearOptions()
    {
        $currentYear = date('Y');
        $years = [];
        for ($year = $currentYear; $year >= 1990; $year--) {
            $years[$year] = (string)$year;
        }
        return $years;
    }

    private function printCategoryTree()
    {
        echo "\n🌳 Category Tree Structure:\n";
        $roots = Category::whereIsRoot()->get();

        foreach ($roots as $root) {
            $this->printNode($root, 0);
        }
    }

    private function printNode($node, $depth)
    {
        $indent = str_repeat('  ', $depth);
        $attributesCount = $node->attributes()->count();
        echo "{$indent}- {$node->name} ({$node->slug}) [{$attributesCount} attributes]\n";

        foreach ($node->children as $child) {
            $this->printNode($child, $depth + 1);
        }
    }
}
