@extends('partials.admin')
@section('title', 'تفاصيل الإعلان الممول')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'sponsored-ads'])
@php use Illuminate\Support\Str; @endphp

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'تفاصيل الإعلان الممول', 'hasBack' => true, 'backTitle' => 'الإعلانات الممولة', 'backUrl' => route('admin.sponsored-ads.index')])

            <div class="row">
                <!-- معلومات الرعاية -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">معلومات الرعاية</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <p><strong>ID الرعاية:</strong> {{ $sponsoredAd->id }}</p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>التكلفة:</strong> {{ money($sponsoredAd->cost) }}</p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>إجمالي الدقائق:</strong> {{ $sponsoredAd->total_minutes }} دقيقة</p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>الدقائق المتبقية:</strong> {{ $sponsoredAd->getRemainingMinutes() }} دقيقة</p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>الحالة:</strong> 
                                        <span class="badge bg-{{ $sponsoredAd->isActive() ? 'success' : ($sponsoredAd->isExpired() ? 'danger' : 'warning') }}">
                                            {{ $sponsoredAd->status }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>النشاط:</strong> 
                                        <span class="badge bg-{{ $sponsoredAd->is_active ? 'success' : 'secondary' }}">
                                            {{ $sponsoredAd->is_active ? 'نشط' : 'غير نشط' }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>بداية الرعاية:</strong> 
                                        {{ $sponsoredAd->started_at ? $sponsoredAd->started_at->format('Y-m-d H:i:s') : 'لم تبدأ بعد' }}
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>انتهاء الرعاية:</strong> 
                                        {{ $sponsoredAd->expires_at ? $sponsoredAd->expires_at->format('Y-m-d H:i:s') : 'غير محدد' }}
                                    </p>
                                </div>
                                <div class="col-sm-12">
                                    <p><strong>الوقت المتبقي:</strong> {{ $sponsoredAd->getRemainingTimeText() }}</p>
                                </div>
                                <div class="col-sm-12">
                                    <p><strong>تاريخ الإنشاء:</strong> {{ $sponsoredAd->created_at->format('Y-m-d H:i:s') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات الإعلان -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">معلومات الإعلان</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <p><strong>عنوان الإعلان:</strong> 
                                        <a href="{{ route('admin.ads.show', $sponsoredAd->ad->slug) }}" target="_blank">
                                            {{ $sponsoredAd->ad->title }}
                                        </a>
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>السعر:</strong> {{ money($sponsoredAd->ad->price) }}</p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>الفئة:</strong> {{ $sponsoredAd->ad->category->name }}</p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>حالة الإعلان:</strong> 
                                        <span class="badge bg-{{ $sponsoredAd->ad->status->color() }}">
                                            {{ $sponsoredAd->ad->status->label() }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-sm-6">
                                    <p><strong>تاريخ الإنشاء:</strong> {{ $sponsoredAd->ad->created_at->format('Y-m-d') }}</p>
                                </div>
                                <div class="col-sm-12">
                                    <p><strong>الوصف:</strong></p>
                                    <p class="text-muted">{{ \Illuminate\Support\Str::limit($sponsoredAd->ad->description, 200) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات المستخدم -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">معلومات المستخدم</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-3">
                                    <p><strong>الاسم:</strong> 
                                        <a href="{{ route('admin.users.show', $sponsoredAd->ad->user->id) }}" target="_blank">
                                            {{ $sponsoredAd->ad->user->name }}
                                        </a>
                                    </p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>اسم المستخدم:</strong> {{ $sponsoredAd->ad->user->username }}</p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>البريد الإلكتروني:</strong> {{ $sponsoredAd->ad->user->email }}</p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>الهاتف:</strong> {{ $sponsoredAd->ad->user->mobile ?? 'غير محدد' }}</p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>التقييم:</strong> {{ $sponsoredAd->ad->user->rating }}/5</p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>عدد الإعلانات:</strong> {{ $sponsoredAd->ad->user->Number_Ads }}</p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>موثوق:</strong> 
                                        <span class="badge bg-{{ $sponsoredAd->ad->user->is_trusted ? 'success' : 'secondary' }}">
                                            {{ $sponsoredAd->ad->user->is_trusted ? 'نعم' : 'لا' }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-sm-3">
                                    <p><strong>مندوب:</strong> 
                                        <span class="badge bg-{{ $sponsoredAd->ad->user->is_broker ? 'info' : 'secondary' }}">
                                            {{ $sponsoredAd->ad->user->is_broker ? 'نعم' : 'لا' }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- صور الإعلان -->
            @if($sponsoredAd->ad->media->count() > 0)
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">صور الإعلان</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($sponsoredAd->ad->media->take(6) as $media)
                                <div class="col-md-2 col-sm-4 mb-3">
                                    <img src="{{ $media->url }}" class="img-fluid rounded" alt="صورة الإعلان" style="height: 100px; object-fit: cover; width: 100%;">
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- الإجراءات -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">الإجراءات</h4>
                        </div>
                        <div class="card-body">
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.ads.show', $sponsoredAd->ad->slug) }}" class="btn btn-info" target="_blank">
                                    <i class="fas fa-eye"></i> عرض الإعلان
                                </a>
                                <a href="{{ route('admin.users.show', $sponsoredAd->ad->user->id) }}" class="btn btn-secondary" target="_blank">
                                    <i class="fas fa-user"></i> عرض المستخدم
                                </a>
                                
                                @if($sponsoredAd->status == 'pending')
                                    @if($sponsoredAd->ad->status->value === 'published')
                                        <!-- الإعلان منشور، فقط تفعيل الرعاية -->
                                        <form method="POST" action="{{ route('admin.sponsored-ads.activate', $sponsoredAd) }}" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-success" onclick="return confirm('تفعيل الرعاية؟')">
                                                <i class="fas fa-play"></i> تفعيل الرعاية
                                            </button>
                                        </form>
                                    @else
                                        <!-- الإعلان غير منشور، إظهار زر الموافقة والتفعيل -->
                                        <div class="alert alert-warning mb-3">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>تنبيه:</strong> الإعلان غير منشور حالياً. يجب نشر الإعلان أولاً لتفعيل الرعاية.
                                        </div>

                                        <form method="POST" action="{{ route('admin.sponsored-ads.approve-and-activate', $sponsoredAd) }}" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-primary me-2" onclick="return confirm('سيتم نشر الإعلان وتفعيل الرعاية. هل تريد المتابعة؟')">
                                                <i class="fas fa-check-double"></i> نشر الإعلان وتفعيل الرعاية
                                            </button>
                                        </form>

                                        <a href="{{ route('admin.ads.edit', $sponsoredAd->ad->slug) }}" class="btn btn-info me-2" target="_blank">
                                            <i class="fas fa-edit"></i> مراجعة وتعديل الإعلان
                                        </a>

                                        <form method="POST" action="{{ route('admin.sponsored-ads.activate', $sponsoredAd) }}" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-success" onclick="return confirm('تفعيل الرعاية؟ (الإعلان يجب أن يكون منشوراً)')">
                                                <i class="fas fa-play"></i> تفعيل الرعاية فقط
                                            </button>
                                        </form>
                                    @endif
                                @elseif($sponsoredAd->is_active)
                                    <form method="POST" action="{{ route('admin.sponsored-ads.deactivate', $sponsoredAd) }}" style="display: inline;">
                                        @csrf
                                        <button type="submit" class="btn btn-warning" onclick="return confirm('إيقاف الرعاية؟')">
                                            <i class="fas fa-pause"></i> إيقاف الرعاية
                                        </button>
                                    </form>
                                @endif
                                
                                <form method="POST" action="{{ route('admin.sponsored-ads.destroy', $sponsoredAd) }}" style="display: inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('حذف سجل الرعاية؟')">
                                        <i class="fas fa-trash"></i> حذف السجل
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- CONTAINER END -->
    </div>
</div>
@endsection
