<?php

namespace App\Console\Commands;

use App\Models\Ad;
use App\Helpers\SecurityHelper;
use Illuminate\Console\Command;

class CleanExistingAdsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:clean-descriptions {--dry-run : Show what would be cleaned without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean HTML tags from existing ad descriptions for security';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $ads = Ad::whereNotNull('description')->get();
        $totalAds = $ads->count();
        $cleanedCount = 0;
        $errorCount = 0;

        if ($totalAds === 0) {
            $this->info('No ads with descriptions found.');
            return;
        }

        $this->info("Found {$totalAds} ads with descriptions to process...");
        $progressBar = $this->output->createProgressBar($totalAds);
        $progressBar->start();

        foreach ($ads as $ad) {
            try {
                $originalDescription = $ad->description;
                $cleanedDescription = SecurityHelper::cleanAdDescription($originalDescription);

                // التحقق من وجود تغيير
                if ($originalDescription !== $cleanedDescription) {
                    if (!$dryRun) {
                        $ad->update(['description' => $cleanedDescription]);
                    }
                    $cleanedCount++;

                    if ($dryRun) {
                        $this->newLine();
                        $this->warn("Ad ID {$ad->id} would be cleaned:");
                        $this->line("Original: " . substr($originalDescription, 0, 100) . '...');
                        $this->line("Cleaned:  " . substr($cleanedDescription, 0, 100) . '...');
                    }
                }
            } catch (\Exception $e) {
                $errorCount++;
                if ($dryRun) {
                    $this->newLine();
                    $this->error("Error processing Ad ID {$ad->id}: " . $e->getMessage());
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        if ($dryRun) {
            $this->info("DRY RUN RESULTS:");
            $this->info("- Total ads processed: {$totalAds}");
            $this->info("- Ads that would be cleaned: {$cleanedCount}");
            $this->info("- Errors encountered: {$errorCount}");
            $this->info("Run without --dry-run to apply changes.");
        } else {
            $this->info("CLEANING COMPLETED:");
            $this->info("- Total ads processed: {$totalAds}");
            $this->info("- Ads cleaned: {$cleanedCount}");
            $this->info("- Errors encountered: {$errorCount}");
        }
    }
}
