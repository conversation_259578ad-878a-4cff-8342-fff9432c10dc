const CountDownTimer=(n,s)=>{const e=document.getElementsByClassName(n);for(let n=0;n<e.length;n++){const o=e[n],d=o.innerText,t=CountDown(d);o.innerHTML="classic"==s?`<span id="${s}_days${n}">${t.days}</span>D :<span id="${s}_hours${n}">${t.hours}</span>H : <span id="${s}_minutes${n}">${t.minutes}</span>M : <span id="${s}_seconds${n}">${t.seconds}</span>S`:"slider"==s?`\n <div class="countdown-single">\n <h5 id="${s}_days${n}">${t.days}</h5>\n <span>Days</span>\n </div>\n <div class="countdown-single">\n <h5 id="${s}_hours${n}">${t.hours}</h5>\n <span>Hours</span>\n </div>\n <div class="countdown-single">\n <h5 id="${s}_minutes${n}">${t.minutes}</h5>\n <span>Minutes</span>\n </div>\n <div class="countdown-single">\n <h5 id="${s}_seconds${n}">${t.seconds}</h5>\n <span>Seconds</span>\n </div>\n `:`<span id="${s}_days${n}">${t.days}</span>D :<span id="${s}_hours${n}">${t.hours}</span>H : <span id="${s}_minutes${n}">${t.minutes}</span>M : <span id="${s}_seconds${n}">${t.seconds}</span>S`,setInterval((n=>{const e=CountDown(d);document.getElementById(`${s}_days${n}`).innerHTML=e.days,document.getElementById(`${s}_hours${n}`).innerHTML=e.hours,document.getElementById(`${s}_minutes${n}`).innerHTML=e.minutes,document.getElementById(`${s}_seconds${n}`).innerHTML=e.seconds}),1e3,n)}},CountDown=n=>{const s=(new Date).getTime(),e=new Date(n).getTime()-s;return{days:Math.floor(e/864e5),hours:Math.floor(e%864e5/36e5),minutes:Math.floor(e%36e5/6e4),seconds:Math.floor(e%6e4/1e3)}};CountDownTimer("countdown-classic","classic"),CountDownTimer("countdown-slider","slider"),CountDownTimer("countdown-default","default");