@extends('partials.admin')

@section('title', 'Report Details')

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'reports'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'Report Details', 'hasBack' => true, 'backTitle' => 'Reports', 'backUrl' => route('admin.reports.index')])

            <div class="row">
                <!-- Report Details -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Report Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Report ID:</label>
                                        <p><code>{{ $report->id }}</code></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Type:</label>
                                        <p><span class="badge bg-info">{{ $report->getTypeLabel() }}</span></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Reason:</label>
                                        <p><span class="badge bg-warning">{{ $report->reason_label }}</span></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Status:</label>
                                        <p>
                                            <span class="badge bg-{{ $report->status === 'pending' ? 'warning' : ($report->status === 'resolved' ? 'success' : 'secondary') }}">
                                                {{ $report->status_label }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Description:</label>
                                        <p class="border p-3 rounded">{{ $report->description ?: 'No description provided.' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Reported Date:</label>
                                        <p>{{ $report->created_at->format('M d, Y \a\t H:i') }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Last Updated:</label>
                                        <p>{{ $report->updated_at->format('M d, Y \a\t H:i') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reporter Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Reporter Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <img src="{{ $report->reporter->avatar ?? '/assets/images/default-avatar.png' }}" 
                                     alt="{{ $report->reporter->name }}" 
                                     class="avatar avatar-lg rounded-circle me-3">
                                <div>
                                    <h5 class="mb-1">{{ $report->reporter->name }}</h5>
                                    <p class="text-muted mb-1">{{ $report->reporter->email }}</p>
                                    <small class="text-muted">Member since {{ $report->reporter->created_at->format('M Y') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reported Content -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Reported Content</h4>
                        </div>
                        <div class="card-body">
                            @if($report->reportable)
                                @if($report->reportable_type === 'App\Models\Ad')
                                    <div class="d-flex align-items-center">
                                        @if($report->reportable->media->first())
                                            <img src="{{ $report->reportable->media->first()->url }}" 
                                                 alt="{{ $report->reportable->title }}" 
                                                 class="rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                        @endif
                                        <div>
                                            <h6>{{ $report->reportable->title }}</h6>
                                            <p class="text-muted mb-1">Price: ${{ number_format($report->reportable->price) }}</p>
                                            <a href="{{ route('auction-details', $report->reportable->slug) }}" 
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>View Ad
                                            </a>
                                        </div>
                                    </div>
                                @elseif($report->reportable_type === 'App\Models\User')
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $report->reportable->avatar ?? '/assets/images/default-avatar.png' }}" 
                                             alt="{{ $report->reportable->name }}" 
                                             class="avatar avatar-lg rounded-circle me-3">
                                        <div>
                                            <h6>{{ $report->reportable->name }}</h6>
                                            <p class="text-muted mb-1">{{ $report->reportable->email }}</p>
                                            <a href="{{ url('user-profile/' . $report->reportable->username) }}" 
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>View Profile
                                            </a>
                                        </div>
                                    </div>
                                @elseif($report->reportable_type === 'App\Models\UserRating')
                                    <div class="border p-3 rounded">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>Rating: {{ $report->reportable->rating }}/5</strong>
                                                <div class="rating-stars">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star {{ $i <= $report->reportable->rating ? 'text-warning' : 'text-muted' }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <small class="text-muted">{{ $report->reportable->created_at->diffForHumans() }}</small>
                                        </div>
                                        @if($report->reportable->comment)
                                            <p class="mb-2">"{{ $report->reportable->comment }}"</p>
                                        @endif
                                        <div class="d-flex justify-content-between">
                                            <small>By: {{ $report->reportable->rater->name ?? 'Unknown' }}</small>
                                            <small>For: {{ $report->reportable->rated->name ?? 'Unknown' }}</small>
                                        </div>
                                        <div class="mt-2">
                                            <a href="{{ route('users.rate', $report->reportable->rated_id) }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>View Rating Page
                                            </a>
                                        </div>
                                    </div>
                                @elseif($report->reportable_type === 'App\Models\ProductReview')
                                    <div class="border p-3 rounded">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>Product Review: {{ $report->reportable->rating }}/5</strong>
                                                <div class="rating-stars">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star {{ $i <= $report->reportable->rating ? 'text-warning' : 'text-muted' }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <small class="text-muted">{{ $report->reportable->created_at->diffForHumans() }}</small>
                                        </div>
                                        @if($report->reportable->comment)
                                            <p class="mb-2">"{{ $report->reportable->comment }}"</p>
                                        @endif
                                        <div class="d-flex justify-content-between">
                                            <small>By: {{ $report->reportable->reviewer->name ?? 'Unknown' }}</small>
                                            <small>Product: {{ $report->reportable->ad->title ?? 'Unknown' }}</small>
                                        </div>
                                        <div class="mt-2">
                                            <a href="{{ route('auction-details', $report->reportable->ad->slug) }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>View Product
                                            </a>
                                        </div>
                                    </div>
                                @elseif($report->reportable_type === 'App\Models\Comment')
                                    <div class="border p-3 rounded">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>Blog Comment</strong>
                                            </div>
                                            <small class="text-muted">{{ $report->reportable->created_at->diffForHumans() }}</small>
                                        </div>
                                        <p class="mb-2">"{{ $report->reportable->content }}"</p>
                                        <div class="d-flex justify-content-between">
                                            <small>By: {{ $report->reportable->user->name ?? 'Unknown' }}</small>
                                            <small>Post: {{ $report->reportable->post->title ?? 'Unknown' }}</small>
                                        </div>
                                        <div class="mt-2">
                                            <a href="{{ route('blog.show', $report->reportable->post->slug) }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-1"></i>View Blog Post
                                            </a>
                                        </div>
                                    </div>
                                @elseif($report->reportable_type === 'App\Models\ChMessage')
                                    <div class="border p-3 rounded">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>Chat Message</strong>
                                            </div>
                                            <small class="text-muted">{{ $report->reportable->created_at->diffForHumans() }}</small>
                                        </div>
                                        <p class="mb-2">"{{ $report->reportable->body }}"</p>
                                        <div class="d-flex justify-content-between">
                                            <small>From: {{ $report->reportable->fromUser->name ?? 'Unknown' }}</small>
                                            <small>To: {{ $report->reportable->toUser->name ?? 'Unknown' }}</small>
                                        </div>
                                        <div class="mt-2">
                                            <a href="{{ route('chatify') }}?user={{ $report->reportable->from_id }}"
                                               class="btn btn-sm btn-outline-primary" target="_blank">
                                                <i class="fas fa-comments me-1"></i>View Chat
                                            </a>
                                        </div>
                                    </div>
                                @endif
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    The reported content has been deleted or is no longer available.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Actions Sidebar -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Actions</h4>
                        </div>
                        <div class="card-body">
                            <!-- Warning Actions -->
                            <div class="mb-4">
                                <h6 class="fw-semibold mb-3">Warning Actions</h6>
                                <button type="button" class="btn btn-warning w-100 mb-2" onclick="showWarningModal()">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Issue Warning
                                </button>
                            </div>

                            <!-- Content Actions -->
                            @if($report->reportable)
                                <div class="mb-4">
                                    <h6 class="fw-semibold mb-3">
                                        <i class="fas fa-cogs me-2"></i>إجراءات المحتوى
                                    </h6>
                                    <div class="alert alert-warning mb-3">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <small>تحذير: حذف المحتوى إجراء نهائي لا يمكن التراجع عنه</small>
                                    </div>

                                    @if($report->reportable_type === 'App\Models\Ad')
                                        <button type="button" class="btn btn-danger w-100 mb-2" onclick="deleteContent('delete_ad')">
                                            <i class="fas fa-trash me-2"></i>حذف الإعلان نهائياً
                                        </button>
                                        <a href="{{ route('auction-details', $report->reportable->slug) }}"
                                           class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                            <i class="fas fa-external-link-alt me-2"></i>عرض الإعلان
                                        </a>
                                    @elseif($report->reportable_type === 'App\Models\Comment')
                                        <button type="button" class="btn btn-danger w-100 mb-2" onclick="deleteContent('delete_comment')">
                                            <i class="fas fa-trash me-2"></i>حذف التعليق نهائياً
                                        </button>
                                        @if($report->reportable->post)
                                            <a href="{{ route('blog.show', $report->reportable->post->slug) }}"
                                               class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                                <i class="fas fa-external-link-alt me-2"></i>عرض المقال
                                            </a>
                                        @endif
                                    @elseif(in_array($report->reportable_type, ['App\Models\ProductReview', 'App\Models\UserRating']))
                                        <button type="button" class="btn btn-danger w-100 mb-2" onclick="deleteContent('delete_review')">
                                            <i class="fas fa-trash me-2"></i>حذف التقييم نهائياً
                                        </button>
                                        @if($report->reportable_type === 'App\Models\ProductReview' && $report->reportable->ad)
                                            <a href="{{ route('auction-details', $report->reportable->ad->slug) }}"
                                               class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                                <i class="fas fa-external-link-alt me-2"></i>عرض المنتج
                                            </a>
                                        @elseif($report->reportable_type === 'App\Models\UserRating' && $report->reportable->rated)
                                            <a href="{{ url('user-profile/' . $report->reportable->rated->username) }}"
                                               class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                                <i class="fas fa-external-link-alt me-2"></i>عرض ملف المستخدم
                                            </a>
                                        @endif
                                    @elseif($report->reportable_type === 'App\Models\ChMessage')
                                        <button type="button" class="btn btn-danger w-100 mb-2" onclick="deleteContent('delete_message')">
                                            <i class="fas fa-trash me-2"></i>حذف الرسالة نهائياً
                                        </button>
                                    @endif
                                </div>
                            @else
                                <div class="mb-4">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        المحتوى المبلغ عنه غير موجود أو تم حذفه مسبقاً
                                    </div>
                                </div>
                            @endif

                            <!-- Report Status Actions -->
                            @if($report->status === 'pending')
                                <div class="mb-4">
                                    <h6 class="fw-semibold mb-3">Report Status</h6>
                                    <form method="POST" action="{{ route('admin.reports.update-status', $report) }}" class="mb-3">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="status" value="resolved">
                                        <button type="submit" class="btn btn-success w-100 mb-2">
                                            <i class="fas fa-check me-2"></i>Mark as Resolved
                                        </button>
                                    </form>

                                    <form method="POST" action="{{ route('admin.reports.update-status', $report) }}" class="mb-3">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="status" value="dismissed">
                                        <button type="submit" class="btn btn-outline-danger w-100 mb-2">
                                            <i class="fas fa-times me-2"></i>Dismiss Report
                                        </button>
                                    </form>
                                </div>
                            @endif

                            <form method="POST" action="{{ route('admin.reports.update-status', $report) }}">
                                @csrf
                                @method('PATCH')
                                <div class="mb-3">
                                    <label for="status" class="form-label">Change Status:</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="pending" {{ $report->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="reviewed" {{ $report->status === 'reviewed' ? 'selected' : '' }}>Reviewed</option>
                                        <option value="resolved" {{ $report->status === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                        <option value="dismissed" {{ $report->status === 'dismissed' ? 'selected' : '' }}>Dismissed</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="admin_notes" class="form-label">Admin Notes:</label>
                                    <textarea name="admin_notes" id="admin_notes" class="form-control" rows="4" 
                                              placeholder="Add your notes about this report...">{{ $report->admin_notes }}</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-save me-2"></i>Update Report
                                </button>
                            </form>
                        </div>
                    </div>

                    @if($report->reviewer)
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Reviewed By</h4>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <img src="{{ $report->reviewer->avatar ?? '/assets/images/default-avatar.png' }}" 
                                         alt="{{ $report->reviewer->name }}" 
                                         class="avatar avatar-md rounded-circle me-3">
                                    <div>
                                        <h6 class="mb-1">{{ $report->reviewer->name }}</h6>
                                        <small class="text-muted">{{ $report->reviewed_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                                @if($report->admin_notes)
                                    <div class="mt-3">
                                        <label class="form-label fw-semibold">Notes:</label>
                                        <p class="border p-2 rounded bg-light">{{ $report->admin_notes }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1" aria-labelledby="warningModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="warningModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Issue Warning to {{ $report->reporter->name }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="warningForm">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="user_id" value="{{ $report->reporter->id }}">
                    <input type="hidden" name="report_id" value="{{ $report->id }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="warningType" class="form-label">Warning Type *</label>
                                <select class="form-select" id="warningType" name="type" required>
                                    <option value="">Select Warning Type</option>
                                    <option value="posting_ban">منع النشر</option>
                                    <option value="chat_ban">منع الشات</option>
                                    <option value="bidding_ban">منع المزايدة</option>
                                    <option value="comment_ban">منع التعليق</option>
                                    <option value="general_warning">تحذير عام</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="durationDays" class="form-label">Duration (Days)</label>
                                <input type="number" class="form-control" id="durationDays" name="duration_days"
                                       min="1" max="365" placeholder="Leave empty for permanent">
                                <small class="form-text text-muted">Leave empty for permanent warning</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="warningTitle" class="form-label">Warning Title *</label>
                        <input type="text" class="form-control" id="warningTitle" name="title" required
                               placeholder="e.g., Inappropriate Content Posting">
                    </div>

                    <div class="mb-3">
                        <label for="warningReason" class="form-label">Reason *</label>
                        <textarea class="form-control" id="warningReason" name="reason" rows="3" required
                                  placeholder="Explain why this warning is being issued..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="adminNotes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="adminNotes" name="admin_notes" rows="2"
                                  placeholder="Internal notes for other admins..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>Issue Warning
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showWarningModal() {
    const modal = new bootstrap.Modal(document.getElementById('warningModal'));
    modal.show();
}

function deleteContent(action) {
    // تأكيد الحذف مع رسالة واضحة
    const confirmMessage = getDeleteConfirmMessage(action);
    if (!confirm(confirmMessage)) {
        return;
    }

    // إظهار حالة التحميل
    const button = event.target;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحذف...';

    fetch('{{ route("admin.warnings.delete-content") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            report_id: '{{ $report->id }}',
            action: action
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // تحديث حالة البلاغ إلى محلول
            updateReportStatus('resolved');
            // إعادة تحميل الصفحة بعد 3 ثواني
            setTimeout(() => location.reload(), 3000);
        } else {
            showAlert('danger', data.message || 'حدث خطأ أثناء حذف المحتوى');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
    })
    .finally(() => {
        // إعادة تعيين حالة الزر
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function getDeleteConfirmMessage(action) {
    switch(action) {
        case 'delete_ad':
            return 'هل أنت متأكد من حذف هذا الإعلان؟ هذا الإجراء لا يمكن التراجع عنه.';
        case 'delete_comment':
            return 'هل أنت متأكد من حذف هذا التعليق؟ هذا الإجراء لا يمكن التراجع عنه.';
        case 'delete_review':
            return 'هل أنت متأكد من حذف هذا التقييم؟ هذا الإجراء لا يمكن التراجع عنه.';
        case 'delete_message':
            return 'هل أنت متأكد من حذف هذه الرسالة؟ هذا الإجراء لا يمكن التراجع عنه.';
        default:
            return 'هل أنت متأكد من حذف هذا المحتوى؟ هذا الإجراء لا يمكن التراجع عنه.';
    }
}

function updateReportStatus(status) {
    // تحديث حالة البلاغ تلقائياً بعد الحذف
    fetch('{{ route("admin.reports.update-status", $report) }}', {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: `status=${status}&admin_notes=تم حذف المحتوى المبلغ عنه`
    })
    .catch(error => {
        console.error('Error updating report status:', error);
    });
}

// Handle warning form submission
document.getElementById('warningForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Issuing Warning...';

    fetch('{{ route("admin.warnings.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('warningModal'));
            modal.hide();

            // Reset form
            this.reset();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while issuing warning.');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Find container to show alert
    const container = document.querySelector('.main-container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // Auto remove after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>
@endpush

@endsection
