<?php

namespace App\Repositories\Category;

use App\Abstracts\BaseCrudRepository;
use App\Contracts\Repositories\CategoryRepositoryInterface;
use App\Models\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

class CategoryRepository extends BaseCrudRepository implements CategoryRepositoryInterface
{
    /**
     * The fields that should be filtered.
     * 
     * @var array
     */
    protected array $filterable = [
        'name',
        'slug',
    ];

    public function __construct(Category $model)
    {
        parent::__construct($model);
    }

    /**
     * Get the categories with sub categories.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */


    /**
     * Get primary categories (root level) with cache.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPrimaryCategories(): Collection
    {
        return Cache::remember('primary_categories', now()->addHours(24), function () {
            return $this->model->whereIsRoot()->select('id', 'name', 'slug', 'icon', 'image')->get();
        });
    }

    /**
     * Get children of a specific category with cache.
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getChildren(string $slug): Collection
    {
        return Cache::remember("category_children_{$slug}", now()->addHours(12), function () use ($slug) {
            $category = $this->findBySlug($slug);
            return $category->children()->select('id', 'name', 'slug', 'icon', 'image')->get();
        });
    }

    /**
     * Get all descendants of a category (all levels).
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDescendants(string $slug): Collection
    {
        $category = $this->findBySlug($slug);
        return $category->descendants()->select('id', 'name', 'slug', 'icon', 'image', 'depth')->get();
    }

    /**
     * Get category tree structure.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCategoryTree(): Collection
    {
        return $this->model->get()->toTree();
    }

    /**
     * Get category breadcrumb.
     *
     * @param string $slug
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getBreadcrumb(string $slug): Collection
    {
        $category = $this->findBySlug($slug);
        return $category->ancestorsAndSelf()->select('id', 'name', 'slug')->get();
    }

    /**
     * Find a category by slug.
     * 
     * @param string $slug
     * @return \App\Models\Category
     */
    public function findBySlug(string $slug): Category
    {
        return $this->findBy('slug', $slug, function () {
            throw new \Exception('Category not found.');
        });
    }
}