@extends('partials.app')
@section('title', 'Profile')


@section('content')

@php
    use Illuminate\Support\Facades\Storage;
    $user = auth()->user();
    $averageRating = $user->average_rating ?? 0; // التقييم من 1-5 نجوم
    $level = min(floor($user->Number_Ads / 20) + 1, 50); // المستوى من 1-50 حسب عدد الإعلانات
    $totalAds = $user->Number_Ads; // عدد الإعلانات المنشورة فقط
@endphp

<!-- Hero Section -->
<div class="profile-hero-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="hero-content">
                    <nav aria-label="breadcrumb" class="hero-breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item active">Profile Settings</li>
                        </ol>
                    </nav>
                    <h1 class="hero-title">Profile Settings</h1>
                    <p class="hero-subtitle">Manage your account information and preferences</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="dashboard-nav-section">
    <div class="container">
        <div class="nav-tabs-wrapper">
            <ul class="nav nav-tabs dashboard-tabs" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <a class="nav-link" href="{{ route('user.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button" role="tab">
                        <i class="fas fa-user"></i>
                        <span>My Profile</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <form method="POST" action="{{ route('user.logout.handle') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="profile-main-section">
    <div class="container">
        <div class="tab-content" id="profileTabsContent">
            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                <div class="row">
                    <!-- User Info Card -->
                    <div class="col-lg-4">
                        <div class="user-info-card">
                            <div class="user-avatar">
                                <img src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}" alt="{{ $user->name }}">
                                @if($user->is_trusted)
                                    <div class="verified-badge-blue">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                @else
                                    <div class="unverified-badge">
                                        <i class="fas fa-shield-exclamation"></i>
                                    </div>
                                @endif
                            </div>

                            <!-- Identity Verification Button -->
                            @if(!$user->is_trusted)
                            <div class="mt-3 text-center">
                                <a href="{{ route('identity-verification.create') }}" class="btn btn-success btn-sm">
                                    <i class="fas fa-id-card me-2"></i>
                                    توثيق الهوية
                                </a>
                            </div>
                            @endif
                            </div>

                            <div class="user-details">
                                <h3 class="user-name">{{ $user->name }}</h3>

                                <!-- User Badges -->
                                <div class="user-badges">
                                    @if($user->is_broker ?? false)
                                        <span class="badge badge-broker">
                                            <i class="fas fa-handshake"></i>
                                            Representative
                                        </span>
                                    @endif

                                    <span class="badge {{ $user->email_verified_at ? 'badge-verified' : 'badge-unverified' }}">
                                        <i class="fas {{ $user->email_verified_at ? 'fa-check-circle' : 'fa-exclamation-triangle' }}"></i>
                                        {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                                    </span>

                                    <span class="badge badge-level">Level {{ $level }}</span>

                                    @if($user->is_broker)
                                        <span class="badge badge-points">
                                            <i class="fas fa-coins"></i>
                                            {{ number_format($user->points) }} نقطة
                                        </span>
                                    @endif
                                </div>

                                <!-- Premium Rank Display -->
                                @switch($user->rank ?? 0)
                                    @case(1)
                                        <div class="premium-rank-card vip-member">
                                            <div class="rank-header">
                                                <i class="fas fa-crown rank-icon"></i>
                                                <span class="rank-title">VIP MEMBER</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-sparkles"></i>
                                                    <i class="fas fa-sparkles"></i>
                                                    <i class="fas fa-sparkles"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Elite Status • Verified Excellence</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Priority Support</span>
                                                    <span><i class="fas fa-check"></i> Exclusive Deals</span>
                                                    <span><i class="fas fa-check"></i> Premium Features</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @case(2)
                                        <div class="premium-rank-card trader">
                                            <div class="rank-header">
                                                <i class="fas fa-chart-line rank-icon"></i>
                                                <span class="rank-title">TRADER</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Professional Trader • Market Expert</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Advanced Analytics</span>
                                                    <span><i class="fas fa-check"></i> Market Insights</span>
                                                    <span><i class="fas fa-check"></i> Trading Tools</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @case(3)
                                        <div class="premium-rank-card company">
                                            <div class="rank-header">
                                                <i class="fas fa-building rank-icon"></i>
                                                <span class="rank-title">COMPANY</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-certificate"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Business Account • Corporate Solutions</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Bulk Operations</span>
                                                    <span><i class="fas fa-check"></i> Business Support</span>
                                                    <span><i class="fas fa-check"></i> Corporate Features</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @case(4)
                                        <div class="premium-rank-card premium">
                                            <div class="rank-header">
                                                <i class="fas fa-gem rank-icon"></i>
                                                <span class="rank-title">PREMIUM</span>
                                                <div class="rank-sparkles">
                                                    <i class="fas fa-diamond"></i>
                                                    <i class="fas fa-diamond"></i>
                                                </div>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Premium Account • Ultimate Experience</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> All Features</span>
                                                    <span><i class="fas fa-check"></i> Premium Support</span>
                                                    <span><i class="fas fa-check"></i> Exclusive Access</span>
                                                </div>
                                            </div>
                                        </div>
                                    @break
                                    @default
                                        <div class="premium-rank-card regular">
                                            <div class="rank-header">
                                                <i class="fas fa-user rank-icon"></i>
                                                <span class="rank-title">REGULAR MEMBER</span>
                                            </div>
                                            <div class="rank-description">
                                                <span class="rank-subtitle">Standard Account • Getting Started</span>
                                                <div class="rank-features">
                                                    <span><i class="fas fa-check"></i> Basic Features</span>
                                                    <span><i class="fas fa-check"></i> Community Access</span>
                                                    <span><i class="fas fa-check"></i> Standard Support</span>
                                                </div>
                                            </div>
                                        </div>
                                @endswitch

                                @if($user->is_broker)
                                <!-- Broker Points Section -->
                                <div class="broker-points-section">
                                    <div class="points-header">
                                        <h4><i class="fas fa-coins"></i> إحصائيات النقاط</h4>
                                    </div>
                                    <div class="points-stats">
                                        <div class="points-stat-item">
                                            <div class="stat-icon">
                                                <i class="fas fa-wallet"></i>
                                            </div>
                                            <div class="stat-content">
                                                <span class="stat-value">{{ number_format($user->points) }}</span>
                                                <span class="stat-label">الرصيد الحالي</span>
                                            </div>
                                        </div>
                                        <div class="points-stat-item">
                                            <div class="stat-icon">
                                                <i class="fas fa-hand-paper"></i>
                                            </div>
                                            <div class="stat-content">
                                                <span class="stat-value">{{ $user->activeBrokerInterests()->count() }}</span>
                                                <span class="stat-label">اهتمامات نشطة</span>
                                            </div>
                                        </div>
                                        <div class="points-stat-item">
                                            <div class="stat-icon">
                                                <i class="fas fa-trophy"></i>
                                            </div>
                                            <div class="stat-content">
                                                <span class="stat-value">{{ $user->brokerInterests()->where('code_entered', true)->where('status', 'active')->count() }}</span>
                                                <span class="stat-label">أكواد مدخلة</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif

                                <!-- User Stats -->
                                <div class="user-stats">
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="rating-display">
                                                <span class="stat-value">{{ number_format($averageRating, 1) }}</span>
                                                <div class="stars">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        <i class="fas fa-star {{ $i <= round($averageRating) ? 'filled' : '' }}"></i>
                                                    @endfor
                                                </div>
                                            </div>
                                            <span class="stat-label">{{ $user->total_ratings ?? 0 }} reviews</span>
                                        </div>
                                    </div>

                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-bullhorn"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $totalAds }}</span>
                                            <span class="stat-label">Published Ads</span>
                                        </div>
                                    </div>

                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="stat-content">
                                            <span class="stat-value">{{ $user->created_at ? $user->created_at->format('F Y') : 'N/A' }}</span>
                                            <span class="stat-label">Member since</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Area -->
                    <div class="col-lg-8">

                        <!-- Profile Form Card -->
                        <div class="profile-form-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-user-edit"></i>
                                    Personal Information
                                </h3>
                                <p class="card-subtitle">Update your personal details and contact information</p>
                            </div>

                            <div class="card-body">
                                <form action="{{ route('user.profile.handle') }}" method="POST" class="profile-form" enctype="multipart/form-data">
                                    @method('PUT')
                                    @csrf

                            <!-- Avatar Section -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h4 class="section-title">
                                        <i class="fas fa-image"></i>
                                        صورة الملف الشخصي
                                    </h4>
                                </div>
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label">صورة الملف الشخصي</label>
                                            <div class="avatar-upload-wrapper">
                                                <div class="current-avatar">
                                                    <img src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}"
                                                         alt="{{ $user->name }}" id="avatar-preview">
                                                    @if($user->is_trusted)
                                                        <div class="verified-badge">
                                                            <i class="fas fa-check-circle"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                <div class="upload-controls">
                                                    @if($user->is_broker ?? false)
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                                            <strong>تنبيه:</strong> كونك مندوب، لا يمكنك تغيير صورتك الشخصية.
                                                            يرجى التواصل مع الإدارة لتغيير صورتك.
                                                        </div>
                                                    @else
                                                        <input type="file" name="avatar" id="avatar-input"
                                                               accept="image/jpeg,image/png,image/jpg,image/gif,image/webp"
                                                               class="form-control" style="display: none;">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="document.getElementById('avatar-input').click()">
                                                            <i class="fas fa-camera"></i>
                                                            اختر صورة جديدة
                                                        </button>
                                                        <small class="form-text text-muted">
                                                            الحد الأقصى: 5MB. الصيغ المدعومة: JPG, PNG, GIF, WebP
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                            @error('avatar')
                                                <div class="error-message">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Name Section -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h4 class="section-title">
                                        <i class="fas fa-id-card"></i>
                                        الاسم الكامل
                                    </h4>
                                </div>
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label">الاسم الكامل</label>
                                            <div class="input-wrapper">
                                                <i class="input-icon fas fa-user"></i>
                                                <input type="text" name="name" class="form-control"
                                                       placeholder="أدخل اسمك الكامل"
                                                       value="{{ old('name', $user->name) }}"
                                                       @if($user->is_trusted || !$user->canChangeName()) disabled readonly @endif>
                                            </div>
                                            @if($user->is_trusted)
                                                <small class="form-text text-warning">
                                                    <i class="fas fa-lock"></i>
                                                    المستخدمون الموثقون لا يمكنهم تغيير أسمائهم
                                                </small>
                                            @elseif($user->name_changed_at && !$user->canChangeName())
                                                <small class="form-text text-info">
                                                    <i class="fas fa-clock"></i>
                                                    يمكنك تغيير اسمك مرة أخرى بعد {{ $user->days_until_name_change }} يوم
                                                </small>
                                            @else
                                                <small class="form-text text-muted">
                                                    <i class="fas fa-info-circle"></i>
                                                    يمكنك تغيير اسمك مرة كل 15 يوم
                                                </small>
                                            @endif
                                            @error('name')
                                                <div class="error-message">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Personal Details Section -->


                            <!-- Contact Information Section -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h4 class="section-title">
                                        <i class="fas fa-address-book"></i>
                                        Contact Information
                                    </h4>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <x-phone-selectable name="mobile" label="Phone Number" placeholder="Your phone number" value="{{ $user->mobile }}"/>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Email Address</label>
                                            <div class="input-wrapper disabled">
                                                <i class="input-icon fas fa-envelope"></i>
                                                <input type="email" name="email" class="form-control"
                                                       value="{{ $user->email }}" disabled readonly>
                                                <span class="input-note">Email cannot be changed</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Username</label>
                                            <div class="input-wrapper disabled">
                                                <i class="input-icon fas fa-at"></i>
                                                <input type="text" name="username" class="form-control"
                                                       value="{{ $user->username }}" disabled readonly>
                                                <span class="input-note">Username cannot be changed</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <x-gender-selectable label="Gender" name="gender" :selected="$user->gender"/>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information Section -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h4 class="section-title">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Address Information
                                    </h4>
                                </div>
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label">Street Address</label>
                                            <div class="input-wrapper">
                                                <i class="input-icon fas fa-home"></i>
                                                <input type="text" name="address" class="form-control"
                                                       placeholder="Enter your street address"
                                                       value="{{ old('address', $user->address) }}">
                                            </div>
                                            @error('address')
                                                <div class="error-message">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <x-countries-selectable :admin="false" has-labels="true"/>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">Zip Code</label>
                                            <div class="input-wrapper">
                                                <i class="input-icon fas fa-mail-bulk"></i>
                                                <input type="text" name="zip_code" class="form-control"
                                                       placeholder="Enter zip code"
                                                       value="{{ old('zip_code', $user->zip_code) }}">
                                            </div>
                                            @error('zip_code')
                                                <div class="error-message">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Section -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h4 class="section-title">
                                        <i class="fas fa-shield-alt"></i>
                                        Security Settings
                                    </h4>
                                    <p class="section-subtitle">Leave password fields empty if you don't want to change your password</p>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Current Password</label>
                                            <div class="input-wrapper">
                                                <i class="input-icon fas fa-lock"></i>
                                                <input type="password" name="current_password" class="form-control"
                                                       placeholder="Enter current password">
                                                <button type="button" class="password-toggle" onclick="togglePassword(this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            @error('current_password')
                                                <div class="error-message">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">New Password</label>
                                            <div class="input-wrapper">
                                                <i class="input-icon fas fa-key"></i>
                                                <input type="password" name="password" class="form-control"
                                                       placeholder="Enter new password">
                                                <button type="button" class="password-toggle" onclick="togglePassword(this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            @error('password')
                                                <div class="error-message">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Update Profile
                                </button>
                                <button type="reset" class="btn btn-secondary">
                                    <i class="fas fa-undo"></i>
                                    Reset Changes
                                </button>
                            </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
/* Hero Section */
.profile-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0 2rem;
    position: relative;
    overflow: hidden;
}

.profile-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.hero-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.hero-breadcrumb .breadcrumb-item.active {
    color: white;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Dashboard Navigation Tabs */
.dashboard-nav-section {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.nav-tabs-wrapper {
    background: white;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dashboard-tabs {
    border: none;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.dashboard-tabs .nav-item {
    margin: 0;
}

.dashboard-tabs .nav-link {
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    color: #6c757d;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.dashboard-tabs .nav-link:hover {
    background: #f8f9fa;
    color: #495057;
}

.dashboard-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.dashboard-tabs .logout-btn {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
}

.dashboard-tabs .logout-btn:hover {
    background: #dc3545;
    color: white;
}

/* Main Section */
.profile-main-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

/* User Info Card */
.user-info-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    text-align: center;
}

.user-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.user-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.verified-badge-blue {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
}

.unverified-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
}

.user-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.user-badges {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.badge-broker {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.badge-verified {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.badge-unverified {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
}

.badge-level {
    background: linear-gradient(135deg, #17a2b8, #6610f2);
    color: white;
}

.user-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-content {
    flex: 1;
    text-align: left;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    display: block;
}

.stat-label {
    font-size: 0.85rem;
    color: #718096;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars .fa-star {
    font-size: 0.8rem;
    color: #e2e8f0;
}

.stars .fa-star.filled {
    color: #fbbf24;
}

/* Premium Rank Cards */
.premium-rank-card {
    margin: 1.5rem 0;
    border-radius: 16px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 2px solid transparent;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.premium-rank-card.vip-member {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #f59e0b 100%);
    border-color: #d97706;
    color: #92400e;
}

.premium-rank-card.trader {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #1d4ed8 100%);
    border-color: #1e40af;
    color: white;
}

.premium-rank-card.company {
    background: linear-gradient(135deg, #10b981 0%, #34d399 50%, #**********%);
    border-color: #047857;
    color: white;
}

.premium-rank-card.premium {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 50%, #7c3aed 100%);
    border-color: #6d28d9;
    color: white;
}

.premium-rank-card.regular {
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 50%, #4b5563 100%);
    border-color: #374151;
    color: white;
}

.rank-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.rank-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.rank-title {
    font-size: 1rem;
    font-weight: 700;
    letter-spacing: 1px;
    flex: 1;
}

.rank-sparkles {
    display: flex;
    gap: 0.25rem;
    opacity: 0.8;
}

.rank-sparkles i {
    font-size: 0.8rem;
    animation: sparkle 2s ease-in-out infinite;
}

.rank-sparkles i:nth-child(2) {
    animation-delay: 0.5s;
}

.rank-sparkles i:nth-child(3) {
    animation-delay: 1s;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.rank-description {
    text-align: left;
}

.rank-subtitle {
    font-size: 0.85rem;
    opacity: 0.9;
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.rank-features {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.rank-features span {
    font-size: 0.75rem;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rank-features i {
    font-size: 0.7rem;
    width: 12px;
}

/* VIP Member specific styles */
.premium-rank-card.vip-member .rank-header {
    position: relative;
}

.premium-rank-card.vip-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706, #f59e0b, #fbbf24);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Profile Form Card */
.profile-form-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid #e9ecef;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-subtitle {
    color: #718096;
    margin: 0;
    font-size: 0.95rem;
}

.card-body {
    padding: 2rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 2.5rem;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-subtitle {
    color: #718096;
    margin: 0;
    font-size: 0.9rem;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.95rem;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 2;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.875rem 1rem 0.875rem 3rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    z-index: 2;
}

.password-toggle:hover {
    color: #667eea;
}

.error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message::before {
    content: '⚠';
    font-size: 0.8rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-tabs {
        flex-direction: column;
    }

    .dashboard-tabs .nav-link {
        justify-content: center;
    }

    .user-info-card {
        padding: 1.5rem;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .profile-main-section {
        padding: 1rem 0;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .user-badges {
        gap: 0.25rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
    }

    .nav-tabs-wrapper {
        padding: 0.25rem;
    }

    .dashboard-tabs .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Profile Hero Section - نفس تصميم Dashboard */
.profile-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 3rem 0 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.profile-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-breadcrumb .breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.hero-breadcrumb .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.hero-breadcrumb .breadcrumb-item.active {
    color: white;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Main Profile Section */
.profile-main-section {
    padding: 3rem 0;
    background: #f8f9fa;
}

/* Profile Header Card - نفس تصميم Dashboard Cards */
.profile-header-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
}

.profile-header-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.profile-cover {
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.cover-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.profile-info {
    padding: 0 2rem 2rem;
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: 2rem;
    margin-top: -40px;
}

.profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.avatar-status {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    border: 2px solid white;
}

.avatar-status.verified {
    background: #28a745;
}

.avatar-status.unverified {
    background: #ffc107;
}

.profile-details {
    flex: 1;
    padding-top: 1rem;
}

.profile-name {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.profile-email {
    color: #6c757d;
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
}

.profile-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.badge-verified {
    background: rgba(40, 167, 69, 0.9);
    color: white;
}

.badge-unverified {
    background: rgba(255, 193, 7, 0.9);
    color: white;
}

.badge-member {
    background: rgba(102, 126, 234, 0.9);
    color: white;
}

.profile-stats {
    display: flex;
    gap: 1.5rem;
    padding-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Profile Form Card - نفس تصميم Dashboard Cards */
.profile-form-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.profile-form-card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    padding: 2rem 2rem 0;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
}

.card-title {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-subtitle {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

.card-body {
    padding: 0 2rem 2rem;
}

/* Form Sections - نفس تصميم Dashboard */
.form-section {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.form-section:last-child {
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-title {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #667eea;
}

.section-subtitle {
    color: #6c757d;
    margin: 0;
    font-size: 0.8rem;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.875rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper.disabled {
    opacity: 0.7;
}

.input-icon {
    position: absolute;
    left: 1rem;
    color: #6c757d;
    z-index: 2;
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.form-control:disabled {
    background: #f8f9fa;
    cursor: not-allowed;
    border-color: #e9ecef;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    z-index: 2;
}

.password-toggle:hover {
    color: #667eea;
}

.input-note {
    position: absolute;
    right: 1rem;
    font-size: 0.75rem;
    color: #6c757d;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
}

.error-message {
    color: #dc3545;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message::before {
    content: '⚠';
    font-size: 0.875rem;
}

/* Form Actions - نفس تصميم Dashboard Buttons */
.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
    justify-content: flex-end;
}

.btn {
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

.btn-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .profile-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
        margin-top: -60px;
        padding-top: 1rem;
    }

    .profile-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .profile-hero-section {
        padding: 1.5rem 0 0.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .profile-main-section {
        padding: 2rem 0;
    }

    .card-header,
    .card-body {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }

    .profile-badges {
        justify-content: center;
    }

    .profile-stats {
        gap: 1rem;
    }
}

@media (max-width: 576px) {
    .avatar-img {
        width: 100px;
        height: 100px;
    }

    .avatar-status {
        width: 25px;
        height: 25px;
        font-size: 0.75rem;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading States */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 1rem 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    max-width: 400px;
    border-left: 4px solid;
}

.notification-success {
    border-left-color: #28a745;
    color: #155724;
}

.notification-error {
    border-left-color: #dc3545;
    color: #721c24;
}

.notification i {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.notification-success i {
    color: #28a745;
}

.notification-error i {
    color: #dc3545;
}

.notification-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    background: #f8f9fa;
    color: #495057;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Avatar Upload Styles */
.avatar-upload-wrapper {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.avatar-upload-wrapper:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.current-avatar {
    position: relative;
    flex-shrink: 0;
}

.current-avatar img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.verified-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    border: 2px solid white;
}

.upload-controls {
    flex: 1;
}

.upload-controls .btn {
    margin-bottom: 0.5rem;
}

.upload-controls small {
    display: block;
    margin-top: 0.5rem;
}

/* Name Change Restrictions */
.input-wrapper.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.form-text.text-warning {
    color: #856404 !important;
    background: #fff3cd;
    padding: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid #ffc107;
    margin-top: 0.5rem;
}

.form-text.text-info {
    color: #0c5460 !important;
    background: #d1ecf1;
    padding: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid #17a2b8;
    margin-top: 0.5rem;
}

/* Enhanced Form Validation Styles */
.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem);
}

/* تحسينات بسيطة */
.form-section:hover .section-title {
    color: #667eea;
    transition: color 0.3s ease;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar preview functionality
    const avatarInput = document.getElementById('avatar-input');
    const avatarPreview = document.getElementById('avatar-preview');

    if (avatarInput && avatarPreview) {
        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('يرجى اختيار صورة بصيغة JPG, PNG, GIF أو WebP');
                    this.value = '';
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
                    this.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Form submission with loading state
    const form = document.querySelector('.profile-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Debug: Check if avatar file is included
            const avatarInput = document.getElementById('avatar-input');
            if (avatarInput && avatarInput.files.length > 0) {
                console.log('Avatar file detected:', avatarInput.files[0]);
                console.log('File name:', avatarInput.files[0].name);
                console.log('File size:', avatarInput.files[0].size);
            } else {
                console.log('No avatar file selected');
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            submitBtn.disabled = true;

            // Re-enable after 3 seconds in case of slow response
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    }

    // Reset form functionality
    const resetBtn = document.querySelector('button[type="reset"]');
    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('Are you sure you want to reset all changes?')) {
                form.reset();
            }
        });
    }
});

// Password toggle functionality
function togglePassword(button) {
    const input = button.parentNode.querySelector('input');
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// Handle form success/error messages from server
@if(session('success'))
    document.addEventListener('DOMContentLoaded', function() {
        alert('{{ session('success') }}');
    });
@endif

@if(session('error'))
    document.addEventListener('DOMContentLoaded', function() {
        alert('{{ session('error') }}');
    });
@endif
</script>
@endpush

@endsection

@push('styles')
<style>
.badge-points {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.broker-points-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.points-header h4 {
    color: white;
    margin: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.points-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.points-stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.points-stat-item .stat-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
}

.points-stat-item .stat-icon i {
    font-size: 1.2rem;
    color: white;
}

.points-stat-item .stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
}

.points-stat-item .stat-label {
    display: block;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

@media (max-width: 768px) {
    .points-stats {
        grid-template-columns: 1fr;
    }

    .broker-points-section {
        padding: 1rem;
    }
}
</style>
@endpush