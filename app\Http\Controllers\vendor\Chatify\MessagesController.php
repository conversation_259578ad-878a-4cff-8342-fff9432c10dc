<?php

namespace Chatify\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Response;
use App\Models\User;
use App\Models\ChMessage as Message;
use App\Models\ChFavorite as Favorite;
use Chatify\Facades\ChatifyMessenger as Chatify;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Illuminate\Support\Str;
class MessagesController extends Controller
{
    protected $perPage = 30;

    /**
     * Authenticate the connection for pusher
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function pusherAuth(Request $request)
    {
        return Chatify::pusherAuth(
            $request->user(),
            Auth::user(),
            $request['channel_name'],
            $request['socket_id']
        );
    }

    /**
     * Returning the view of the app with the required data.
     *
     * @param int $id
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function index( $id = null)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('user.login')->with('error', 'يجب تسجيل الدخول أولاً.');
        }

        $messenger_color = $user->messenger_color;

        // تحقق من أن المستخدم لا يفتح محادثة مع نفسه
        if ($id == $user->id) {
            // Redirect للصفحة الرئيسية بدلاً من تغيير الـ ID فقط
            return redirect()->route(config('chatify.routes.prefix'));
        }

        return view('Chatify::pages.app', [
            'id' => $id ?? 0,
            'messengerColor' => $messenger_color ? $messenger_color : Chatify::getFallbackColor(),
            'dark_mode' => ($user->dark_mode ?? 0) < 1 ? 'light' : 'dark',
            'admin' => Auth::guard('admin')->check(), // إضافة متغير admin للهيدر
        ]);
    }


    /**
     * Fetch data (user, favorite.. etc).
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function idFetchData(Request $request)
    {
        // تحقق من أن المستخدم لا يجلب معلومات لنفسه
        if (Auth::user()->id == $request['id']) {
            return Response::json([
                'favorite' => 0,
                'fetch' => null,
                'user_avatar' => null,
            ]);
        }
        
        $favorite = Chatify::inFavorite($request['id']);
        $fetch = User::where('id', $request['id'])->first();
        if($fetch){
            $userAvatar = Chatify::getUserWithAvatar($fetch)->avatar;
        }
        return Response::json([
            'favorite' => $favorite,
            'fetch' => $fetch ?? null,
            'user_avatar' => $userAvatar ?? null,
            'is_trusted' => $fetch ? $fetch->is_trusted : false,
            'rank' => $fetch ? $fetch->rank : 0,
        ]);
    }

    /**
     * This method to make a links for the attachments
     * to be downloadable.
     *
     * @param string $fileName
     * @return \Symfony\Component\HttpFoundation\StreamedResponse|void
     */
    public function download($fileName)
    {
        $filePath = config('chatify.attachments.folder') . '/' . $fileName;
        if (Chatify::storage()->exists($filePath)) {
            return Chatify::storage()->download($filePath);
        }
        return abort(404, "Sorry, File does not exist in our server or may have been deleted!");
    }

    /**
     * Send a message to database
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function send(Request $request)
    {
        // تحقق من أن المستخدم لا يرسل رسالة لنفسه
        if (Auth::user()->id == $request['id']) {
            return Response::json([
                'status' => '400',
                'error' => (object)[
                    'status' => 1,
                    'message' => 'لا يمكنك إرسال رسالة لنفسك'
                ],
                'message' => null,
                'tempID' => $request['temporaryMsgId'],
            ]);
        }
        
        // default variables
        $error = (object)[
            'status' => 0,
            'message' => null
        ];
        $attachment = null;
        $attachment_title = null;

        // if there is attachment [file]
        if ($request->hasFile('file')) {
            // allowed extensions
            $allowed_images = Chatify::getAllowedImages();
            $allowed_files  = Chatify::getAllowedFiles();
            $allowed        = array_merge($allowed_images, $allowed_files);

            $file = $request->file('file');
            // check file size
            if ($file->getSize() < Chatify::getMaxUploadSize()) {
                if (in_array(strtolower($file->extension()), $allowed)) {
                    // get attachment name
                    $attachment_title = $file->getClientOriginalName();
                    // upload attachment and store the new name
                    $attachment = Str::uuid() . "." . $file->extension();
                    $file->storeAs(config('chatify.attachments.folder'), $attachment, config('chatify.storage_disk_name'));
                } else {
                    $error->status = 1;
                    $error->message = "File extension not allowed!";
                }
            } else {
                $error->status = 1;
                $error->message = "File size you are trying to upload is too large!";
            }
        }

        if (!$error->status) {
            $message = Chatify::newMessage([
                'from_id' => Auth::user()->id,
                'to_id' => $request['id'],
                'body' => htmlentities(trim($request['message']), ENT_QUOTES, 'UTF-8'),
                'attachment' => ($attachment) ? json_encode((object)[
                    'new_name' => $attachment,
                    'old_name' => htmlentities(trim($attachment_title), ENT_QUOTES, 'UTF-8'),
                ]) : null,
            ]);
            $messageData = Chatify::parseMessage($message);

            // إضافة starred تلقائي للطرفين
            $this->addAutoStarred(Auth::user()->id, $request['id']);
            $this->addAutoStarred($request['id'], Auth::user()->id);

            if (Auth::user()->id != $request['id']) {
                Chatify::push("private-chatify.".$request['id'], 'messaging', [
                    'from_id' => Auth::user()->id,
                    'to_id' => $request['id'],
                    'message' => Chatify::messageCard($messageData, true)
                ]);
            }
        }

        // send the response
        $response = [
            'status' => '200',
            'error' => $error,
            'tempID' => $request['temporaryMsgId'],
        ];
        
        // إضافة الرسالة للاستجابة فقط إذا لم يكن هناك خطأ وكانت الرسالة موجودة
        if (!$error->status && isset($messageData)) {
            $response['message'] = Chatify::messageCard($messageData);
        } else {
            $response['message'] = null;
        }
        
        return Response::json($response);
    }

    /**
     * fetch [user/group] messages from database
     *
     * @param Request $request
     * @return JsonResponse
     */
public function fetch(Request $request)
{
    $userId = Auth::user()->id;
    $otherUserId = $request['id'];
    
    // تحقق من أن المستخدم لا يجلب رسائل لنفسه
    if ($userId == $otherUserId) {
        return Response::json([
            'total' => 0,
            'last_page' => 1,
            'last_message_id' => null,
            'messages' => '<p class="message-hint center-el"><span>لا يمكنك التحدث مع نفسك</span></p>',
        ]);
    }

    // إذا كان هذا فحص للرسائل الجديدة
    if ($request->has('check_new') && $request->check_new) {
        $lastMessageId = $request->last_message_id;
        
        $query = Message::where(function ($query) use ($userId, $otherUserId) {
            $query->where('from_id', $userId)->where('to_id', $otherUserId);
        })->orWhere(function ($query) use ($userId, $otherUserId) {
            $query->where('from_id', $otherUserId)->where('to_id', $userId);
        });
        
        // إذا كان هناك last_message_id، احصل على الرسائل الأحدث منه فقط
        if ($lastMessageId) {
            $query->where('id', '>', $lastMessageId);
        }
        
        $newMessages = $query->orderBy('created_at', 'ASC')->get();
        
        $allMessages = '';
        foreach ($newMessages as $message) {
            $allMessages .= Chatify::messageCard(
                Chatify::parseMessage($message)
            );
        }
        
        return Response::json([
            'messages' => $allMessages,
            'new_count' => $newMessages->count()
        ]);
    }

    // الكود الأصلي للجلب العادي
    $messages = Message::where(function ($query) use ($userId, $otherUserId) {
            $query->where('from_id', $userId)->where('to_id', $otherUserId);
        })->orWhere(function ($query) use ($userId, $otherUserId) {
            $query->where('from_id', $otherUserId)->where('to_id', $userId);
        })
        ->latest()
        ->paginate($request->per_page ?? $this->perPage);

    $totalMessages = $messages->total();
    $lastPage = $messages->lastPage();
    $response = [
        'total' => $totalMessages,
        'last_page' => $lastPage,
        'last_message_id' => collect($messages->items())->last()->id ?? null,
        'messages' => '',
    ];

    // لو مفيش رسايل
    if ($totalMessages < 1) {
        $response['messages'] = '<p class="message-hint center-el"><span>Say \'hi\' and start messaging</span></p>';
        return Response::json($response);
    }

    if (count($messages->items()) < 1) {
        $response['messages'] = '';
        return Response::json($response);
    }

    $allMessages = '';
    foreach ($messages->reverse() as $message) {
        $allMessages .= Chatify::messageCard(
            Chatify::parseMessage($message)
        );
    }

    $response['messages'] = $allMessages;
    return Response::json($response);
}


    /**
     * Make messages as seen
     *
     * @param Request $request
     * @return JsonResponse|void
     */
    public function seen(Request $request)
    {
        // make as seen
        $seen = Chatify::makeSeen($request['id']);
        // send the response
        return Response::json([
            'status' => $seen,
        ], 200);
    }

    /**
     * Get contacts list
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getContacts(Request $request)
    {
        // get all users that received/sent message from/to [Auth user]
        $users = Message::join('users',  function ($join) {
            $join->on('ch_messages.from_id', '=', 'users.id')
                ->orOn('ch_messages.to_id', '=', 'users.id');
        })
        ->where(function ($q) {
            $q->where('ch_messages.from_id', Auth::user()->id)
            ->orWhere('ch_messages.to_id', Auth::user()->id);
        })
        ->where('users.id','!=',Auth::user()->id) // تأكد من عدم ظهور المستخدم الحالي
        ->select('users.*',DB::raw('MAX(ch_messages.created_at) max_created_at'))
        ->orderBy('max_created_at', 'desc')
        ->groupBy('users.id')
        ->paginate($request->per_page ?? $this->perPage);

        $usersList = $users->items();

        if (count($usersList) > 0) {
            $contacts = '';
            foreach ($usersList as $user) {
                // تحقق إضافي من أن المستخدم لا يظهر في قائمة الاتصالات
                if ($user->id == Auth::user()->id) {
                    continue;
                }
                $contacts .= Chatify::getContactItem($user);
            }
        } else {
            $contacts = '<p class="message-hint center-el"><span>Your contact list is empty</span></p>';
        }

        return Response::json([
            'contacts' => $contacts,
            'total' => $users->total() ?? 0,
            'last_page' => $users->lastPage() ?? 1,
        ], 200);
    }

    /**
     * Update user's list item data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateContactItem(Request $request)
    {
        // Get user data
        $user = User::where('id', $request['user_id'])->first();
        if(!$user){
            return Response::json([
                'message' => 'User not found!',
            ], 401);
        }
        $contactItem = Chatify::getContactItem($user);

        // send the response
        return Response::json([
            'contactItem' => $contactItem,
        ], 200);
    }

    /**
     * Put a user in the favorites list
     *
     * @param Request $request
     * @return JsonResponse|void
     */
    public function favorite(Request $request)
    {
        $userId = $request['user_id'];
        // check action [star/unstar]
        $favoriteStatus = Chatify::inFavorite($userId) ? 0 : 1;
        Chatify::makeInFavorite($userId, $favoriteStatus);

        // send the response
        return Response::json([
            'status' => @$favoriteStatus,
        ], 200);
    }

    /**
     * Get favorites list (shows auto-starred users - last 10)
     *
     * @param Request $request
     * @return JsonResponse|void
     */
    public function getFavorites(Request $request)
    {
        $favoritesList = null;

        // جلب آخر 10 أشخاص في المفضلة (starred تلقائياً)
        $favorites = Favorite::where('user_id', Auth::user()->id)
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get();

        // بناء قائمة HTML
        foreach ($favorites as $favorite) {
            // تحقق من أن المستخدم لا يظهر في قائمة المفضلة
            if ($favorite->favorite_id == Auth::user()->id) {
                continue; // تخطي المستخدم الحالي
            }

            $user = User::where('id', $favorite->favorite_id)->first();
            if ($user) {
                $favoritesList .= view('Chatify::layouts.favorite', [
                    'user' => $user,
                ]);
            }
        }

        // send the response
        return Response::json([
            'count' => $favorites->count(),
            'favorites' => $favorites->count() > 0
                ? $favoritesList
                : 0,
        ], 200);
    }



    /**
     * Get shared photos
     *
     * @param Request $request
     * @return JsonResponse|void
     */
    public function sharedPhotos(Request $request)
    {
        $shared = Chatify::getSharedPhotos($request['user_id']);
        $sharedPhotos = null;

        // shared with its template
        for ($i = 0; $i < count($shared); $i++) {
            $sharedPhotos .= view('Chatify::layouts.listItem', [
                'get' => 'sharedPhoto',
                'image' => Chatify::getAttachmentUrl($shared[$i]),
            ])->render();
        }
        // send the response
        return Response::json([
            'shared' => count($shared) > 0 ? $sharedPhotos : '<p class="message-hint"><span>Nothing shared yet</span></p>',
        ], 200);
    }

    /**
     * Delete conversation
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteConversation(Request $request)
    {
        // delete
        $delete = Chatify::deleteConversation($request['id']);

        // send the response
        return Response::json([
            'deleted' => $delete ? 1 : 0,
        ], 200);
    }

    // تم حذف دالة حذف الرسالة نهائياً

    public function updateSettings(Request $request)
    {
        $msg = null;
        $error = $success = 0;

        // dark mode
        if ($request['dark_mode']) {
            $request['dark_mode'] == "dark"
                ? User::where('id', Auth::user()->id)->update(['dark_mode' => 1])  // Make Dark
                : User::where('id', Auth::user()->id)->update(['dark_mode' => 0]); // Make Light
        }

        // If messenger color selected
        if ($request['messengerColor']) {
            $messenger_color = trim(filter_var($request['messengerColor']));
            User::where('id', Auth::user()->id)
                ->update(['messenger_color' => $messenger_color]);
        }
        // if there is a [file]
        if ($request->hasFile('avatar')) {
            // allowed extensions
            $allowed_images = Chatify::getAllowedImages();

            $file = $request->file('avatar');
            // check file size
            if ($file->getSize() < Chatify::getMaxUploadSize()) {
                if (in_array(strtolower($file->extension()), $allowed_images)) {
                    // delete the older one
                    if (Auth::user()->avatar != config('chatify.user_avatar.default')) {
                        $avatar = Auth::user()->avatar;
                        if (Chatify::storage()->exists($avatar)) {
                            Chatify::storage()->delete($avatar);
                        }
                    }
                    // upload
                    $avatar = Str::uuid() . "." . $file->extension();
                    $update = User::where('id', Auth::user()->id)->update(['avatar' => $avatar]);
                    $file->storeAs(config('chatify.user_avatar.folder'), $avatar, config('chatify.storage_disk_name'));
                    $success = $update ? 1 : 0;
                } else {
                    $msg = "File extension not allowed!";
                    $error = 1;
                }
            } else {
                $msg = "File size you are trying to upload is too large!";
                $error = 1;
            }
        }

        // send the response
        return Response::json([
            'status' => $success ? 1 : 0,
            'error' => $error ? 1 : 0,
            'message' => $error ? $msg : 0,
        ], 200);
    }

    /**
     * Set user's active status
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setActiveStatus(Request $request)
    {
        $activeStatus = $request['status'] > 0 ? 1 : 0;
        $status = User::where('id', Auth::user()->id)->update(['active_status' => $activeStatus]);
        return Response::json([
            'status' => $status,
        ], 200);
    }

    /**
     * إضافة starred تلقائي - يحتفظ بآخر 10 أشخاص فقط
     */
    private function addAutoStarred($userId, $favoriteId)
    {
        // تحقق من أن المستخدم لا يضيف نفسه
        if ($userId == $favoriteId) {
            return;
        }

        // تحقق إذا كان موجود بالفعل
        $existingFavorite = Favorite::where('user_id', $userId)
            ->where('favorite_id', $favoriteId)
            ->first();

        if (!$existingFavorite) {
            // إضافة المفضلة الجديدة
            Favorite::create([
                'user_id' => $userId,
                'favorite_id' => $favoriteId
            ]);
        } else {
            // تحديث التاريخ للمفضلة الموجودة
            $existingFavorite->touch();
        }

        // الاحتفاظ بآخر 10 فقط - حذف الأقدم
        $favoritesCount = Favorite::where('user_id', $userId)->count();
        if ($favoritesCount > 10) {
            $oldestFavorites = Favorite::where('user_id', $userId)
                ->orderBy('updated_at', 'asc')
                ->limit($favoritesCount - 10)
                ->get();

            foreach ($oldestFavorites as $oldFavorite) {
                $oldFavorite->delete();
            }
        }
    }
}
