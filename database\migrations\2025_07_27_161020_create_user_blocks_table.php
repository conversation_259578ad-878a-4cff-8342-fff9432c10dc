<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_blocks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('blocker_id'); // المستخدم الذي يحظر
            $table->uuid('blocked_id'); // المستخدم المحظور
            $table->timestamps();

            $table->foreign('blocker_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('blocked_id')->references('id')->on('users')->onDelete('cascade');

            // منع الحظر المكرر
            $table->unique(['blocker_id', 'blocked_id']);

            $table->index(['blocker_id']);
            $table->index(['blocked_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_blocks');
    }
};
