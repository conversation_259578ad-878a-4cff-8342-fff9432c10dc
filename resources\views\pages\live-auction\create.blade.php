<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @include('layouts.seo')
    <link rel="icon" href="assets/images/bg/sm-logo.png" type="image/gif" sizes="20x20">
    @include('layouts.style', ['admin' => false])
    <link rel="stylesheet" href="{{ asset('assets/css/add-listing.css') }}">
</head>
    @php
        use Illuminate\Support\Facades\Storage;
    @endphp

    @include('layouts.notify')
    @include('layouts.header', ['admin' => false])
    @include('layouts.search')

<div class="container mt-5">
      <div class="row justify-content-center">
          <div class="col-lg-10">
              <div class="card">
              </div>
          </div>
      </div>
  </div>
<div class="listing-hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    @if(isset($isEditing) && $isEditing)
                        <h1 class="hero-title">Edit Your Listing</h1>
                        <p class="hero-subtitle">Update your listing details and information</p>
                    @else
                        <h1 class="hero-title">Post Your Listing</h1>
                        <p class="hero-subtitle">Reach thousands of potential buyers and sell your items quickly</p>
                    @endif
                    <div class="hero-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>50K+ Active Buyers</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Secure Transactions</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>Quick Approval</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <div class="floating-card">
                        <i class="fas fa-plus-circle"></i>
                        <h4>Easy Listing</h4>
                        <p>Create your listing in minutes</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Form Section -->
<div class="listing-form-section">
    <div class="container">
        @guest('web')
        <div class="auth-required-card">
            <div class="auth-icon">
                <i class="fas fa-user-lock"></i>
            </div>
            <h3>Sign In Required</h3>
            <p>Please sign in to post your listing and connect with potential buyers</p>
            <div class="auth-actions">
                <a href="{{ route('user.login') }}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                </a>
                <a href="{{ route('user.register') }}" class="btn btn-outline-primary">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                </a>
            </div>
        </div>
        @else

        <!-- Success Info -->
        <div class="info-banner">
            <div class="info-content">
                <i class="fas fa-info-circle"></i>
                <div>
                    <h5>Quick Review Process</h5>
                    <p>Your listing will be reviewed within 24 hours and published once approved</p>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle"></i>
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <!-- Main Form -->
        <div class="row">
            <div class="col-lg-8">
                @if(isset($isEditing) && $isEditing)
                    <!-- Edit Warning Alert -->
                    <div class="alert alert-warning mb-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-3 fs-4"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Editing Your Listing</h5>
                                <p class="mb-0">When you update this listing, it will be sent for review again and temporarily removed from public view until approved.</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Identity Verification Notice -->
                @if(!auth()->user()->is_trusted)
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-3 fs-4"></i>
                        <div class="flex-grow-1">
                            <h5 class="alert-heading mb-1">خدمات البرايم والمندوبين</h5>
                            <p class="mb-2">لو عايز تستفيد من خدمات البرايم وتخلي مندوب يسوق لمنتجاتك ويجيبلك عملاء، لازم تكون موثق الهوية عشان نضمن الأمان للجميع.</p>
                            <a href="{{ route('identity-verification.create') }}" class="btn btn-sm btn-success">
                                <i class="fas fa-id-card me-2"></i>
                                وثق هويتك الآن
                            </a>
                        </div>
                    </div>
                </div>
                @endif

                <form class="listing-form" action="{{ isset($isEditing) && $isEditing ? route('user.ads.edit.handle', $editingAd->slug) : route('add-listing.handle') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    @if(isset($isEditing) && $isEditing)
                        <!-- Hidden field to indicate editing mode -->
                        <input type="hidden" name="editing_ad_id" value="{{ $editingAd->id }}">
                    @endif

                    <!-- Hidden inputs for selected categories -->
                    <input type="hidden" name="category" id="selected-category" value="{{ old('category', isset($editingAd) ? ($editingAd->category->parent_id ?? $editingAd->category_id) : '') }}">
                    <input type="hidden" name="subcategory" id="selected-subcategory" value="{{ old('subcategory', isset($editingAd) && $editingAd->category->parent_id ? $editingAd->category_id : '') }}">

                    <!-- Step 1: Basic Information -->
                    <div class="form-step active" data-step="1">
                        <div class="step-header">
                            <div class="step-number">1</div>
                            <div class="step-info">
                                <h3>Basic Information</h3>
                                <p>Tell us about your item</p>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tag"></i>
                                    Listing Title
                                </label>
                                <input type="text" name="title" class="form-control"
                                       placeholder="e.g., iPhone 14 Pro Max - Excellent Condition"
                                       value="{{ old('title', isset($editingAd) ? $editingAd->title : '') }}" required>
                                @error('title')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-pound-sign"></i>
                                    Price (EGP)
                                </label>
                                <div class="price-input">
                                    <span class="currency">EGP</span>
                                    <input type="number" name="price" class="form-control"
                                           placeholder="1000" value="{{ old('price', isset($editingAd) ? $editingAd->price : '') }}" required>
                                </div>
                                <small class="form-text text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Enter the price in Egyptian Pounds (EGP)
                                </small>
                                @error('price')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group full-width">
                                <label class="form-label">
                                    <i class="fas fa-align-left"></i>
                                    Description
                                </label>
                                <textarea name="description" class="form-control" rows="5"
                                          placeholder="Describe your item in detail. Include condition, features, and any important information buyers should know..."
                                          required>{{ old('description', isset($editingAd) ? $editingAd->description : '') }}</textarea>
                                @error('description')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn btn-primary next-step">
                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Category & Images -->
                    <div class="form-step" data-step="2">


                        <div class="form-grid">
                            <div class="form-group full-width">
                                <!-- Enhanced Category Section -->
                                <div class="category-section">
                                    <div class="category-header">
                                        <div class="category-icon">
                                            <i class="fas fa-sitemap"></i>
                                        </div>
                                        <div class="category-title">
                                            <h3>اختيار التصنيف</h3>
                                            <p>اختر التصنيف المناسب لمنتجك للوصول لأكبر عدد من المشترين</p>
                                        </div>
                                    </div>

                                    <div class="category-grid">
                                        <div class="category-dropdown">
                                            <label class="form-label">
                                                <i class="fas fa-folder"></i>
                                                التصنيف الرئيسي
                                            </label>
                                            <select id="main-category" class="form-control">
                                                <option value="">اختر التصنيف الرئيسي</option>
                                            </select>
                                        </div>

                                        <div id="sub-category-container" class="category-dropdown" style="display: none;">
                                            <label class="form-label">
                                                <i class="fas fa-folder-open"></i>
                                                التصنيف الفرعي
                                            </label>
                                            <select id="sub-category" class="form-control">
                                                <option value="">اختر التصنيف الفرعي</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div id="parallel-attributes-container" style="display: none;">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5><i class="fas fa-cogs me-2"></i>الخصائص المتقدمة</h5>
                                                <small>اختر الخصائص التي تناسب منتجك لتحسين ظهوره في نتائج البحث</small>
                                            </div>
                                            <div class="card-body">
                                                <div id="attributes-grid" class="row"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- END: Enhanced Category Section -->
                            </div>

                            <div class="form-group full-width">
                                <label class="form-label">
                                    <i class="fas fa-images"></i>
                                    Photos (Up to 7 images) <span class="text-danger">*</span>
                                    <small class="text-muted d-block">At least one image is required</small>
                                </label>
                                <div class="image-upload-area">
                                    <div class="upload-zone" id="upload-zone">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <h4>Drag & Drop Images Here</h4>
                                        <p>or click to browse</p>
                                        <input type="file" name="images[]" multiple accept="image/*" id="image-input" style="display: none;">
                                    </div>
                                    <div class="image-preview" id="image-preview"></div>
                                </div>
                                @error('images')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>



                        <div class="step-actions">
                            <button type="button" class="btn btn-outline-secondary prev-step">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </button>
                            <button type="button" class="btn btn-primary next-step">
                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                    <!-- Step 3: Premium Features (for trusted users) -->
                    @php
                        $user = auth()->user();
                        $isVerified = $user && ($user->is_trusted ?? 0) > 0;
                        $userRank = $user ? ($user->rank ?? 0) : 0; // الرتبة (VIP, Trader, etc.)
                        // السماح للمستخدمين الموثقين بالوصول للـ Premium Features
                        $isPremiumUser = $isVerified; // أي مستخدم موثق يمكنه الوصول للميزات المتقدمة

                        $rankNames = [
                            1 => 'VIP Member',
                            2 => 'Professional Trader',
                            3 => 'Company',
                            4 => 'Premium Member'
                        ];
                    @endphp

                    <div class="form-step" data-step="3">
                        <div class="step-header">
                            <div class="step-number">3</div>
                            <div class="step-info">
                                <h3>
                                    <i class="fas fa-crown"></i>
                                    Premium Features
                                </h3>
                                <p>Professional marketing and promotion services</p>
                            </div>
                        </div>

                        @if($isPremiumUser)

                        <div class="premium-features">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="feature-content">
                                    <h4>Marketing Support</h4>
                                    <p>Get professional marketing help for your listing</p>
                                    <div class="feature-toggle">
                                        <label class="switch">
                                            <input type="checkbox" name="needs_brokering" value="1">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="feature-card" id="broker-commission-card" style="display: none;">
                                <div class="form-group">
                                    <label class="form-label">Marketing Commission (EGP)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-pound-sign"></i>
                                        </span>
                                        <input type="number" name="broker_commission" class="form-control"
                                               placeholder="500" min="1" step="1" id="broker-commission-input">
                                        <span class="input-group-text">EGP</span>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Amount the broker will earn for successfully marketing your listing (Min 1 EGP, Max 25% of item price)
                                    </small>
                                    <div id="commission-suggestion" class="commission-suggestion" style="display: none;">
                                        <i class="fas fa-lightbulb"></i>
                                        <span class="suggestion-text">Our recommended commission: <strong id="suggested-amount">0</strong> EGP</span>
                                    </div>
                                    <div id="commission-error" class="commission-error" style="display: none;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span class="error-text">Commission cannot exceed 25% of the item price</span>
                                    </div>
                                </div>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="feature-content">
                                    <h4>Sponsored Listing</h4>
                                    <p>Promote your listing to reach more buyers</p>
                                    <div class="feature-toggle">
                                        <label class="switch">
                                            <input type="checkbox" name="is_sponsored" value="1">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="feature-card" id="sponsorship-cost-card" style="display: none;">
                                <div class="form-group">
                                    <label class="form-label">ميزانية الرعاية (جنيه مصري)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-pound-sign"></i>
                                        </span>
                                        <input type="number" name="sponsorship_cost" class="form-control"
                                               placeholder="500" min="10" step="1" onchange="calculateSponsoredMinutes()">
                                        <span class="input-group-text">جنيه</span>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        كل 100 جنيه = 60 دقيقة رعاية (الحد الأدنى 10 جنيه)
                                        <span id="sponsored-minutes-display" style="color: #007bff; font-weight: bold; display: block; margin-top: 5px;"></span>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn btn-outline-secondary prev-step">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </button>
                            <button type="button" class="btn btn-primary next-step">
                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>

                        @else
                        <!-- Premium Features Preview for Regular Users -->
                        <div class="premium-features-preview">
                            <div class="preview-header">
                                <div class="preview-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h4>Unlock Premium Marketing Features</h4>
                                <p>Take your business to the next level with professional marketing support</p>
                            </div>

                            <div class="preview-features">
                                <div class="feature-preview-item">
                                    <div class="feature-preview-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="feature-preview-content">
                                        <h5>دعم التسويق المهني</h5>
                                        <p>احصل على مندوبين محترفين لتسويق إعلاناتك والوصول لعدد أكبر من المشترين</p>
                                        <div class="feature-preview-badge">للموثقين فقط</div>
                                    </div>
                                </div>

                                <div class="feature-preview-item">
                                    <div class="feature-preview-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="feature-preview-content">
                                        <h5>الإعلانات الممولة</h5>
                                        <p>روج لمنتجاتك لتظهر في أعلى نتائج البحث وتحصل على مشاهدات أكثر</p>
                                        <div class="feature-preview-badge">للموثقين فقط</div>
                                    </div>
                                </div>

                                <div class="feature-preview-item">
                                    <div class="feature-preview-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="feature-preview-content">
                                        <h5>تحليلات متقدمة</h5>
                                        <p>تتبع أداء إعلاناتك وحسن النتائج للحصول على مبيعات أفضل</p>
                                        <div class="feature-preview-badge">للموثقين فقط</div>
                                    </div>
                                </div>
                            </div>

                            <div class="preview-requirements">
                                <h5><i class="fas fa-shield-check"></i> متطلبات الوصول للميزات المتقدمة:</h5>
                                <div class="requirements-preview-list">
                                    <div class="requirement-preview-item {{ $isVerified ? 'completed' : 'pending' }}">
                                        <i class="fas {{ $isVerified ? 'fa-check-circle' : 'fa-clock' }}"></i>
                                        <span>حساب موثق</span>
                                        @if(!$isVerified)
                                            <small>أكمل عملية توثيق الهوية</small>
                                        @endif
                                    </div>
                                </div>

                                <div class="verification-benefits">
                                    <h6><i class="fas fa-star"></i> فوائد توثيق الحساب:</h6>
                                    <ul class="benefits-list">
                                        <li><i class="fas fa-check"></i> استخدام المندوبين لتسويق منتجاتك</li>
                                        <li><i class="fas fa-check"></i> الإعلانات الممولة للظهور في المقدمة</li>
                                        <li><i class="fas fa-check"></i> تحليلات متقدمة لأداء إعلاناتك</li>
                                        <li><i class="fas fa-check"></i> أولوية في خدمة العملاء</li>
                                        <li><i class="fas fa-check"></i> شارة التوثيق على حسابك</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="preview-actions">
                                <div class="preview-note">
                                    <i class="fas fa-info-circle"></i>
                                    <span>يمكنك إنشاء إعلانك الآن وتوثيق حسابك لاحقاً للوصول لهذه الميزات المتقدمة</span>
                                </div>

                                <div class="preview-buttons">
                                    @if(!$isVerified)
                                        <a href="{{ route('contact') }}?subject=account-verification" class="btn btn-verify-small">
                                            <i class="fas fa-shield-check"></i>
                                            وثق حسابك الآن
                                        </a>
                                    @endif

                                    @if($userRank == 0)
                                        <a href="{{ route('contact') }}?subject=membership-upgrade" class="btn btn-upgrade-small">
                                            <i class="fas fa-crown"></i>
                                            Upgrade Membership
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn btn-outline-secondary prev-step">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </button>
                            <button type="button" class="btn btn-primary next-step">
                                Next Step <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                        @endif
                    </div>

                    <!-- Final Step: Review & Submit -->
                    <div class="form-step" data-step="4">
                        <div class="step-header">
                            <div class="step-number">4</div>
                            <div class="step-info">
                                <h3>Review & Submit</h3>
                                <p>Confirm your listing details</p>
                            </div>
                        </div>

                        <div class="review-section">
                            <div class="seller-info-card">
                                <div class="seller-header">
                                    <div class="seller-avatar">
                                        <img src="{{ auth()->user()->avatar ? Storage::url(auth()->user()->avatar) : get_random_avatar() }}" alt="{{ auth()->user()->name }}">
                                    </div>
                                    <div class="seller-details">
                                        <h4>{{ auth()->user()->name }}</h4>
                                        <div class="seller-badges">
                                            @if(auth()->user()->is_trusted)
                                                <span class="badge badge-trusted">
                                                    <i class="fas fa-shield-check"></i> Verified Seller
                                                </span>
                                            @endif
                                            <span class="badge badge-rank">
                                                @switch(auth()->user()->rank)
                                                    @case(1) VIP Seller @break
                                                    @case(2) Trader @break
                                                    @case(3) Company @break
                                                    @case(4) Premium @break
                                                    @default Regular Seller
                                                @endswitch
                                            </span>
                                        </div>
                                        @php
                                            $level = floor(auth()->user()->Number_Ads / 20) + 1; // المستوى حسب عدد الإعلانات
                                            $level = min($level, 50);
                                        @endphp
                                        <div class="seller-rating">
                                            <div class="rating-stars">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="fas fa-star {{ $i <= round(auth()->user()->average_rating ?? 0) ? 'active' : '' }}"></i>
                                                @endfor
                                            </div>
                                            <span class="rating-level">Level {{ $level }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="terms-agreement">
                                <label class="checkbox-container">
                                    <input type="checkbox" name="terms" required>
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">
                                        I agree to the <a href="#" target="_blank">Terms & Conditions</a>
                                        and <a href="#" target="_blank">Privacy Policy</a>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <div class="step-actions">
                            <button type="button" class="btn btn-outline-secondary prev-step">
                                <i class="fas fa-arrow-left me-2"></i>Previous
                            </button>
                            <button type="submit" class="btn btn-success btn-lg submit-listing"
                                    @if(isset($isEditing) && $isEditing)
                                        onclick="return confirm('Are you sure you want to update this listing? It will be sent for review again and temporarily removed from public view until approved.')"
                                    @endif>
                                @if(isset($isEditing) && $isEditing)
                                    <i class="fas fa-save me-2"></i>Update Listing
                                @else
                                    <i class="fas fa-check me-2"></i>Create Listing
                                @endif
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="listing-sidebar">
                    <!-- Progress Steps -->
                    <div class="progress-card">
                        <h4>Listing Progress</h4>
                        <div class="progress-steps">
                            <div class="progress-step active" data-step="1">
                                <div class="step-circle">1</div>
                                <span>Basic Info</span>
                            </div>
                            <div class="progress-step" data-step="2">
                                <div class="step-circle">2</div>
                                <span>Category & Photos</span>
                            </div>
                            @if (auth()->user()->is_trusted > 0 && auth()->user()->rank > 0)
                            <div class="progress-step" data-step="3">
                                <div class="step-circle">3</div>
                                <span>Premium Features</span>
                            </div>
                            @endif
                            <div class="progress-step" data-step="{{ auth()->user()->is_trusted > 0 && auth()->user()->rank > 0 ? '4' : '3' }}">
                                <div class="step-circle">{{ auth()->user()->is_trusted > 0 && auth()->user()->rank > 0 ? '4' : '3' }}</div>
                                <span>Review & Submit</span>
                            </div>
                        </div>
                    </div>

                    <!-- Tips Card -->
                    <div class="tips-card">
                        <h4><i class="fas fa-lightbulb"></i> Listing Tips</h4>
                        <ul class="tips-list">
                            <li>Use clear, high-quality photos</li>
                            <li>Write detailed descriptions</li>
                            <li>Set competitive prices</li>
                            <li>Respond quickly to inquiries</li>
                            <li>Be honest about item condition</li>
                        </ul>
                    </div>

                    <!-- Support Card -->
                    <div class="support-card">
                        <div class="support-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4>Need Help?</h4>
                        <p>Our support team is here to help you create the perfect listing</p>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-comments me-2"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

@include('layouts.footer')






































































  <script>
    document.addEventListener('DOMContentLoaded', function() {
    // User status for step management
    const isPremiumUser = {{ $isPremiumUser ? 'true' : 'false' }};

    // Multi-step form functionality
    const steps = document.querySelectorAll('.form-step');
    const progressSteps = document.querySelectorAll('.progress-step');
    const nextButtons = document.querySelectorAll('.next-step');
    const prevButtons = document.querySelectorAll('.prev-step');
    let currentStep = 1;

    // Calculate total steps dynamically based on visible steps
    const getMaxStep = () => {
        let maxStep = 0;
        steps.forEach(step => {
            const stepNumber = parseInt(step.getAttribute('data-step'));
            if (stepNumber > maxStep) {
                maxStep = stepNumber;
            }
        });
        return maxStep;
    };

    const totalSteps = getMaxStep();

    function showStep(stepNumber) {
        // Hide all steps
        steps.forEach(step => step.classList.remove('active'));
        progressSteps.forEach(step => step.classList.remove('active'));

        // Show current step
        const currentStepElement = document.querySelector(`[data-step="${stepNumber}"]`);
        if (currentStepElement) {
            currentStepElement.classList.add('active');
        }

        // Update progress
        const currentProgressStep = document.querySelector(`.progress-step[data-step="${stepNumber}"]`);
        if (currentProgressStep) {
            currentProgressStep.classList.add('active');
        }

        // Update completed steps
        progressSteps.forEach(step => {
            const stepNum = parseInt(step.dataset.step);
            if (stepNum < stepNumber) {
                step.classList.add('completed');
            } else {
                step.classList.remove('completed');
            }
        });
    }

    function validateStep(stepNumber) {
        const currentStepElement = document.querySelector(`[data-step="${stepNumber}"]`);
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('error');
            } else {
                field.classList.remove('error');
            }
        });

        return isValid;
    }

    // Next button functionality
    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (validateStep(currentStep)) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    showStep(currentStep);
                }
            } else {
                // Show validation errors
                const errorFields = document.querySelectorAll('.error');
                if (errorFields.length > 0) {
                    errorFields[0].focus();
                }
            }
        });
    });

    // Previous button functionality
    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        });
    });

    // Image upload functionality
    const uploadZone = document.getElementById('upload-zone');
    const imageInput = document.getElementById('image-input');
    const imagePreview = document.getElementById('image-preview');
    let uploadedFiles = [];

    if (uploadZone && imageInput) {
        uploadZone.addEventListener('click', () => imageInput.click());

        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.style.borderColor = '#667eea';
            uploadZone.style.background = 'rgba(102, 126, 234, 0.05)';
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.style.borderColor = '#dee2e6';
            uploadZone.style.background = 'transparent';
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.style.borderColor = '#dee2e6';
            uploadZone.style.background = 'transparent';

            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        imageInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });
    }

    function handleFiles(files) {
        files.forEach(file => {
            if (file.type.startsWith('image/') && uploadedFiles.length < 7) {
                uploadedFiles.push(file);
                displayImage(file);
            }
        });
        updateImageInput();
    }

    function displayImage(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'preview-item';
            previewItem.innerHTML = `
                <img src="${e.target.result}" alt="Preview">
                <button type="button" class="remove-image" onclick="removeImage(${uploadedFiles.length - 1})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            imagePreview.appendChild(previewItem);
        };
        reader.readAsDataURL(file);
    }

    window.removeImage = function(index) {
        uploadedFiles.splice(index, 1);
        updateImagePreview();
        updateImageInput();
    };

    function updateImagePreview() {
        imagePreview.innerHTML = '';
        uploadedFiles.forEach((file, index) => {
            displayImage(file);
        });
    }

    function updateImageInput() {
        const dt = new DataTransfer();
        uploadedFiles.forEach(file => dt.items.add(file));
        imageInput.files = dt.files;
    }

    // Premium features toggle
    const brokeringToggle = document.querySelector('input[name="needs_brokering"]');
    const sponsoredToggle = document.querySelector('input[name="is_sponsored"]');
    const brokerCommissionCard = document.getElementById('broker-commission-card');
    const sponsorshipCostCard = document.getElementById('sponsorship-cost-card');
    const brokerCommissionInput = document.querySelector('input[name="broker_commission"]');
    const sponsorshipCostInput = document.querySelector('input[name="sponsorship_cost"]');
    const priceInput = document.querySelector('input[name="price"]');
    const commissionSuggestion = document.getElementById('commission-suggestion');
    const commissionError = document.getElementById('commission-error');
    const suggestedAmount = document.getElementById('suggested-amount');

    // دالة حساب العمولة المقترحة
    function calculateSuggestedCommission(price) {
        if (!price || price <= 0) return 1; // الحد الأدنى 1 جنيه

        let suggested;
        if (price <= 30000) {
            // للأسعار أقل من 30 ألف: 5%
            suggested = Math.round(price * 0.05);
        } else if (price <= 100000) {
            // من 30 ألف إلى 100 ألف: 4%
            suggested = Math.round(price * 0.04);
        } else if (price <= 500000) {
            // من 100 ألف إلى 500 ألف: 3%
            suggested = Math.round(price * 0.03);
        } else if (price <= 1000000) {
            // من 500 ألف إلى مليون: 2%
            suggested = Math.round(price * 0.02);
        } else {
            // أكثر من مليون: 1.5%
            suggested = Math.round(price * 0.015);
        }

        // التأكد من أن الاقتراح لا يقل عن 1 جنيه
        return Math.max(suggested, 1);
    }

    // دالة تحديث اقتراح العمولة
    function updateCommissionSuggestion() {
        const price = parseFloat(priceInput?.value || 0);
        const commission = parseFloat(brokerCommissionInput?.value || 0);

        if (price > 0) {
            const suggested = calculateSuggestedCommission(price);
            const maxAllowed = Math.max(Math.round(price * 0.25), 1); // 25% كحد أقصى، لكن لا يقل عن 1

            if (suggestedAmount) {
                suggestedAmount.textContent = suggested.toLocaleString();
            }

            if (commissionSuggestion) {
                commissionSuggestion.style.display = 'block';
            }

            // التحقق من تجاوز الحد الأقصى
            if (commission > maxAllowed && commission > 0) {
                if (commissionError) {
                    commissionError.style.display = 'block';
                    commissionError.querySelector('.error-text').textContent =
                        `Commission cannot exceed 25% of the item price (${maxAllowed.toLocaleString()} EGP maximum)`;
                }
                if (brokerCommissionInput) {
                    brokerCommissionInput.style.borderColor = '#dc3545';
                }
            } else if (commission < 1 && commission > 0) {
                if (commissionError) {
                    commissionError.style.display = 'block';
                    commissionError.querySelector('.error-text').textContent =
                        'Commission must be at least 1 EGP';
                }
                if (brokerCommissionInput) {
                    brokerCommissionInput.style.borderColor = '#dc3545';
                }
            } else {
                if (commissionError) {
                    commissionError.style.display = 'none';
                }
                if (brokerCommissionInput) {
                    brokerCommissionInput.style.borderColor = '';
                }
            }
        } else {
            if (commissionSuggestion) {
                commissionSuggestion.style.display = 'none';
            }
            if (commissionError) {
                commissionError.style.display = 'none';
            }
        }
    }

    // مراقبة تغيير السعر
    if (priceInput) {
        priceInput.addEventListener('input', updateCommissionSuggestion);
    }

    // مراقبة تغيير العمولة
    if (brokerCommissionInput) {
        brokerCommissionInput.addEventListener('input', updateCommissionSuggestion);
    }

    if (brokeringToggle && brokerCommissionCard) {
        brokeringToggle.addEventListener('change', function() {
            if (this.checked) {
                brokerCommissionCard.style.display = 'block';
                if (brokerCommissionInput) {
                    brokerCommissionInput.setAttribute('required', 'required');
                }
            } else {
                brokerCommissionCard.style.display = 'none';
                if (brokerCommissionInput) {
                    brokerCommissionInput.removeAttribute('required');
                    brokerCommissionInput.value = '';
                }
            }
        });
    }

    if (sponsoredToggle && sponsorshipCostCard) {
        sponsoredToggle.addEventListener('change', function() {
            if (this.checked) {
                sponsorshipCostCard.style.display = 'block';
                if (sponsorshipCostInput) {
                    sponsorshipCostInput.setAttribute('required', 'required');
                }
            } else {
                sponsorshipCostCard.style.display = 'none';
                if (sponsorshipCostInput) {
                    sponsorshipCostInput.removeAttribute('required');
                    sponsorshipCostInput.value = '';
                }
            }
        });
    }

    // Form submission validation
    const form = document.querySelector('.listing-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            let errorMessage = '';

            // Check Marketing Support validation (only for premium users)
            if (isPremiumUser && brokeringToggle && brokeringToggle.checked) {
                const price = parseFloat(priceInput?.value || 0);
                const commission = parseFloat(brokerCommissionInput?.value || 0);
                const maxAllowed = Math.max(Math.round(price * 0.25), 1); // الحد الأدنى 1

                if (!brokerCommissionInput || !brokerCommissionInput.value || commission < 1) {
                    isValid = false;
                    errorMessage = 'Marketing commission is required and must be at least 1 EGP when Marketing Support is enabled.';
                } else if (commission > maxAllowed && price > 0) {
                    isValid = false;
                    errorMessage = `Marketing commission cannot exceed 25% of the item price (${maxAllowed.toLocaleString()} EGP maximum).`;
                }
            }

            // Check Sponsored Listing validation (only for premium users)
            if (isPremiumUser && sponsoredToggle && sponsoredToggle.checked) {
                const sponsorshipCost = parseFloat(sponsorshipCostInput?.value || 0);

                if (!sponsorshipCostInput || !sponsorshipCostInput.value || sponsorshipCost < 1) {
                    isValid = false;
                    errorMessage = 'Sponsorship budget is required and must be at least 1 EGP when Sponsored Listing is enabled.';
                }
            }

            // Check if at least one image is uploaded
            if (uploadedFiles.length === 0) {
                isValid = false;
                errorMessage = 'Please upload at least one image for your listing.';

                // Navigate to step 2 where images are uploaded
                currentStep = 2;
                showStep(currentStep);

                // Highlight the upload area
                const uploadZone = document.getElementById('upload-zone');
                if (uploadZone) {
                    uploadZone.style.borderColor = '#dc3545';
                    uploadZone.style.background = 'rgba(220, 53, 69, 0.05)';

                    setTimeout(() => {
                        uploadZone.style.borderColor = '#dee2e6';
                        uploadZone.style.background = 'transparent';
                    }, 3000);
                }
            }

            if (!isValid) {
                e.preventDefault();
                alert(errorMessage);
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Listing...';
                submitBtn.disabled = true;
            }
        });
    }

    // Form validation styles
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('error');
            } else {
                this.classList.remove('error');
            }
        });

        control.addEventListener('input', function() {
            this.classList.remove('error');
        });
    });

    // Initialize first step
    showStep(1);
});

// Add error styles
const errorStyle = document.createElement('style');
errorStyle.textContent = `
    .form-control.error {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
    }

    .progress-step.completed .step-circle {
        background: #28a745 !important;
    }

    .progress-step.completed .step-circle::after {
        content: '✓';
        font-weight: bold;
    }
`;
document.head.appendChild(errorStyle);

function renderParallelAttributes(attributes) {
  const grid = document.getElementById('attributes-grid');
  grid.innerHTML = '';
  attributes.forEach(attr => {
    const colDiv = document.createElement('div');
    colDiv.className = 'col-md-6 mb-3';

    const formGroup = document.createElement('div');
    formGroup.className = 'form-group';

    const label = document.createElement('label');
    label.className = 'form-label';
    label.textContent = attr.attribute_label;
    if (attr.is_required) {
      label.innerHTML += ' <span class="text-danger">*</span>';
    }

    let input;
    if (attr.attribute_type === 'select' && attr.attribute_options) {
      input = document.createElement('select');
      input.className = 'form-control';
      input.name = attr.attribute_name;

      const defaultOption = document.createElement('option');
      defaultOption.value = '';
      defaultOption.textContent = `اختر ${attr.attribute_label}`;
      input.appendChild(defaultOption);

      Object.keys(attr.attribute_options).forEach(key => {
        const option = document.createElement('option');
        option.value = key;
        option.textContent = attr.attribute_options[key];
        input.appendChild(option);
      });
    } else {
      input = document.createElement('input');
      input.type = 'text';
      input.className = 'form-control';
      input.name = attr.attribute_name;
      input.placeholder = `أدخل ${attr.attribute_label}`;
    }

    formGroup.appendChild(label);
    formGroup.appendChild(input);
    colDiv.appendChild(formGroup);
    grid.appendChild(colDiv);
  });
}
    let categoryData = {};

    document.addEventListener('DOMContentLoaded', function() {
      const mainCategory = document.getElementById('main-category');
      const subCategoryContainer = document.getElementById('sub-category-container');
      const subCategory = document.getElementById('sub-category');
      const attributesContainer = document.getElementById('parallel-attributes-container');

      // تحميل التصنيفات الرئيسية من الـ API
      loadRootCategories();

      mainCategory.addEventListener('change', function() {
        if (this.value) {
          loadSubCategories(this.value);
          subCategoryContainer.style.display = 'block';
          // Update hidden input
          document.getElementById('selected-category').value = this.value;
        } else {
          subCategoryContainer.style.display = 'none';
          attributesContainer.style.display = 'none';
          // Clear hidden inputs
          document.getElementById('selected-category').value = '';
          document.getElementById('selected-subcategory').value = '';
        }
      });

      subCategory.addEventListener('change', function() {
        if (this.value) {
          loadCategoryAttributes(this.value);
          attributesContainer.style.display = 'block';
          // Update hidden input
          document.getElementById('selected-subcategory').value = this.value;
        } else {
          attributesContainer.style.display = 'none';
          // Clear hidden input
          document.getElementById('selected-subcategory').value = '';
        }
      });

      async function loadRootCategories() {
        try {
          const response = await fetch('/api/nested-categories');
          const data = await response.json();
          if (data.success) {
            mainCategory.innerHTML = '<option value="">اختر التصنيف</option>';
            data.data.forEach(category => {
              const option = document.createElement('option');
              option.value = category.slug;
              option.textContent = category.name;
              mainCategory.appendChild(option);
              categoryData[category.slug] = category;
            });
          }
        } catch (error) {
          console.error('Error loading root categories:', error);
        }
      }

      function loadSubCategories(mainCategorySlug) {
        const category = categoryData[mainCategorySlug];
        if (!category || !category.children) {
          return;
        }
        subCategory.innerHTML = '<option value="">اختر التصنيف الفرعي</option>';
        category.children.forEach(child => {
          const option = document.createElement('option');
          option.value = child.slug;
          option.textContent = child.name;
          subCategory.appendChild(option);
        });
      }

      async function loadCategoryAttributes(categorySlug) {
        try {
          const response = await fetch(`/api/category-attributes/${categorySlug}`);
          const data = await response.json();
          if (data.success && data.data.attributes.length > 0) {
            renderParallelAttributes(data.data.attributes);
          } else {
            document.getElementById('attributes-grid').innerHTML =
              '<div class="col-12"><div class="alert alert-info">لا توجد خصائص محددة لهذا التصنيف</div></div>';
          }
        } catch (error) {
          console.error('Error loading attributes:', error);
        }
      }

      function renderParallelAttributes(attributes) {
        const grid = document.getElementById('attributes-grid');
        grid.innerHTML = '';
        attributes.forEach(attr => {
          const colDiv = document.createElement('div');
          colDiv.className = 'col-md-6 mb-3';

          const formGroup = document.createElement('div');
          formGroup.className = 'form-group';

          const label = document.createElement('label');
          label.className = 'form-label';
          label.textContent = attr.attribute_label;
          if (attr.is_required) {
            label.innerHTML += ' <span class="text-danger">*</span>';
          }

          let input;
          if (attr.attribute_type === 'select' && attr.attribute_options) {
            input = document.createElement('select');
            input.className = 'form-control';
            input.name = attr.attribute_name;

            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = `اختر ${attr.attribute_label}`;
            input.appendChild(defaultOption);

            Object.keys(attr.attribute_options).forEach(key => {
              const option = document.createElement('option');
              option.value = key;
              option.textContent = attr.attribute_options[key];
              input.appendChild(option);
            });
          } else {
            input = document.createElement('input');
            input.type = 'text';
            input.className = 'form-control';
            input.name = attr.attribute_name;
            input.placeholder = `أدخل ${attr.attribute_label}`;
          }

          formGroup.appendChild(label);
          formGroup.appendChild(input);
          colDiv.appendChild(formGroup);
          grid.appendChild(colDiv);
        });
      }

      @if(isset($isEditing) && $isEditing)
      // Load existing ad data for editing
      setTimeout(function() {
          console.log('Loading existing ad data for editing...');

          // Set category data
          const mainCategorySlug = '{{ $editingAd->category->parent_id ? $editingAd->category->parent->slug : $editingAd->category->slug }}';
          const subCategorySlug = '{{ $editingAd->category->parent_id ? $editingAd->category->slug : "" }}';

          // Set main category
          const mainCategorySelect = document.getElementById('main-category');
          if (mainCategorySelect) {
              mainCategorySelect.value = mainCategorySlug;
              mainCategorySelect.dispatchEvent(new Event('change'));

              // Set subcategory after a delay
              if (subCategorySlug) {
                  setTimeout(function() {
                      const subCategorySelect = document.getElementById('sub-category');
                      if (subCategorySelect) {
                          subCategorySelect.value = subCategorySlug;
                          subCategorySelect.dispatchEvent(new Event('change'));
                      }
                  }, 1000);
              }
          }



          console.log('Existing ad data loaded successfully');
      }, 500);


      @endif
    });

    // حساب دقائق الرعاية
    function calculateSponsoredMinutes() {
        const costInput = document.querySelector('input[name="sponsorship_cost"]');
        const displayElement = document.getElementById('sponsored-minutes-display');

        if (costInput && displayElement) {
            const cost = parseFloat(costInput.value) || 0;
            if (cost >= 10) {
                const minutes = Math.floor((cost / 100) * 60);
                const hours = Math.floor(minutes / 60);
                const remainingMinutes = minutes % 60;

                let timeText = '';
                if (hours > 0) {
                    timeText = `${hours} ساعة`;
                    if (remainingMinutes > 0) {
                        timeText += ` و ${remainingMinutes} دقيقة`;
                    }
                } else if (minutes > 0) {
                    timeText = `${minutes} دقيقة`;
                } else {
                    timeText = '';
                }

                if (timeText) {
                    displayElement.textContent = `← سيتم رعاية إعلانك لمدة ${timeText}`;
                    displayElement.style.color = '#007bff';
                } else {
                    displayElement.textContent = '';
                }
            } else if (cost > 0 && cost < 10) {
                displayElement.textContent = '← الحد الأدنى للرعاية 10 جنيه';
                displayElement.style.color = '#dc3545';
            } else {
                displayElement.textContent = '';
            }
        }
    }
  </script>
