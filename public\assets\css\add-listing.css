/* Add Listing Page Styles */

/* Hero Section */
.listing-hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.listing-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-features {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.feature-item i {
    color: #ffd700;
}

.hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 3rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Auth Required Card */
.auth-required-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.auth-icon i {
    font-size: 2rem;
    color: white;
}

.auth-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Form Section */
.listing-form-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.info-banner {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #667eea;
}

.info-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.info-content i {
    font-size: 1.5rem;
    color: #667eea;
}

.info-content h5 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.info-content p {
    margin: 0;
    color: #6c757d;
}

/* Alert Messages */
.alert {
    border-radius: 15px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border: none;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert i {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.alert ul {
    list-style: none;
    padding-left: 0;
}

.alert ul li {
    padding: 0.25rem 0;
}

.alert ul li::before {
    content: '•';
    margin-right: 0.5rem;
    font-weight: bold;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    opacity: 0.5;
    cursor: pointer;
    margin-left: auto;
}

.btn-close:hover {
    opacity: 1;
}

/* Form Steps */
.listing-form {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.step-info h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.step-info p {
    margin: 0;
    color: #6c757d;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group {
    position: relative;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #667eea;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.price-input {
    position: relative;
}

.currency {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-weight: 600;
}

.price-input .form-control {
    padding-left: 50px;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}


/* Image Upload Styles */
.image-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.image-upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.upload-zone {
    cursor: pointer;
    padding: 2rem;
    transition: all 0.3s ease;
}

.upload-zone:hover {
    transform: scale(1.02);
}

.upload-zone i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.upload-zone h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.upload-zone p {
    color: #6c757d;
    margin: 0;
}

.image-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.preview-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    aspect-ratio: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.preview-item:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.remove-image:hover {
    background: #dc3545;
    transform: scale(1.1);
}

/* Step Actions */
.step-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #f0f0f0;
}

.step-actions .btn {
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.step-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
}

/* Sidebar Styles */
.listing-sidebar {
    position: sticky;
    top: 2rem;
}

.progress-card,
.tips-card,
.support-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.progress-card:hover,
.tips-card:hover,
.support-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.progress-card h4,
.tips-card h4,
.support-card h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-weight: 700;
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.progress-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.progress-step.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.progress-step:not(.active) {
    background: #f8f9fa;
    color: #6c757d;
}

.progress-step:not(.active):hover {
    background: #e9ecef;
}

.step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
}

.progress-step.active .step-circle {
    background: rgba(255, 255, 255, 0.2);
}

.progress-step:not(.active) .step-circle {
    background: #dee2e6;
    color: #6c757d;
}

/* Premium Features */
.premium-features {
    display: grid;
    gap: 1rem;
}

.feature-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: #667eea;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.feature-card .feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.feature-card .feature-icon i {
    color: white;
    font-size: 1.2rem;
}

.feature-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.feature-content h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.feature-content p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Commission Suggestion Styles */
.commission-suggestion {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #28a745;
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #155724;
}

.commission-suggestion i {
    color: #28a745;
    font-size: 1rem;
}

.suggestion-text {
    flex: 1;
}

.commission-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    border: 1px solid #dc3545;
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #721c24;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.commission-error i {
    color: #dc3545;
    font-size: 1rem;
}

.error-text {
    flex: 1;
    font-weight: 600;
}

/* Review Section */
.review-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.seller-info-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.seller-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.seller-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
}

.seller-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.seller-details h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.seller-badges {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-trusted {
    background: #d4edda;
    color: #155724;
}

.badge-rank {
    background: #e2e3e5;
    color: #383d41;
}

.seller-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    display: flex;
    gap: 0.2rem;
}

.rating-stars i {
    color: #dee2e6;
    font-size: 0.9rem;
}

.rating-stars i.active {
    color: #ffc107;
}

.rating-level {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Terms Agreement */
.terms-agreement {
    margin: 1.5rem 0;
}

.checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    position: relative;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-container:hover .checkmark {
    border-color: #667eea;
}

.checkbox-container input:checked ~ .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkmark::after {
    content: '';
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark::after {
    display: block;
}

.checkbox-text {
    color: #6c757d;
    line-height: 1.4;
}

.checkbox-text a {
    color: #667eea;
    text-decoration: none;
}

.checkbox-text a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 992px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .hero-features {
        justify-content: center;
    }

    .auth-actions {
        flex-direction: column;
        align-items: center;
    }

    .auth-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .step-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .category-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .category-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .category-section {
        padding: 1.5rem;
        margin: 1rem 0;
    }

    #parallel-attributes-container .card-body {
        padding: 1.5rem;
    }

    #attributes-grid {
        gap: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-features {
        justify-content: center;
    }

    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .step-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .seller-header {
        flex-direction: column;
        text-align: center;
    }

    .seller-badges {
        justify-content: center;
    }
}
