<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Category;

class AdvancedCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing categories
        Category::truncate();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create comprehensive category structure using nested set
        $this->createVehiclesCategory();
        $this->createElectronicsCategory();
        $this->createRealEstateCategory();
        $this->createFashionCategory();
        $this->createHomeGardenCategory();

        // Rebuild the tree structure
        Category::fixTree();
    }

    private function createVehiclesCategory()
    {
        $vehicles = Category::create([
            'name' => 'المركبات',
            'slug' => 'vehicles',
            'description' => 'جميع أنواع المركبات',
            'icon' => 'assets/categories/icon/car.svg',
            'image' => 'assets/categories/bg/vehicles.png'
        ]);

        // Cars with deep levels
        $cars = $vehicles->children()->create([
            'name' => 'سيارات',
            'slug' => 'cars',
            'description' => 'سيارات بجميع أنواعها'
        ]);

        $this->createCarBrands($cars);
        $this->createCarYears($cars);
        $this->createCarConditions($cars);
        $this->createCarFuelTypes($cars);
        $this->createCarTransmissions($cars);
        $this->createCarColors($cars);
        $this->createCarBodyTypes($cars);
        $this->createCarEngineTypes($cars);

        // Motorcycles with deep levels
        $motorcycles = $vehicles->children()->create([
            'name' => 'موتوسيكلات',
            'slug' => 'motorcycles',
            'description' => 'دراجات نارية بجميع أنواعها'
        ]);

        $this->createMotorcycleBrands($motorcycles);
        $this->createMotorcycleEngines($motorcycles);
        $this->createMotorcycleTypes($motorcycles);
        $this->createMotorcycleConditions($motorcycles);

        // Boats
        $boats = $vehicles->children()->create([
            'name' => 'قوارب',
            'slug' => 'boats',
            'description' => 'قوارب ويخوت'
        ]);

        $this->createBoatTypes($boats);
        $this->createBoatSizes($boats);
        $this->createBoatEngines($boats);
    }

    private function createCarBrands($parent)
    {
        // Create brand category with direct brand options (not nested)
        $toyota = $parent->children()->create(['name' => 'تويوتا', 'slug' => 'toyota']);

        // Add Toyota models as children
        $toyota->children()->create(['name' => 'كورولا', 'slug' => 'toyota-corolla']);
        $toyota->children()->create(['name' => 'كامري', 'slug' => 'toyota-camry']);
        $toyota->children()->create(['name' => 'ياريس', 'slug' => 'toyota-yaris']);
        $toyota->children()->create(['name' => 'هايلكس', 'slug' => 'toyota-hilux']);
        $toyota->children()->create(['name' => 'لاند كروزر', 'slug' => 'toyota-land-cruiser']);

        $honda = $parent->children()->create(['name' => 'هوندا', 'slug' => 'honda']);
        $honda->children()->create(['name' => 'سيفيك', 'slug' => 'honda-civic']);
        $honda->children()->create(['name' => 'أكورد', 'slug' => 'honda-accord']);
        $honda->children()->create(['name' => 'CR-V', 'slug' => 'honda-cr-v']);

        $bmw = $parent->children()->create(['name' => 'BMW', 'slug' => 'bmw']);
        $bmw->children()->create(['name' => '320i', 'slug' => 'bmw-320i']);
        $bmw->children()->create(['name' => '520i', 'slug' => 'bmw-520i']);
        $bmw->children()->create(['name' => 'X3', 'slug' => 'bmw-x3']);
        $bmw->children()->create(['name' => 'X5', 'slug' => 'bmw-x5']);

        $mercedes = $parent->children()->create(['name' => 'مرسيدس', 'slug' => 'mercedes']);
        $mercedes->children()->create(['name' => 'C-Class', 'slug' => 'mercedes-c-class']);
        $mercedes->children()->create(['name' => 'E-Class', 'slug' => 'mercedes-e-class']);
        $mercedes->children()->create(['name' => 'S-Class', 'slug' => 'mercedes-s-class']);

        $hyundai = $parent->children()->create(['name' => 'هيونداي', 'slug' => 'hyundai']);
        $hyundai->children()->create(['name' => 'إلانترا', 'slug' => 'hyundai-elantra']);
        $hyundai->children()->create(['name' => 'سوناتا', 'slug' => 'hyundai-sonata']);
        $hyundai->children()->create(['name' => 'توكسون', 'slug' => 'hyundai-tucson']);
    }

    private function createCarYears($parent)
    {
        // Create years directly under cars (not as a separate category)
        // This is wrong approach - we need parallel structure
        // Will fix this in the new approach
    }

    private function createCarConditions($parent)
    {
        $conditions = $parent->children()->create([
            'name' => 'حالة السيارة',
            'slug' => 'car-conditions',
            'description' => 'حالة السيارة'
        ]);

        $conditions->children()->create(['name' => 'جديدة', 'slug' => 'new']);
        $conditions->children()->create(['name' => 'مستعملة', 'slug' => 'used']);
        $conditions->children()->create(['name' => 'فابريكا', 'slug' => 'factory']);
        $conditions->children()->create(['name' => 'راشة', 'slug' => 'accident']);
        $conditions->children()->create(['name' => 'معرض', 'slug' => 'showroom']);
        $conditions->children()->create(['name' => 'وكالة', 'slug' => 'agency']);
    }

    private function createCarFuelTypes($parent)
    {
        $fuels = $parent->children()->create([
            'name' => 'نوع الوقود',
            'slug' => 'car-fuel-types',
            'description' => 'أنواع الوقود'
        ]);

        $fuels->children()->create(['name' => 'بنزين', 'slug' => 'petrol']);
        $fuels->children()->create(['name' => 'ديزل', 'slug' => 'diesel']);
        $fuels->children()->create(['name' => 'هايبرد', 'slug' => 'hybrid']);
        $fuels->children()->create(['name' => 'كهربائي', 'slug' => 'electric']);
        $fuels->children()->create(['name' => 'غاز طبيعي', 'slug' => 'cng']);
        $fuels->children()->create(['name' => 'بنزين + غاز', 'slug' => 'petrol-gas']);
    }

    private function createCarTransmissions($parent)
    {
        $transmissions = $parent->children()->create([
            'name' => 'ناقل الحركة',
            'slug' => 'car-transmissions',
            'description' => 'أنواع ناقل الحركة'
        ]);

        $transmissions->children()->create(['name' => 'يدوي', 'slug' => 'manual']);
        $transmissions->children()->create(['name' => 'أوتوماتيك', 'slug' => 'automatic']);
        $transmissions->children()->create(['name' => 'CVT', 'slug' => 'cvt']);
        $transmissions->children()->create(['name' => 'تيبترونيك', 'slug' => 'tiptronic']);
        $transmissions->children()->create(['name' => 'DSG', 'slug' => 'dsg']);
    }

    private function createCarColors($parent)
    {
        $colors = $parent->children()->create([
            'name' => 'لون السيارة',
            'slug' => 'car-colors',
            'description' => 'ألوان السيارات'
        ]);

        $colors->children()->create(['name' => 'أبيض', 'slug' => 'white']);
        $colors->children()->create(['name' => 'أسود', 'slug' => 'black']);
        $colors->children()->create(['name' => 'فضي', 'slug' => 'silver']);
        $colors->children()->create(['name' => 'رمادي', 'slug' => 'gray']);
        $colors->children()->create(['name' => 'أحمر', 'slug' => 'red']);
        $colors->children()->create(['name' => 'أزرق', 'slug' => 'blue']);
        $colors->children()->create(['name' => 'أخضر', 'slug' => 'green']);
        $colors->children()->create(['name' => 'ذهبي', 'slug' => 'gold']);
        $colors->children()->create(['name' => 'بني', 'slug' => 'brown']);
        $colors->children()->create(['name' => 'برتقالي', 'slug' => 'orange']);
        $colors->children()->create(['name' => 'أصفر', 'slug' => 'yellow']);
        $colors->children()->create(['name' => 'بنفسجي', 'slug' => 'purple']);
    }

    private function createCarBodyTypes($parent)
    {
        $bodyTypes = $parent->children()->create([
            'name' => 'نوع الهيكل',
            'slug' => 'car-body-types',
            'description' => 'أنواع هياكل السيارات'
        ]);

        $bodyTypes->children()->create(['name' => 'سيدان', 'slug' => 'sedan']);
        $bodyTypes->children()->create(['name' => 'هاتشباك', 'slug' => 'hatchback']);
        $bodyTypes->children()->create(['name' => 'SUV', 'slug' => 'suv']);
        $bodyTypes->children()->create(['name' => 'كوبيه', 'slug' => 'coupe']);
        $bodyTypes->children()->create(['name' => 'كونفرتيبل', 'slug' => 'convertible']);
        $bodyTypes->children()->create(['name' => 'ستيشن واجن', 'slug' => 'station-wagon']);
        $bodyTypes->children()->create(['name' => 'بيك أب', 'slug' => 'pickup']);
        $bodyTypes->children()->create(['name' => 'فان', 'slug' => 'van']);
        $bodyTypes->children()->create(['name' => 'كروس أوفر', 'slug' => 'crossover']);
    }

    private function createCarEngineTypes($parent)
    {
        $engines = $parent->children()->create([
            'name' => 'نوع المحرك',
            'slug' => 'car-engine-types',
            'description' => 'أنواع محركات السيارات'
        ]);

        $engines->children()->create(['name' => '4 سلندر', 'slug' => '4-cylinder']);
        $engines->children()->create(['name' => '6 سلندر', 'slug' => '6-cylinder']);
        $engines->children()->create(['name' => '8 سلندر', 'slug' => '8-cylinder']);
        $engines->children()->create(['name' => 'تيربو', 'slug' => 'turbo']);
        $engines->children()->create(['name' => 'سوبر تشارج', 'slug' => 'supercharged']);
        $engines->children()->create(['name' => 'هايبرد', 'slug' => 'hybrid-engine']);
        $engines->children()->create(['name' => 'كهربائي', 'slug' => 'electric-engine']);
    }

    private function createMotorcycleBrands($parent)
    {
        $brands = $parent->children()->create([
            'name' => 'نوع الموتوسيكل',
            'slug' => 'motorcycle-brands',
            'description' => 'ماركات الموتوسيكلات'
        ]);

        // Dayang with models
        $dayang = $brands->children()->create(['name' => 'دايون', 'slug' => 'dayang']);
        $dayang->children()->create(['name' => 'دايون 150', 'slug' => 'dayang-150']);
        $dayang->children()->create(['name' => 'دايون 200', 'slug' => 'dayang-200']);
        $dayang->children()->create(['name' => 'دايون سبورت', 'slug' => 'dayang-sport']);

        // Halawa with models
        $halawa = $brands->children()->create(['name' => 'حلاوة', 'slug' => 'halawa']);
        $halawa->children()->create(['name' => 'حلاوة 125', 'slug' => 'halawa-125']);
        $halawa->children()->create(['name' => 'حلاوة 150', 'slug' => 'halawa-150']);
        $halawa->children()->create(['name' => 'حلاوة كلاسيك', 'slug' => 'halawa-classic']);

        // Yamaha with models
        $yamaha = $brands->children()->create(['name' => 'ياماها', 'slug' => 'yamaha']);
        $yamaha->children()->create(['name' => 'YZF-R1', 'slug' => 'yzf-r1']);
        $yamaha->children()->create(['name' => 'YZF-R6', 'slug' => 'yzf-r6']);
        $yamaha->children()->create(['name' => 'MT-07', 'slug' => 'mt-07']);
        $yamaha->children()->create(['name' => 'MT-09', 'slug' => 'mt-09']);
        $yamaha->children()->create(['name' => 'TMAX', 'slug' => 'tmax']);

        // Honda with models
        $hondaMotor = $brands->children()->create(['name' => 'هوندا', 'slug' => 'honda-motor']);
        $hondaMotor->children()->create(['name' => 'CBR1000RR', 'slug' => 'cbr1000rr']);
        $hondaMotor->children()->create(['name' => 'CBR600RR', 'slug' => 'cbr600rr']);
        $hondaMotor->children()->create(['name' => 'CB650R', 'slug' => 'cb650r']);
        $hondaMotor->children()->create(['name' => 'PCX', 'slug' => 'pcx']);

        // Suzuki with models
        $suzuki = $brands->children()->create(['name' => 'سوزوكي', 'slug' => 'suzuki']);
        $suzuki->children()->create(['name' => 'GSX-R1000', 'slug' => 'gsx-r1000']);
        $suzuki->children()->create(['name' => 'GSX-R600', 'slug' => 'gsx-r600']);
        $suzuki->children()->create(['name' => 'V-Strom', 'slug' => 'v-strom']);

        // Kawasaki with models
        $kawasaki = $brands->children()->create(['name' => 'كاواساكي', 'slug' => 'kawasaki']);
        $kawasaki->children()->create(['name' => 'Ninja ZX-10R', 'slug' => 'ninja-zx-10r']);
        $kawasaki->children()->create(['name' => 'Ninja 650', 'slug' => 'ninja-650']);
        $kawasaki->children()->create(['name' => 'Z900', 'slug' => 'z900']);
    }

    private function createMotorcycleEngines($parent)
    {
        $engines = $parent->children()->create([
            'name' => 'حجم المحرك',
            'slug' => 'motorcycle-engines',
            'description' => 'أحجام محركات الموتوسيكلات'
        ]);

        $engines->children()->create(['name' => '50 سي سي', 'slug' => '50cc']);
        $engines->children()->create(['name' => '125 سي سي', 'slug' => '125cc']);
        $engines->children()->create(['name' => '150 سي سي', 'slug' => '150cc']);
        $engines->children()->create(['name' => '200 سي سي', 'slug' => '200cc']);
        $engines->children()->create(['name' => '250 سي سي', 'slug' => '250cc']);
        $engines->children()->create(['name' => '400 سي سي', 'slug' => '400cc']);
        $engines->children()->create(['name' => '600 سي سي', 'slug' => '600cc']);
        $engines->children()->create(['name' => '750 سي سي', 'slug' => '750cc']);
        $engines->children()->create(['name' => '1000 سي سي', 'slug' => '1000cc']);
        $engines->children()->create(['name' => '1200 سي سي', 'slug' => '1200cc']);
    }

    private function createMotorcycleTypes($parent)
    {
        $types = $parent->children()->create([
            'name' => 'نوع الموتوسيكل',
            'slug' => 'motorcycle-types',
            'description' => 'أنواع الموتوسيكلات'
        ]);

        $types->children()->create(['name' => 'سبورت', 'slug' => 'sport']);
        $types->children()->create(['name' => 'كروزر', 'slug' => 'cruiser']);
        $types->children()->create(['name' => 'تورينج', 'slug' => 'touring']);
        $types->children()->create(['name' => 'أدفنتشر', 'slug' => 'adventure']);
        $types->children()->create(['name' => 'ستريت', 'slug' => 'street']);
        $types->children()->create(['name' => 'سكوتر', 'slug' => 'scooter']);
        $types->children()->create(['name' => 'دراجة ترابية', 'slug' => 'dirt-bike']);
        $types->children()->create(['name' => 'كافيه ريسر', 'slug' => 'cafe-racer']);
    }

    private function createMotorcycleConditions($parent)
    {
        $conditions = $parent->children()->create([
            'name' => 'حالة الموتوسيكل',
            'slug' => 'motorcycle-conditions',
            'description' => 'حالة الموتوسيكل'
        ]);

        $conditions->children()->create(['name' => 'جديد', 'slug' => 'new']);
        $conditions->children()->create(['name' => 'مستعمل', 'slug' => 'used']);
        $conditions->children()->create(['name' => 'يحتاج إصلاح', 'slug' => 'needs-repair']);
        $conditions->children()->create(['name' => 'للقطع الغيار', 'slug' => 'for-parts']);
        $conditions->children()->create(['name' => 'معرض', 'slug' => 'showroom']);
    }

    private function createBoatTypes($parent)
    {
        $types = $parent->children()->create([
            'name' => 'نوع القارب',
            'slug' => 'boat-types',
            'description' => 'أنواع القوارب'
        ]);

        $types->children()->create(['name' => 'قارب صيد', 'slug' => 'fishing-boat']);
        $types->children()->create(['name' => 'يخت', 'slug' => 'yacht']);
        $types->children()->create(['name' => 'قارب سريع', 'slug' => 'speedboat']);
        $types->children()->create(['name' => 'قارب شراعي', 'slug' => 'sailboat']);
        $types->children()->create(['name' => 'كاتاماران', 'slug' => 'catamaran']);
        $types->children()->create(['name' => 'قارب مطاطي', 'slug' => 'inflatable-boat']);
    }

    private function createBoatSizes($parent)
    {
        $sizes = $parent->children()->create([
            'name' => 'حجم القارب',
            'slug' => 'boat-sizes',
            'description' => 'أحجام القوارب'
        ]);

        $sizes->children()->create(['name' => 'صغير (أقل من 20 قدم)', 'slug' => 'small']);
        $sizes->children()->create(['name' => 'متوسط (20-40 قدم)', 'slug' => 'medium']);
        $sizes->children()->create(['name' => 'كبير (40-80 قدم)', 'slug' => 'large']);
        $sizes->children()->create(['name' => 'ضخم (أكثر من 80 قدم)', 'slug' => 'mega']);
    }

    private function createBoatEngines($parent)
    {
        $engines = $parent->children()->create([
            'name' => 'نوع المحرك',
            'slug' => 'boat-engines',
            'description' => 'أنواع محركات القوارب'
        ]);

        $engines->children()->create(['name' => 'محرك خارجي', 'slug' => 'outboard']);
        $engines->children()->create(['name' => 'محرك داخلي', 'slug' => 'inboard']);
        $engines->children()->create(['name' => 'شراعي', 'slug' => 'sail']);
        $engines->children()->create(['name' => 'كهربائي', 'slug' => 'electric']);
        $engines->children()->create(['name' => 'هايبرد', 'slug' => 'hybrid']);
    }

    private function createElectronicsCategory()
    {
        $electronics = Category::create([
            'name' => 'الإلكترونيات',
            'slug' => 'electronics',
            'description' => 'جميع الأجهزة الإلكترونية',
            'icon' => 'assets/categories/icon/electronics.svg',
            'image' => 'assets/categories/bg/electronics.png'
        ]);

        // Mobile Phones with deep levels
        $mobiles = $electronics->children()->create([
            'name' => 'موبايلات',
            'slug' => 'mobiles',
            'description' => 'هواتف ذكية'
        ]);

        $this->createMobileBrands($mobiles);
        $this->createMobileStorage($mobiles);
        $this->createMobileRAM($mobiles);
        $this->createMobileColors($mobiles);
        $this->createMobileConditions($mobiles);
        $this->createMobileBatteries($mobiles);
        $this->createMobileScreenSizes($mobiles);
        $this->createMobileCameras($mobiles);

        // Laptops with deep levels
        $laptops = $electronics->children()->create([
            'name' => 'لابتوبات',
            'slug' => 'laptops',
            'description' => 'أجهزة كمبيوتر محمولة'
        ]);

        $this->createLaptopBrands($laptops);
        $this->createLaptopProcessors($laptops);
        $this->createLaptopRAM($laptops);
        $this->createLaptopStorage($laptops);
        $this->createLaptopScreenSizes($laptops);
        $this->createLaptopGraphics($laptops);
        $this->createLaptopConditions($laptops);

        // Tablets
        $tablets = $electronics->children()->create([
            'name' => 'تابلت',
            'slug' => 'tablets',
            'description' => 'أجهزة لوحية'
        ]);

        $this->createTabletBrands($tablets);
        $this->createTabletScreenSizes($tablets);
        $this->createTabletStorage($tablets);

        // TVs
        $tvs = $electronics->children()->create([
            'name' => 'تلفزيونات',
            'slug' => 'tvs',
            'description' => 'أجهزة تلفزيون'
        ]);

        $this->createTVBrands($tvs);
        $this->createTVSizes($tvs);
        $this->createTVTypes($tvs);
        $this->createTVResolutions($tvs);
    }

    // Mobile methods (simplified for now)
    private function createMobileBrands($parent) { /* Will implement in next part */ }
    private function createMobileStorage($parent) { /* Will implement in next part */ }
    private function createMobileRAM($parent) { /* Will implement in next part */ }
    private function createMobileColors($parent) { /* Will implement in next part */ }
    private function createMobileConditions($parent) { /* Will implement in next part */ }
    private function createMobileBatteries($parent) { /* Will implement in next part */ }
    private function createMobileScreenSizes($parent) { /* Will implement in next part */ }
    private function createMobileCameras($parent) { /* Will implement in next part */ }

    // Laptop methods (simplified for now)
    private function createLaptopBrands($parent) { /* Will implement in next part */ }
    private function createLaptopProcessors($parent) { /* Will implement in next part */ }
    private function createLaptopRAM($parent) { /* Will implement in next part */ }
    private function createLaptopStorage($parent) { /* Will implement in next part */ }
    private function createLaptopScreenSizes($parent) { /* Will implement in next part */ }
    private function createLaptopGraphics($parent) { /* Will implement in next part */ }
    private function createLaptopConditions($parent) { /* Will implement in next part */ }

    // Tablet methods (simplified for now)
    private function createTabletBrands($parent) { /* Will implement in next part */ }
    private function createTabletScreenSizes($parent) { /* Will implement in next part */ }
    private function createTabletStorage($parent) { /* Will implement in next part */ }

    // TV methods (simplified for now)
    private function createTVBrands($parent) { /* Will implement in next part */ }
    private function createTVSizes($parent) { /* Will implement in next part */ }
    private function createTVTypes($parent) { /* Will implement in next part */ }
    private function createTVResolutions($parent) { /* Will implement in next part */ }

    // Real Estate and Fashion methods (simplified for now)
    private function createRealEstateCategory() { /* Will implement in next part */ }
    private function createFashionCategory() { /* Will implement in next part */ }
    private function createHomeGardenCategory() { /* Will implement in next part */ }
}
