<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('username', 191)->unique()->index();
            $table->string('email', 191)->unique();
            $table->string('mobile', 15)->unique()->nullable();
            $table->smallInteger('gender')->nullable();
            $table->boolean('is_broker')->default(false); 
            $table->decimal('rating', 2, 1)->default(0);
            $table->unsignedInteger('ratings_count')->default(0);
            $table->string('password');
            $table->integer('Number_Ads')->default(0); // التقييم الشخص العادي من 1 إلى 60
            $table->integer('rank')->default('0'); // الرتبة (تاجر، admin، غير موثوق، إلخ)
            $table->boolean('is_trusted')->default(false); // هل المستخدم موثوق؟
            $table->boolean('is_active')->default(true);
            $table->string('email_verification_token')->nullable()->index();
            $table->timestamp('mobile_verified_at')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('avatar');
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('cascade');
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('cascade');
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('cascade');
            $table->string('address')->nullable();
            $table->string('zip_code', 191)->nullable();
            $table->foreignId('timezone_id')->nullable()->constrained('timezones')->onDelete('cascade');
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
