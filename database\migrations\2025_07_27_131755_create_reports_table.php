<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('reporter_id'); // المستخدم اللي عمل الإبلاغ
            $table->string('reportable_type'); // نوع الشيء المبلغ عنه (Ad, Comment, User, Review, Message)
            $table->uuid('reportable_id'); // ID الشيء المبلغ عنه
            $table->string('reason'); // سبب الإبلاغ
            $table->text('description')->nullable(); // وصف تفصيلي
            $table->enum('status', ['pending', 'reviewed', 'resolved', 'dismissed'])->default('pending');
            $table->uuid('reviewed_by')->nullable(); // الأدمن اللي راجع الإبلاغ
            $table->text('admin_notes')->nullable(); // ملاحظات الأدمن
            $table->timestamp('reviewed_at')->nullable();
            $table->json('metadata')->nullable(); // بيانات إضافية (مثل معلومات الشات)
            $table->timestamps();

            // Foreign keys
            $table->foreign('reporter_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewed_by')->references('id')->on('admins')->onDelete('set null');

            // Indexes
            $table->index(['reportable_type', 'reportable_id']);
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
