<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('broker_interests', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('broker_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('ad_id')->constrained('ads')->onDelete('cascade');
            $table->integer('points_paid'); // النقاط المدفوعة للاهتمام
            $table->integer('points_earned')->default(0); // النقاط المكتسبة عند إدخال الكود
            $table->boolean('code_entered')->default(false); // هل تم إدخال الكود
            $table->timestamp('code_entered_at')->nullable(); // وقت إدخال الكود
            $table->enum('status', ['active', 'withdrawn', 'expired'])->default('active');
            $table->timestamp('withdrawal_requested_at')->nullable(); // وقت طلب الانسحاب
            $table->timestamp('expires_at'); // تاريخ انتهاء فترة الاهتمام (10 أيام)
            $table->timestamps();

            // فهارس للبحث السريع
            $table->index(['broker_id', 'status']);
            $table->index(['ad_id', 'status']);
            $table->index(['expires_at']);

            // منع الاهتمام المكرر
            $table->unique(['broker_id', 'ad_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('broker_interests');
    }
};
