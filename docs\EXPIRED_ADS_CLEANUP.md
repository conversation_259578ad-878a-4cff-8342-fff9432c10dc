# Expired Ads Cleanup System

## Overview

The Expired Ads Cleanup System automatically deletes ads that are older than 30 days along with their associated data (images, reviews, reports, etc.) to keep the database clean and optimize storage usage.

## Features

- ✅ **Automatic Cleanup**: Runs daily via Laravel scheduler
- ✅ **Complete Data Removal**: Deletes ads, images, reviews, reports, and admin notes
- ✅ **Storage Cleanup**: Removes image files from storage disk
- ✅ **Dry Run Mode**: Test what would be deleted without actually deleting
- ✅ **Detailed Logging**: Comprehensive logs for monitoring and debugging
- ✅ **Performance Optimized**: Uses database indexes for efficient queries
- ✅ **Safe Deletion**: Uses foreign key constraints for data integrity

## Command Usage

### Manual Execution

```bash
# Delete expired ads (older than 30 days)
php artisan ads:delete-expired

# Dry run - see what would be deleted without actually deleting
php artisan ads:delete-expired --dry-run
```

### Automatic Execution

The command runs automatically every day at midnight via Laravel's task scheduler. No manual intervention required.

## What Gets Deleted

When an ad is older than 30 days, the system deletes:

1. **The Ad Record** - Main ad data from `ads` table
2. **Associated Images** - All images from `media` table and storage files
3. **Product Reviews** - All reviews from `product_reviews` table
4. **Reports** - All reports from `report_ads` table
5. **Admin Notes** - All admin notes from `admin_notes` table
6. **Broker Interests** - Sets ad_id to null in `broker_interests` table

## Database Relations

The cleanup leverages foreign key constraints with cascade delete:

```sql
-- Media table (polymorphic relationship)
FOREIGN KEY (mediaable_id) REFERENCES ads(id) ON DELETE CASCADE

-- Product reviews
FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE

-- Report ads
FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE

-- Admin notes
FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE

-- Broker interests (set null to preserve broker data)
FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE SET NULL
```

## Performance Optimization

- **Database Index**: Added index on `ads.created_at` for efficient date-based queries
- **Batch Processing**: Processes ads one by one to avoid memory issues
- **Background Execution**: Runs in background without overlapping
- **Efficient Queries**: Uses eager loading to minimize database queries

## Monitoring and Logging

### Console Output
```
🔍 Starting expired ads cleanup...
📊 Found 5 expired ads to delete.
🔄 Processing ad: Old Car Ad (ID: abc-123)
   🖼️  Deleted image: ad/images/car1.jpg
   🖼️  Deleted image: ad/images/car2.jpg
   ✅ Deleted ad: Old Car Ad (2 images, 3 reviews)

📈 Cleanup Summary:
   🗑️  Deleted Ads: 5
   🖼️  Deleted Images: 12
   📝 Deleted Reviews: 8
   📁 Deleted Files: 12

✅ Expired ads cleanup completed successfully!
```

### Log Files
Detailed logs are written to `storage/logs/laravel.log`:

```json
{
  "message": "Expired ads cleanup completed",
  "deleted_ads": 5,
  "deleted_images": 12,
  "deleted_reviews": 8,
  "deleted_files": 12,
  "cutoff_date": "2025-06-29 19:48:40"
}
```

## Configuration

### Scheduler Configuration
Located in `app/Console/Kernel.php`:

```php
// Delete expired ads (older than 30 days) with their images and reviews
$schedule->command('ads:delete-expired')
         ->daily()
         ->withoutOverlapping()
         ->runInBackground();
```

### Customization
To change the expiration period, modify the command in `app/Console/Commands/DeleteExpiredAds.php`:

```php
// Change from 30 days to desired period
$expiredDate = Carbon::now()->subDays(30); // Change 30 to your desired number
```

## Safety Features

1. **Dry Run Mode**: Test before actual deletion
2. **Foreign Key Constraints**: Ensure data integrity
3. **Error Handling**: Graceful error handling with logging
4. **Transaction Safety**: Each ad deletion is isolated
5. **Backup Recommendation**: Regular database backups recommended

## Troubleshooting

### Common Issues

**Command not found:**
```bash
php artisan list | grep ads:delete-expired
```

**Permission errors:**
```bash
# Check storage permissions
ls -la storage/app/public/
```

**Database errors:**
```bash
# Check foreign key constraints
php artisan migrate:status
```

### Debug Mode
Enable detailed logging by setting `LOG_LEVEL=debug` in `.env` file.

## Best Practices

1. **Regular Monitoring**: Check logs regularly for any issues
2. **Database Backups**: Maintain regular backups before cleanup
3. **Storage Monitoring**: Monitor disk space usage
4. **Performance Testing**: Test on staging environment first
5. **Gradual Rollout**: Start with dry-run mode in production

## Related Commands

```bash
# View all scheduled commands
php artisan schedule:list

# Run scheduler manually (for testing)
php artisan schedule:run

# View command help
php artisan ads:delete-expired --help
```

## Security Considerations

- Command requires proper file system permissions
- Database user needs DELETE privileges
- Storage disk access required for file deletion
- Logs may contain sensitive information - secure log files

---

**Note**: This system is designed to run automatically. Manual execution should only be done for testing or emergency cleanup.
