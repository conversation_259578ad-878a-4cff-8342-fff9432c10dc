@extends('partials.admin')
@section('title', 'مراقبة نقاط المندوبين')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'broker-management'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'مراقبة نقاط المندوبين'])

            <!-- إحصائيات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">إجمالي المندوبين</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_brokers']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-users fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">إجمالي النقاط في النظام</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_points_in_system']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-coins fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">النقاط المنفقة</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_points_spent']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-arrow-down fa-2x text-danger"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">حسابات مشبوهة</h6>
                                    <h2 class="mb-0 number-font text-danger">{{ number_format($stats['suspicious_accounts']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">فلترة المندوبين</h3>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">الحد الأدنى للنقاط</label>
                                    <input type="number" name="min_points" class="form-control" 
                                           placeholder="0" value="{{ request('min_points') }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">الحد الأقصى للنقاط</label>
                                    <input type="number" name="max_points" class="form-control" 
                                           placeholder="100000" value="{{ request('max_points') }}">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">البحث بالاسم</label>
                                    <input type="text" name="name_search" class="form-control" 
                                           placeholder="اسم المندوب..." value="{{ request('name_search') }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <a href="{{ route('admin.broker-management.points-monitoring') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المندوبين -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">مندوبين النظام ({{ $brokers->total() }} مندوب)</h3>
                        </div>
                        <div class="card-body">
                            @if($brokers->count())
                                <div class="table-responsive">
                                    <table class="table table-bordered text-nowrap border-bottom">
                                        <thead>
                                            <tr>
                                                <th>المندوب</th>
                                                <th>النقاط الحالية</th>
                                                <th>إجمالي الاهتمامات</th>
                                                <th>الأكواد الناجحة</th>
                                                <th>النقاط المنفقة</th>
                                                <th>النقاط المكتسبة</th>
                                                <th>معدل النجاح</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($brokers as $broker)
                                                <tr class="{{ $broker->points > 50000 ? 'table-warning' : '' }}">
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div>
                                                                <strong>{{ $broker->name }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $broker->email }}</small>
                                                                @if($broker->is_trusted)
                                                                    <br>
                                                                    <span class="badge bg-primary">موثق</span>
                                                                @endif
                                                                @if($broker->points > 50000)
                                                                    <br>
                                                                    <span class="badge bg-warning">مشبوه</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <span class="badge bg-{{ $broker->points > 50000 ? 'danger' : ($broker->points > 10000 ? 'warning' : 'success') }} fs-6 me-2">
                                                                {{ number_format($broker->points) }}
                                                            </span>
                                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                    data-bs-toggle="modal" data-bs-target="#editPointsModal{{ $broker->id }}">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info fs-6">{{ number_format($broker->broker_interests_count) }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success fs-6">{{ number_format($broker->successful_codes) }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-danger fs-6">{{ number_format($broker->broker_interests_sum_points_paid ?? 0) }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success fs-6">{{ number_format($broker->broker_interests_sum_points_earned ?? 0) }}</span>
                                                    </td>
                                                    <td>
                                                        @php
                                                            $successRate = $broker->broker_interests_count > 0 ? 
                                                                ($broker->successful_codes / $broker->broker_interests_count) * 100 : 0;
                                                        @endphp
                                                        <span class="badge bg-{{ $successRate > 70 ? 'success' : ($successRate > 40 ? 'warning' : 'danger') }} fs-6">
                                                            {{ number_format($successRate, 1) }}%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        {{ $broker->created_at->format('Y-m-d') }}
                                                        <br>
                                                        <small class="text-muted">{{ $broker->created_at->diffForHumans() }}</small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.users.show', $broker->id) }}" 
                                                               class="btn btn-sm btn-info" target="_blank" title="عرض التفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{{ route('admin.broker-management.interests', ['broker_id' => $broker->id]) }}" 
                                                               class="btn btn-sm btn-primary" title="عرض الاهتمامات">
                                                                <i class="fas fa-heart"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>

                                                <!-- Modal لتعديل النقاط -->
                                                <div class="modal fade" id="editPointsModal{{ $broker->id }}" tabindex="-1">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <form method="POST" action="{{ route('admin.broker-management.update-points', $broker) }}">
                                                                @csrf
                                                                @method('PUT')
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">تعديل نقاط {{ $broker->name }}</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">النقاط الحالية</label>
                                                                        <input type="text" class="form-control" value="{{ number_format($broker->points) }}" readonly>
                                                                    </div>
                                                                    <div class="mb-3">
                                                                        <label class="form-label">النقاط الجديدة <span class="text-danger">*</span></label>
                                                                        <input type="number" name="points" class="form-control" 
                                                                               value="{{ $broker->points }}" min="0" required>
                                                                    </div>
                                                                    <div class="mb-3">
                                                                        <label class="form-label">سبب التعديل <span class="text-danger">*</span></label>
                                                                        <textarea name="reason" class="form-control" rows="3" 
                                                                                  placeholder="اذكر سبب تعديل النقاط..." required></textarea>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                    <button type="submit" class="btn btn-primary">حفظ التعديل</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- التصفح -->
                                <div class="mt-3">
                                    {{ $brokers->withQueryString()->links() }}
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مندوبين</h5>
                                    <p class="text-muted">لم يتم العثور على أي مندوبين يطابقون معايير البحث</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
