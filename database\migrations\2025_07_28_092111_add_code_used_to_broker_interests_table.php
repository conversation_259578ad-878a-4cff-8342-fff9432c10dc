<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('broker_interests', function (Blueprint $table) {
            // إضافة عمود لتتبع إذا كان الكود تم استخدامه لهذا الإعلان
            $table->boolean('code_used_for_ad')->default(false)->after('code_entered_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('broker_interests', function (Blueprint $table) {
            $table->dropColumn('code_used_for_ad');
        });
    }
};
