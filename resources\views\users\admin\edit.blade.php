@extends('partials.admin')
@section('title', 'Admin Users Edit')

@php
    use Illuminate\Support\Facades\Storage;
@endphp

@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'users.edit'])

<div class="main-content app-content mt-0">
    <div class="side-app">

        <!-- CONTAINER -->
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'User', 'hasBack' => true, 'backTitle' =>
            'All Users', 'backUrl' => route('admin.users.index')])

            <div class="row">
                <div class="col-12 col-sm-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title mb-0">All User Edit</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض رسائل النجاح والخطأ -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-xl-4">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">Password</div>
                        </div>
                        <div class="card-body">
                            <div class="text-center chat-image mb-5">
                                <div class="avatar avatar-xxl chat-profile mb-3 brround">
                                    <a class="" href="#"><img alt="avatar" src="{{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }}"
                                            class="brround"></a>
                                </div>
                                <div class="main-chat-msg-name">
                                    <a href="#">
                                        <h5 class="mb-1 text-dark fw-semibold">{{$user->name}}</h5>
                                    </a>
                                    <p class="text-muted mt-0 mb-0 pt-0 fs-13">{{'@'.$user->username}}</p>
                                </div>

                                <!-- Avatar Upload Section -->
                                <div class="mt-3">
                                    <label class="form-label">تغيير صورة المستخدم</label>
                                    <input type="file" name="avatar" class="form-control" accept="image/jpeg,image/png,image/jpg,image/gif,image/webp">
                                    <small class="form-text text-muted">الحد الأقصى: 5MB. الصيغ المدعومة: JPG, PNG, GIF, WebP</small>
                                    @if($user->is_broker)
                                        <div class="alert alert-info mt-2">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>ملاحظة:</strong> هذا المستخدم مندوب ولا يمكنه تغيير صورته بنفسه. يمكن للإدارة فقط تغيير صورته.
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <div class="d-flex flex-column gap-2">
                                <a href="{{ route('admin.users.request-password-reset', $user->id) }}"
                                    class="btn btn-primary">
                                    <i class="fas fa-key me-2"></i>Request Password Change
                                </a>
                                <a href="{{ route('admin.impersonate', $user->id) }}"
                                    class="btn btn-warning"
                                    onclick="return confirm('Are you sure you want to login as {{ $user->name }}? You will be redirected to their dashboard.')">
                                    <i class="fas fa-user-secret me-2"></i>Login as {{ $user->name }}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card panel-theme">
                        <div class="card-header">
                            <div class="float-start">
                                <h3 class="card-title">Contact</h3>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="card-body no-padding">
                            <ul class="list-group no-margin">
                                <li class="list-group-item d-flex ps-3">
                                    <div class="social social-profile-buttons me-2">
                                        <a class="social-icon text-primary" href="javascript:void(0)"><i
                                                class="fa-regular fa-envelope"></i></a>
                                    </div>
                                    <a href="{{'mailto:'.$user->email}}" class="my-auto">{{$user->email}}</a>
                                </li>
                                <li class="list-group-item d-flex ps-3">
                                    <div class="social social-profile-buttons me-2">
                                        <a class="social-icon text-primary" href="javascript:void(0)"><i
                                                class="fa-regular fa-globe"></i></a>
                                    </div>
                                    <a href="https://maps.google.com?q={{$user->address}}"
                                        class="my-auto">{{$user->address}}
                                </li>
                                <li class="list-group-item d-flex ps-3">
                                    <div class="social social-profile-buttons me-2">
                                        <a class="social-icon text-primary" href="javascript:void(0)"><i
                                                class="fa-regular fa-phone"></i></a>
                                    </div>
                                    <a href="{{'tel:'.$user->mobile}}" class="my-auto">{{$user->mobile}}</a>
                                </li>
                                @if($user->national_id)
                                <li class="list-group-item d-flex ps-3">
                                    <div class="social social-profile-buttons me-2">
                                        <a class="social-icon text-primary" href="javascript:void(0)"><i
                                                class="fas fa-id-card"></i></a>
                                    </div>
                                    <span class="my-auto">{{$user->national_id}}</span>
                                    @if($user->identityVerification)
                                        <a href="{{ route('admin.identity-verifications.show', $user->identityVerification->id) }}"
                                           class="btn btn-sm btn-info ms-2" target="_blank">
                                            <i class="fas fa-eye"></i> عرض الهوية
                                        </a>
                                    @endif
                                </li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-xl-8">
                    <form class="card" action="{{route('admin.users.update', $user->id)}}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="card-header">
                            <h3 class="card-title">Edit Profile</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-12">
                                    <div class="form-group">
                                        <label for="exampleInputname">First Name</label>
                                        <input type="text" name="first_name" class="form-control" id="exampleInputname"
                                            placeholder="First Name" value="{{$user->first_name}}">
                                    </div>
                                    <span class="text-danger">{{$errors->first('first_name')}}</span>
                                </div>
                                <div class="col-lg-6 col-md-12">
                                    <div class="form-group">
                                        <label for="exampleInputname1">Last Name</label>
                                        <input type="text" class="form-control" id="exampleInputname1" name="last_name"
                                            placeholder="Enter Last Name" value="{{$user->last_name}}">
                                    </div>
                                    <span class="text-danger">{{$errors->first('last_name')}}</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1">Username</label>
                                <input type="text" class="form-control" id="exampleInputEmail1" placeholder="Username" name="username"  
                                    value="{{$user->username}}">
                            </div>
                            <div class="form-group">
                                <label for="exampleInputEmail1">Email address</label>
                                <input type="email" class="form-control" id="exampleInputEmail1" name="email"  
                                    placeholder="Email address" value="{{$user->email}}"  >
                            </div>
                            <div class="form-group">
                                <label for="exampleInputnumber">Contact Number</label>
                                <input type="text" class="form-control" id="exampleInputnumber" name="mobile"
                                    placeholder="Contact number" value="{{$user->mobile}}"  >
                            </div>
                            <div class="form-group">
                                <label for="national_id">الرقم القومي</label>
                                <input type="text" class="form-control" id="national_id" name="national_id"
                                    placeholder="الرقم القومي" value="{{$user->national_id}}" maxlength="20">
                                @if($errors->has('national_id'))
                                    <span class="text-danger">{{$errors->first('national_id')}}</span>
                                @endif
                                @if($user->national_id && $user->identityVerification && $user->identityVerification->status === 'approved')
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>
                                        الهوية موثقة
                                    </small>
                                @endif
                            </div>
                            <div class="form-group">
                                <label class="form-label">Address</label>
                                <input class="form-control" placeholder="Home Address" value="{{$user->address}}" name="address">
                                <span class="text-danger">{{$errors->first('address')}}</span>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Gender</label>
                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                                <select class="form-control select2 form-select" name="gender"
                                                    <option>All</option>
                                                    @foreach (\App\Enums\Gender::all() as $gender)
                                                    <option value="{{$gender}}" @selected(true ? $user->gender ===
                                                        $gender : false)>{{$gender->label()}}</option>
                                                    @endforeach
                                                </select>
                                                <span class="text-danger">{{$errors->first('gender')}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Zip Code</label>
                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                                <input type="text" class="form-control" id="exampleInputnumber" name="zip_code"
                                                    placeholder="Zip Code" value="{{$user->zip_code}}">
                                                <span class="text-danger">{{$errors->first('zip_code')}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                


                                <div class="row">
                                    <!-- Seller Rank -->
                                    <div class="col-md-6 mb-2">
                                        <label for="rank">Seller Rank:</label>
                                        <select class="form-control select2 form-select" name="rank" id="rank">
                                            <option value="0" @selected($user->rank == 0)>Regular Seller</option>
                                            <option value="1" @selected($user->rank == 1)>VIP Seller</option>
                                            <option value="2" @selected($user->rank == 2)>Trader Seller</option>
                                            <option value="3" @selected($user->rank == 3)>Company Seller</option>
                                            <option value="4" @selected($user->rank == 4)>Admin Seller</option>
                                        </select>
                                        <span class="text-danger">{{$errors->first('rank')}}</span>
                                    </div>
                                
                                    <!-- Trusted Status -->
                                    <div class="col-md-6 mb-2">
                                        <label for="is_trusted">Trust Status:</label>
                                        <select class="form-control select2 form-select" name="is_trusted" id="is_trusted">
                                            <option value="1" @selected($user->is_trusted == 1)>المستخدم موثق الهوية ✅</option>
                                            <option value="0" @selected($user->is_trusted == 0)>المستخدم غير موثق الهوية ❌</option>
                                        </select>
                                        <span class="text-danger">{{$errors->first('is_trusted')}}</span>

                                        @if($user->identityVerification)
                                            <div class="mt-2">
                                                <a href="{{ route('admin.identity-verifications.show', $user->identityVerification) }}"
                                                   class="btn btn-sm btn-info">
                                                    <i class="fas fa-id-card me-1"></i>
                                                    View Identity Verification
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                
                                    <!-- Seller Level (Based on Ads Number_Ads) -->
                                    <div class="col-md-6 mb-2">
                                        <label for="Number_Ads">Number of Ads:</label>
                                        <input type="number" class="form-control" name="Number_Ads" id="Number_Ads"
                                            value="{{ $user->Number_Ads }}" min="1" max="50000" step="1" required>
                                        <span class="text-danger">{{$errors->first('Number_Ads')}}</span>
                                    </div>
                                    
                                    
                                </div>

                                



                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Time Zone</label>
                                        <div class="row">
                                            <div class="col-md-12 mb-2">
                                            @if($user == null)
                                                <input type="text" class="form-control" id="exampleInputnumber"
                                                    placeholder="Time Zone" value="{{$user->timezone->name}}" readonly disabled>
                                            </div>
                                        </div>
                                    </div>
                                            @else
                                                <p>User not found.</p>
                                            @endif
                                </div>
                                <div class="col-md-6">
                                    <x-location-selectable :selected-country="$user->country" />
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Active Status</label>
                                    <div class="row">
                                        <div class="col-md-12 mb-2">
                                            <select class="form-control select2 form-select" name="is_active">
                                                <option value="1" @selected(true ? $user->is_active : false)>Active</option>
                                                <option value="0" @selected(true ? !$user->is_active : false)>Ban</option>
                                            </select>
                                            <span class="text-danger">{{$errors->first('is_active')}}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Broker Status -->
                                <div class="form-group">
                                    <label class="form-label">حالة المندوب</label>
                                    <div class="row">
                                        <div class="col-md-12 mb-2">
                                            <select class="form-control select2 form-select" name="is_broker">
                                                <option value="0" @selected(!$user->is_broker)>شخص عادي</option>
                                                <option value="1" @selected($user->is_broker)>مندوب</option>
                                            </select>
                                            <span class="text-danger">{{$errors->first('is_broker')}}</span>
                                            <small class="form-text text-muted">
                                                يمكنك تحويل المستخدم من مندوب إلى شخص عادي أو العكس
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- User Points -->
                                <div class="form-group">
                                    <label class="form-label">النقاط</label>
                                    <div class="row">
                                        <div class="col-md-8 mb-2">
                                            <input type="number" class="form-control" name="points"
                                                   value="{{ $user->points ?? 0 }}" min="0" step="1">
                                            <span class="text-danger">{{$errors->first('points')}}</span>
                                            <small class="form-text text-muted">
                                                النقاط الحالية للمستخدم (للمندوبين فقط)
                                            </small>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <button type="button" class="btn btn-outline-info btn-sm w-100"
                                                    data-bs-toggle="modal" data-bs-target="#pointsHistoryModal">
                                                <i class="fas fa-history me-2"></i>
                                                تاريخ النقاط
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button type="submit" class="btn btn-success my-1">Save</button>
                            <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-danger my-1">Cancel</a>

                            <!-- رابط المستندات للمندوبين -->
                            @if($user->is_broker && $user->brokerApplication)
                                <a href="{{ route('admin.broker-applications.show', $user->brokerApplication->id) }}"
                                   class="btn btn-outline-success my-1">
                                    <i class="fas fa-file-alt me-2"></i>
                                    عرض مستندات المندوب
                                </a>
                            @endif

                            <!-- أدوات إدارة المندوب -->
                            @if($user->is_broker)
                                <div class="broker-management-section mt-3">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-user-cog me-2"></i>
                                        إدارة المندوب
                                    </h6>

                                    @php
                                        $isActiveBroker = false;
                                        $daysUntilInactive = 0;
                                        $isNewBroker = false;
                                        $statusMessage = '';

                                        if ($user->last_successful_code_at) {
                                            $daysSinceLastCode = $user->last_successful_code_at->diffInDays(now());
                                            $daysUntilInactive = max(0, 30 - $daysSinceLastCode);
                                            $isActiveBroker = $daysSinceLastCode < 30;

                                            if ($isActiveBroker) {
                                                $statusMessage = "آخر كود ناجح: " . $user->last_successful_code_at->format('d/m/Y H:i') . " - متبقي {$daysUntilInactive} يوم";
                                            } else {
                                                $statusMessage = "آخر كود ناجح: " . $user->last_successful_code_at->format('d/m/Y H:i') . " - انتهت صلاحية النشاط";
                                            }
                                        } else {
                                            $brokerActivationDate = $user->broker_activated_at ?? $user->updated_at;
                                            if ($brokerActivationDate) {
                                                $daysSinceBecameBroker = $brokerActivationDate->diffInDays(now());

                                                if ($daysSinceBecameBroker <= 30) {
                                                    $isActiveBroker = true;
                                                    $isNewBroker = true;
                                                    $daysUntilInactive = max(0, 30 - $daysSinceBecameBroker);
                                                    $statusMessage = "مندوب جديد منذ: " . $brokerActivationDate->format('d/m/Y H:i') . " - متبقي {$daysUntilInactive} يوم من فترة السماح";
                                                } else {
                                                    $statusMessage = "مندوب منذ: " . $brokerActivationDate->format('d/m/Y H:i') . " - لم يحقق أي كود ناجح";
                                                }
                                            } else {
                                                $statusMessage = "لا يوجد تاريخ تفعيل مندوب";
                                            }
                                        }
                                    @endphp

                                    <div class="broker-status-info mb-3 p-3 rounded" style="background-color: {{ $isActiveBroker ? '#d4edda' : '#f8d7da' }};">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div>
                                                <strong class="text-{{ $isActiveBroker ? 'success' : 'danger' }}">
                                                    <i class="fas fa-{{ $isActiveBroker ? 'check-circle' : 'times-circle' }} me-2"></i>
                                                    {{ $isActiveBroker ? 'مندوب نشط' : 'مندوب غير نشط' }}
                                                </strong>
                                                <div class="text-muted mt-1">
                                                    <small>{{ $statusMessage }}</small>
                                                </div>
                                         </form>
                                                <!-- معلومات إضافية للتشخيص -->
                                                <div class="mt-2">
                                                    <small class="text-info">
                                                        <strong>تشخيص:</strong>
                                                        @if($user->last_successful_code_at)
                                                            آخر كود ناجح موجود
                                                        @else
                                                            لا يوجد كود ناجح
                                                        @endif
                                                        |
                                                        @if($user->broker_activated_at)
                                                            تاريخ التفعيل: {{ $user->broker_activated_at->format('d/m/Y') }}
                                                        @else
                                                            لا يوجد تاريخ تفعيل
                                                        @endif
                                                    </small>
                                                </div>
                                            </div>
                                            <div>
                                                @if($user->is_broker)
                                                    @if($isActiveBroker)
                                                        <form action="{{ route('admin.users.deactivate-broker', $user->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            <button type="submit" class="btn btn-sm btn-danger"
                                                                    onclick="return confirm('هل أنت متأكد من إلغاء تنشيط هذا المندوب؟\n\nسيتم وضع تاريخ قديم في last_successful_code_at')">
                                                                <i class="fas fa-user-times me-1"></i>
                                                                إلغاء التنشيط
                                                            </button>
                                                        </form>
                                                    @else
                                                
                                                        <form action="{{ route('admin.users.activate-broker', $user->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            <button type="submit" class="btn btn-sm btn-success"
                                                                    onclick="return confirm('هل أنت متأكد من تنشيط هذا المندوب؟\n\nسيتم وضع التاريخ الحالي في last_successful_code_at ويحصل على 30 يوم نشاط جديد.')">
                                                                <i class="fas fa-user-check me-1"></i>
                                                                تنشيط المندوب
                                                            </button>
                                                        </form>
                                                    @endif
                                                @else
                                                    <span class="text-muted">
                                                        <small>ليس مندوب - استخدم النموذج أعلاه لتحويله إلى مندوب</small>
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <div class="broker-dates-info">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar-plus me-1"></i>
                                                    <strong>تاريخ التفعيل:</strong>
                                                    {{ $user->broker_activated_at ? $user->broker_activated_at->format('Y-m-d H:i') : 'غير محدد' }}
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar-check me-1"></i>
                                                    <strong>آخر كود ناجح:</strong>
                                                    {{ $user->last_successful_code_at ? $user->last_successful_code_at->format('Y-m-d H:i') : 'لم يدخل أكواد بعد' }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    
                    <form class="card" action="{{route('admin.users.destroy', $user->id)}}" method="POST">
                        @csrf
                        @method('DELETE')
                        <div class="card-header">
                            <div class="card-title">Delete Account</div>
                        </div>
                        <div class="card-body">
                            <p>Its Advisable for you to disable this account instead of deleting it. Use the button "Active
                                Status" to disable this account.</p>
                            <label class="custom-control custom-checkbox mb-0">
                                <input type="checkbox" class="custom-control-input" name="example-checkbox1"
                                    value="option1" checked>
                                <span class="custom-control-label">Yes, Send my data to my Email.</span>
                            </label>
                        </div>
                        <div class="card-footer text-end">
                            <button type="submit" class="btn btn-danger my-1">Delete Account</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- User Warnings Section -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">User Warnings</h3>
                            <button type="button" class="btn btn-warning btn-sm" onclick="showUserWarningModal()">
                                <i class="fas fa-exclamation-triangle me-2"></i>Issue Warning
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="warningsContainer">
                                <div class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                                    <p class="text-muted mt-2">Loading warnings...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- CONTAINER END -->
    </div>
</div>


<!-- User Warning Modal -->
<div class="modal fade" id="userWarningModal" tabindex="-1" aria-labelledby="userWarningModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userWarningModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Issue Warning to {{ $user->name }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="userWarningForm">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="user_id" value="{{ $user->id }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userWarningType" class="form-label">Warning Type *</label>
                                <select class="form-select" id="userWarningType" name="type" required>
                                    <option value="">Select Warning Type</option>
                                    <option value="posting_ban">منع النشر</option>
                                    <option value="chat_ban">منع الشات</option>
                                    <option value="bidding_ban">منع المزايدة</option>
                                    <option value="comment_ban">منع التعليق</option>
                                    <option value="general_warning">تحذير عام</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userDurationDays" class="form-label">Duration (Days)</label>
                                <input type="number" class="form-control" id="userDurationDays" name="duration_days"
                                       min="1" max="365" placeholder="Leave empty for permanent">
                                <small class="form-text text-muted">Leave empty for permanent warning</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="userWarningTitle" class="form-label">Warning Title *</label>
                        <input type="text" class="form-control" id="userWarningTitle" name="title" required
                               placeholder="e.g., Inappropriate Content Posting">
                    </div>

                    <div class="mb-3">
                        <label for="userWarningReason" class="form-label">Reason *</label>
                        <textarea class="form-control" id="userWarningReason" name="reason" rows="3" required
                                  placeholder="Explain why this warning is being issued..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="userAdminNotes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="userAdminNotes" name="admin_notes" rows="2"
                                  placeholder="Internal notes for other admins..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>Issue Warning
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal لتاريخ النقاط -->
<div class="modal fade" id="pointsHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تاريخ نقاط {{ $user->name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4>{{ number_format($user->points ?? 0) }}</h4>
                                <p class="mb-0">النقاط الحالية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4>{{ number_format($user->brokerInterests()->sum('points_earned') ?? 0) }}</h4>
                                <p class="mb-0">النقاط المكتسبة</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if($user->is_broker)
                    <h6>آخر العمليات:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>النقاط</th>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($user->brokerInterests()->latest()->take(10)->get() as $interest)
                                    <tr>
                                        <td>
                                            @if($interest->code_entered)
                                                <span class="badge bg-success">مكتسبة</span>
                                            @else
                                                <span class="badge bg-danger">منفقة</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($interest->code_entered)
                                                +{{ number_format($interest->points_earned) }}
                                            @else
                                                -{{ number_format($interest->points_paid) }}
                                            @endif
                                        </td>
                                        <td>{{ $interest->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            @if($interest->code_entered)
                                                كود ناجح للإعلان #{{ $interest->ad_id }}
                                            @else
                                                اهتمام بالإعلان #{{ $interest->ad_id }}
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">لا توجد عمليات</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذا المستخدم ليس مندوباً، لذا لا يوجد تاريخ للنقاط
                    </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

@endsection
@push('scripts')
<script src="/plugin/select2/select2.full.min.js"></script>
<script src="/assets/js/select2.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadUserWarnings();
});

function showUserWarningModal() {
    const modal = new bootstrap.Modal(document.getElementById('userWarningModal'));
    modal.show();
}

function loadUserWarnings() {
    fetch('{{ route("admin.users.warnings", $user) }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayWarnings(data.warnings);
            } else {
                document.getElementById('warningsContainer').innerHTML =
                    '<div class="text-center py-3"><p class="text-muted">Failed to load warnings</p></div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('warningsContainer').innerHTML =
                '<div class="text-center py-3"><p class="text-muted">Error loading warnings</p></div>';
        });
}

function displayWarnings(warnings) {
    const container = document.getElementById('warningsContainer');

    if (warnings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-shield-check fa-3x text-success mb-3"></i>
                <h5>No Warnings</h5>
                <p class="text-muted">This user has no warnings issued.</p>
            </div>
        `;
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-bordered"><thead><tr>';
    html += '<th>Type</th><th>Title</th><th>Reason</th><th>Status</th><th>Expires</th><th>Issued</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    warnings.forEach(warning => {
        const isActive = warning.is_active && (!warning.expires_at || new Date(warning.expires_at) > new Date());
        const statusBadge = isActive ?
            '<span class="badge bg-danger">Active</span>' :
            '<span class="badge bg-secondary">Inactive</span>';

        const expiresText = warning.expires_at ?
            new Date(warning.expires_at).toLocaleDateString() :
            'Permanent';

        html += `
            <tr class="${isActive ? 'table-warning' : ''}">
                <td><span class="badge bg-info">${getTypeLabel(warning.type)}</span></td>
                <td>${warning.title}</td>
                <td>${warning.reason}</td>
                <td>${statusBadge}</td>
                <td>${expiresText}</td>
                <td>${new Date(warning.created_at).toLocaleDateString()}</td>
                <td>
                    ${isActive ?
                        `<button class="btn btn-sm btn-outline-success" onclick="deactivateWarning('${warning.id}')">
                            <i class="fas fa-check"></i> Deactivate
                        </button>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

function getTypeLabel(type) {
    const labels = {
        'posting_ban': 'منع النشر',
        'chat_ban': 'منع الشات',
        'bidding_ban': 'منع المزايدة',
        'comment_ban': 'منع التعليق',
        'general_warning': 'تحذير عام'
    };
    return labels[type] || type;
}

function deactivateWarning(warningId) {
    if (!confirm('Are you sure you want to deactivate this warning?')) {
        return;
    }

    fetch(`/admin/warnings/${warningId}/deactivate`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            loadUserWarnings(); // Reload warnings
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while deactivating warning.');
    });
}

// Handle user warning form submission
document.getElementById('userWarningForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Issuing Warning...';

    fetch('{{ route("admin.warnings.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('userWarningModal'));
            modal.hide();

            // Reset form and reload warnings
            this.reset();
            loadUserWarnings();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while issuing warning.');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.main-container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);

        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>

@endpush