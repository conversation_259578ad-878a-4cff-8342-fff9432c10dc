/**
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
*/
hr {
	height: 0; /* 1 */
	color: inherit; /* 2 */
}
/*
Text-level semantics
====================
*/
/**
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr[title] {
	-webkit-text-decoration: underline dotted;
	        text-decoration: underline dotted;
}
/**
Add the correct font weight in Edge and Safari.
*/
b,
strong {
	font-weight: bolder;
}
/**
1. Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
2. Correct the odd 'em' font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
	font-family:
		ui-monospace,
		SFMono-Regular,
		Consolas,
		'Liberation Mono',
		Menlo,
		monospace; /* 1 */
	font-size: 1em; /* 2 */
}
/**
Add the correct font size in all browsers.
*/
small {
	font-size: 80%;
}
/**
Prevent 'sub' and 'sup' elements from affecting the line height in all browsers.
*/
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sub {
	bottom: -0.25em;
}
sup {
	top: -0.5em;
}
/*
Tabular data
============
*/
/**
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
*/
table {
	text-indent: 0; /* 1 */
	border-color: inherit; /* 2 */
}

/**
Correct the inability to style clickable types in iOS and Safari.
*/
button,
[type='button'],
[type='reset'],
[type='submit'] {
	-webkit-appearance: button;
}
/**
Remove the inner border and padding in Firefox.
*/
::-moz-focus-inner {
	border-style: none;
	padding: 0;
}
/**
Restore the focus styles unset by the previous rule.
*/
:-moz-focusring {
	outline: 1px dotted ButtonText;
}
/**
Remove the additional ':invalid' styles in Firefox.
See: https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737
*/
:-moz-ui-invalid {
	box-shadow: none;
}
/**
Remove the padding so developers are not caught out when they zero out 'fieldset' elements in all browsers.
*/
legend {
	padding: 0;
}
/**
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
	vertical-align: baseline;
}
/**
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}
/**
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
	-webkit-appearance: textfield; /* 1 */
	outline-offset: -2px; /* 2 */
}
/**
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
	-webkit-appearance: none;
}
/**
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to 'inherit' in Safari.
*/
::-webkit-file-upload-button {
	-webkit-appearance: button; /* 1 */
	font: inherit; /* 2 */
}
/*
Interactive
===========
*/
/*
Add the correct display in Chrome and Safari.
*/
summary {
	display: list-item;
}

/*
 * Ensure horizontal rules are visible by default
 */
hr {
  border-top-width: 1px;
}

.pointer-events-auto{
	pointer-events: auto;
}

.pointer-events-none{
	pointer-events: none;
}

.relative{
	position: relative;
}

.absolute{
	position: absolute;
}

.fixed{
	position: fixed;
}

.inset-0{
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.left-full{
	left: 100%;
}

.top-0{
	top: 0;
}

.left-1\/2{
	left: 50%;
}

.right-full{
	right: 100%;
}

.z-10{
	z-index: 10;
}

.mx-auto{
	margin-left: auto;
	margin-right: auto;
}

.-mx-4{
	margin-left: -1rem;
	margin-right: -1rem;
}

.-ml-0\.5{
	margin-left: -0.125rem;
}

.mr-1\.5{
	margin-right: 0.375rem;
}

.-ml-0{
	margin-left: 0px;
}

.mr-1{
	margin-right: 0.25rem;
}

.mt-1{
	margin-top: 0.25rem;
}

.mt-3{
	margin-top: 0.75rem;
}

.mt-5{
	margin-top: 1.25rem;
}

.mt-12{
	margin-top: 3rem;
}

.mt-32{
	margin-top: 8rem;
}

.mt-10{
	margin-top: 2.5rem;
}

.ml-2{
	margin-left: 0.5rem;
}

.mt-2{
	margin-top: 0.5rem;
}

.mb-4{
	margin-bottom: 1rem;
}

.mt-4{
	margin-top: 1rem;
}

.ml-6{
	margin-left: 1.5rem;
}

.ml-4{
	margin-left: 1rem;
}

.mt-8{
	margin-top: 2rem;
}

.mt-6{
	margin-top: 1.5rem;
}

.mt-16{
	margin-top: 4rem;
}

.ml-3{
	margin-left: 0.75rem;
}

.ml-1{
	margin-left: 0.25rem;
}

.block{
	display: block;
}

.flex{
	display: flex;
}

.inline-flex{
	display: inline-flex;
}

.hidden{
	display: none;
}

.h-2{
	height: 0.5rem;
}

.h-4{
	height: 1rem;
}

.h-5{
	height: 1.25rem;
}

.h-48{
	height: 12rem;
}

.h-10{
	height: 2.5rem;
}

.h-6{
	height: 1.5rem;
}

.h-12{
	height: 3rem;
}

.h-screen{
	height: 100vh;
}

.h-8{
	height: 2rem;
}

.h-40{
	height: 10rem;
}

.w-2{
	width: 0.5rem;
}

.w-full{
	width: 100%;
}

.w-4{
	width: 1rem;
}

.w-5{
	width: 1.25rem;
}

.w-0{
	width: 0px;
}

.w-10{
	width: 2.5rem;
}

.w-6{
	width: 1.5rem;
}

.w-12{
	width: 3rem;
}

.w-8{
	width: 2rem;
}

.w-auto{
	width: auto;
}

.max-w-screen-xl{
	max-width: 1280px;
}

.max-w-xl{
	max-width: 36rem;
}

.max-w-md{
	max-width: 28rem;
}

.max-w-sm{
	max-width: 24rem;
}

.max-w-2xl{
	max-width: 42rem;
}

.flex-1{
	flex: 1 1 0%;
}

.flex-shrink-0{
	flex-shrink: 0;
}

.transform{
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.origin-top{
	transform-origin: top;
}

.-translate-x-1\/2{
	--tw-translate-x: -50%;
}

.-translate-y-10{
	--tw-translate-y: -2.5rem;
}

.-translate-y-8{
	--tw-translate-y: -2rem;
}

.translate-y-2{
	--tw-translate-y: 0.5rem;
}

.translate-y-0{
	--tw-translate-y: 0px;
}

.translate-x-1\/2{
	--tw-translate-x: 50%;
}

.translate-y-12{
	--tw-translate-y: 3rem;
}

.translate-y-16{
	--tw-translate-y: 4rem;
}

.scale-75{
	--tw-scale-x: .75;
	--tw-scale-y: .75;
}

@-webkit-keyframes bounce{
	0%, 100%{
		transform: translateY(-25%);
		-webkit-animation-timing-function: cubic-bezier(0.8,0,1,1);
		        animation-timing-function: cubic-bezier(0.8,0,1,1);
	}
	50%{
		transform: none;
		-webkit-animation-timing-function: cubic-bezier(0,0,0.2,1);
		        animation-timing-function: cubic-bezier(0,0,0.2,1);
	}
}

@keyframes bounce{
	0%, 100%{
		transform: translateY(-25%);
		-webkit-animation-timing-function: cubic-bezier(0.8,0,1,1);
		        animation-timing-function: cubic-bezier(0.8,0,1,1);
	}
	50%{
		transform: none;
		-webkit-animation-timing-function: cubic-bezier(0,0,0.2,1);
		        animation-timing-function: cubic-bezier(0,0,0.2,1);
	}
}

.animate-bounce{
	-webkit-animation: bounce 1s infinite;
	        animation: bounce 1s infinite;
}

.cursor-pointer{
	cursor: pointer;
}

.flex-col{
	flex-direction: column;
}

.items-center{
	align-items: center;
}

.items-start{
	align-items: flex-start;
}

.items-end{
	align-items: flex-end;
}

.justify-center{
	justify-content: center;
}

.justify-end{
	justify-content: flex-end;
}

.justify-between{
	justify-content: space-between;
}

.space-x-6 > :not([hidden]) ~ :not([hidden]){
	--tw-space-x-reverse: 0;
	margin-right: calc(1.5rem * var(--tw-space-x-reverse));
	margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]){
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]){
	--tw-space-y-reverse: 0;
	margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]){
	--tw-space-y-reverse: 0;
	margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]){
	--tw-space-x-reverse: 0;
	margin-right: calc(0.5rem * var(--tw-space-x-reverse));
	margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.overflow-hidden{
	overflow: hidden;
}

.rounded-full{
	border-radius: 9999px;
}

.rounded-md{
	border-radius: 0.375rem;
}

.rounded-lg{
	border-radius: 0.5rem;
}

.rounded{
	border-radius: 0.25rem;
}

.border{
	border-width: 1px;
}

.border-t-4{
	border-top-width: 4px;
}

.border-l-4{
	border-left-width: 4px;
}

.border-b{
	border-bottom-width: 1px;
}

.border-transparent{
	border-color: transparent;
}

.border-green-600{
	--tw-border-opacity: 1;
	border-color: rgba(5, 150, 105, var(--tw-border-opacity));
}

.border-red-600{
	--tw-border-opacity: 1;
	border-color: rgba(220, 38, 38, var(--tw-border-opacity));
}

.border-yellow-400{
	--tw-border-opacity: 1;
	border-color: rgba(251, 191, 36, var(--tw-border-opacity));
}

.border-blue-600{
	--tw-border-opacity: 1;
	border-color: rgba(37, 99, 235, var(--tw-border-opacity));
}

.border-gray-200{
	--tw-border-opacity: 1;
	border-color: rgba(229, 231, 235, var(--tw-border-opacity));
}

.bg-white{
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}

.bg-pink-100{
	--tw-bg-opacity: 1;
	background-color: rgba(252, 231, 243, var(--tw-bg-opacity));
}

.bg-pink-600{
	--tw-bg-opacity: 1;
	background-color: rgba(219, 39, 119, var(--tw-bg-opacity));
}

.bg-green-600{
	--tw-bg-opacity: 1;
	background-color: rgba(5, 150, 105, var(--tw-bg-opacity));
}

.bg-yellow-400{
	--tw-bg-opacity: 1;
	background-color: rgba(251, 191, 36, var(--tw-bg-opacity));
}

.bg-blue-600{
	--tw-bg-opacity: 1;
	background-color: rgba(37, 99, 235, var(--tw-bg-opacity));
}

.bg-red-600{
	--tw-bg-opacity: 1;
	background-color: rgba(220, 38, 38, var(--tw-bg-opacity));
}

.bg-green-500{
	--tw-bg-opacity: 1;
	background-color: rgba(16, 185, 129, var(--tw-bg-opacity));
}

.bg-red-500{
	--tw-bg-opacity: 1;
	background-color: rgba(239, 68, 68, var(--tw-bg-opacity));
}

.bg-gray-900{
	--tw-bg-opacity: 1;
	background-color: rgba(17, 24, 39, var(--tw-bg-opacity));
}

.bg-red-100{
	--tw-bg-opacity: 1;
	background-color: rgba(254, 226, 226, var(--tw-bg-opacity));
}

.bg-gray-800{
	--tw-bg-opacity: 1;
	background-color: rgba(31, 41, 55, var(--tw-bg-opacity));
}

.bg-gray-50{
	--tw-bg-opacity: 1;
	background-color: rgba(249, 250, 251, var(--tw-bg-opacity));
}

.bg-gradient-to-r{
	background-image: linear-gradient(to right, var(--gradient-color-stops));
}

.from-green-600{
	--tw-gradient-from: #059669;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(5, 150, 105, 0));
}

.from-red-600{
	--tw-gradient-from: #dc2626;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(220, 38, 38, 0));
}

.via-green-500{
	--tw-gradient-stops: var(--tw-gradient-from), #10b981, var(--tw-gradient-to, rgba(16, 185, 129, 0));
}

.via-red-500{
	--tw-gradient-stops: var(--tw-gradient-from), #ef4444, var(--tw-gradient-to, rgba(239, 68, 68, 0));
}

.to-green-800{
	--tw-gradient-to: #065f46;
}

.to-red-800{
	--tw-gradient-to: #991b1b;
}

.p-4{
	padding: 1rem;
}

.p-2{
	padding: 0.5rem;
}

.p-1{
	padding: 0.25rem;
}

.px-4{
	padding-left: 1rem;
	padding-right: 1rem;
}

.py-8{
	padding-top: 2rem;
	padding-bottom: 2rem;
}

.px-3{
	padding-left: 0.75rem;
	padding-right: 0.75rem;
}

.py-1{
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
}

.px-6{
	padding-left: 1.5rem;
	padding-right: 1.5rem;
}

.py-3{
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
}

.py-16{
	padding-top: 4rem;
	padding-bottom: 4rem;
}

.py-12{
	padding-top: 3rem;
	padding-bottom: 3rem;
}

.py-6{
	padding-top: 1.5rem;
	padding-bottom: 1.5rem;
}

.py-5{
	padding-top: 1.25rem;
	padding-bottom: 1.25rem;
}

.text-right{
	text-align: right;
}

.text-base{
	font-size: 1rem;
	line-height: 1.5rem;
}

.text-4xl{
	font-size: 2.25rem;
	line-height: 2.5rem;
}

.text-2xl{
	font-size: 1.5rem;
	line-height: 2rem;
}

.text-lg{
	font-size: 1.125rem;
	line-height: 1.75rem;
}

.text-sm{
	font-size: 0.875rem;
	line-height: 1.25rem;
}

.text-xl{
	font-size: 1.25rem;
	line-height: 1.75rem;
}

.text-3xl{
	font-size: 1.875rem;
	line-height: 2.25rem;
}

.font-medium{
	font-weight: 500;
}

.font-extrabold{
	font-weight: 800;
}

.font-semibold{
	font-weight: 600;
}

.capitalize{
	text-transform: capitalize;
}

.leading-5{
	line-height: 1.25rem;
}

.leading-10{
	line-height: 2.5rem;
}

.leading-8{
	line-height: 2rem;
}

.leading-7{
	line-height: 1.75rem;
}

.leading-6{
	line-height: 1.5rem;
}

.tracking-tight{
	letter-spacing: -0.025em;
}

.text-gray-200{
	--tw-text-opacity: 1;
	color: rgba(229, 231, 235, var(--tw-text-opacity));
}

.text-pink-800{
	--tw-text-opacity: 1;
	color: rgba(157, 23, 77, var(--tw-text-opacity));
}

.text-pink-400{
	--tw-text-opacity: 1;
	color: rgba(244, 114, 182, var(--tw-text-opacity));
}

.text-gray-900{
	--tw-text-opacity: 1;
	color: rgba(17, 24, 39, var(--tw-text-opacity));
}

.text-gray-600{
	--tw-text-opacity: 1;
	color: rgba(75, 85, 99, var(--tw-text-opacity));
}

.text-white{
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
}

.text-gray-50{
	--tw-text-opacity: 1;
	color: rgba(249, 250, 251, var(--tw-text-opacity));
}

.text-gray-500{
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}

.text-pink-600{
	--tw-text-opacity: 1;
	color: rgba(219, 39, 119, var(--tw-text-opacity));
}

.text-gray-700{
	--tw-text-opacity: 1;
	color: rgba(55, 65, 81, var(--tw-text-opacity));
}

.text-green-600{
	--tw-text-opacity: 1;
	color: rgba(5, 150, 105, var(--tw-text-opacity));
}

.text-gray-400{
	--tw-text-opacity: 1;
	color: rgba(156, 163, 175, var(--tw-text-opacity));
}

.text-red-600{
	--tw-text-opacity: 1;
	color: rgba(220, 38, 38, var(--tw-text-opacity));
}

.text-yellow-400{
	--tw-text-opacity: 1;
	color: rgba(251, 191, 36, var(--tw-text-opacity));
}

.text-blue-600{
	--tw-text-opacity: 1;
	color: rgba(37, 99, 235, var(--tw-text-opacity));
}

.text-gray-300{
	--tw-text-opacity: 1;
	color: rgba(209, 213, 219, var(--tw-text-opacity));
}

.text-red-800{
	--tw-text-opacity: 1;
	color: rgba(153, 27, 27, var(--tw-text-opacity));
}

.text-gray-100{
	--tw-text-opacity: 1;
	color: rgba(243, 244, 246, var(--tw-text-opacity));
}

.text-pink-500{
	--tw-text-opacity: 1;
	color: rgba(236, 72, 153, var(--tw-text-opacity));
}

.opacity-0{
	opacity: 0;
}

.opacity-100{
	opacity: 1;
}

.opacity-75{
	opacity: 0.75;
}

.shadow{
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg{
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.transition{
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}

.duration-150{
	transition-duration: 150ms;
}

.duration-300{
	transition-duration: 300ms;
}

.duration-100{
	transition-duration: 100ms;
}

.ease-in-out{
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out{
	transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ease-in{
	transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.hover\:bg-pink-500:hover{
	--tw-bg-opacity: 1;
	background-color: rgba(236, 72, 153, var(--tw-bg-opacity));
}

.hover\:text-gray-600:hover{
	--tw-text-opacity: 1;
	color: rgba(75, 85, 99, var(--tw-text-opacity));
}

.focus\:text-gray-500:focus{
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}

.focus\:text-gray-50:focus{
	--tw-text-opacity: 1;
	color: rgba(249, 250, 251, var(--tw-text-opacity));
}

.focus\:outline-none:focus{
	outline: 2px solid transparent;
	outline-offset: 2px;
}

@media (min-width: 640px){
	.sm\:mx-auto{
		margin-left: auto;
		margin-right: auto;
	}
	.sm\:mt-5{
		margin-top: 1.25rem;
	}
	.sm\:mt-8{
		margin-top: 2rem;
	}
	.sm\:flex{
		display: flex;
	}
	.sm\:max-w-lg{
		max-width: 32rem;
	}
	.sm\:translate-y-0{
		--tw-translate-y: 0px;
	}
	.sm\:translate-x-2{
		--tw-translate-x: 0.5rem;
	}
	.sm\:translate-x-0{
		--tw-translate-x: 0px;
	}
	.sm\:scale-100{
		--tw-scale-x: 1;
		--tw-scale-y: 1;
	}
	.sm\:items-start{
		align-items: flex-start;
	}
	.sm\:justify-center{
		justify-content: center;
	}
	.sm\:justify-end{
		justify-content: flex-end;
	}
	.sm\:p-6{
		padding: 1.5rem;
	}
	.sm\:py-12{
		padding-top: 3rem;
		padding-bottom: 3rem;
	}
	.sm\:px-6{
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
	.sm\:text-center{
		text-align: center;
	}
	.sm\:text-6xl{
		font-size: 3.75rem;
		line-height: 1;
	}
	.sm\:text-xl{
		font-size: 1.25rem;
		line-height: 1.75rem;
	}
	.sm\:text-3xl{
		font-size: 1.875rem;
		line-height: 2.25rem;
	}
	.sm\:text-4xl{
		font-size: 2.25rem;
		line-height: 2.5rem;
	}
	.sm\:leading-none{
		line-height: 1;
	}
	.sm\:leading-9{
		line-height: 2.25rem;
	}
	.sm\:leading-10{
		line-height: 2.5rem;
	}
}

@media (min-width: 768px){
	.md\:mx-auto{
		margin-left: auto;
		margin-right: auto;
	}
	.md\:mt-0{
		margin-top: 0px;
	}
	.md\:grid{
		display: grid;
	}
	.md\:max-w-2xl{
		max-width: 42rem;
	}
	.md\:grid-cols-3{
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}
	.md\:space-y-0 > :not([hidden]) ~ :not([hidden]){
		--tw-space-y-reverse: 0;
		margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
		margin-bottom: calc(0px * var(--tw-space-y-reverse));
	}
	.md\:py-20{
		padding-top: 5rem;
		padding-bottom: 5rem;
	}
	.md\:py-4{
		padding-top: 1rem;
		padding-bottom: 1rem;
	}
	.md\:px-10{
		padding-left: 2.5rem;
		padding-right: 2.5rem;
	}
	.md\:text-lg{
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
}

@media (min-width: 1024px){
	.lg\:col-span-6{
		grid-column: span 6 / span 6;
	}
	.lg\:col-start-2{
		grid-column-start: 2;
	}
	.lg\:col-start-1{
		grid-column-start: 1;
	}
	.lg\:mx-0{
		margin-left: 0px;
		margin-right: 0px;
	}
	.lg\:mx-auto{
		margin-left: auto;
		margin-right: auto;
	}
	.lg\:mt-0{
		margin-top: 0px;
	}
	.lg\:block{
		display: block;
	}
	.lg\:flex{
		display: flex;
	}
	.lg\:grid{
		display: grid;
	}
	.lg\:hidden{
		display: none;
	}
	.lg\:max-w-none{
		max-width: none;
	}
	.lg\:max-w-md{
		max-width: 28rem;
	}
	.lg\:max-w-screen-xl{
		max-width: 1280px;
	}
	.lg\:grid-flow-row-dense{
		grid-auto-flow: row dense;
	}
	.lg\:grid-cols-12{
		grid-template-columns: repeat(12, minmax(0, 1fr));
	}
	.lg\:grid-cols-2{
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	.lg\:items-center{
		align-items: center;
	}
	.lg\:items-start{
		align-items: flex-start;
	}
	.lg\:justify-start{
		justify-content: flex-start;
	}
	.lg\:gap-8{
		gap: 2rem;
	}
	.lg\:gap-12{
		gap: 3rem;
	}
	.lg\:px-8{
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.lg\:py-24{
		padding-top: 6rem;
		padding-bottom: 6rem;
	}
	.lg\:text-left{
		text-align: left;
	}
	.lg\:text-center{
		text-align: center;
	}
	.lg\:text-5xl{
		font-size: 3rem;
		line-height: 1;
	}
	.lg\:text-lg{
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
}

@media (min-width: 1280px){
	.xl\:py-24{
		padding-top: 6rem;
		padding-bottom: 6rem;
	}
	.xl\:text-6xl{
		font-size: 3.75rem;
		line-height: 1;
	}
	.xl\:text-xl{
		font-size: 1.25rem;
		line-height: 1.75rem;
	}
}

.notify{
	z-index: 9999;
}

.notify-button{
	background: none;
	border: none;
	outline: none;
}