@extends('partials.admin')
@section('title', 'الأكواد الناجحة للمندوبين')
@section('content')

@include('layouts.header', ['admin' => true])
@include('layouts.sidebar', ['admin' => true, 'active' => 'broker-management'])

<div class="main-content app-content mt-0">
    <div class="side-app">
        <div class="main-container container-fluid">
            @include('layouts.breadcrumb', ['admin' => true, 'pageTitle' => 'الأكواد الناجحة للمندوبين'])

            <!-- إحصائيات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">إجمالي الأكواد الناجحة</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_successful']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-key fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">النقاط المكتسبة</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['total_points_earned']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-coins fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">اليوم</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['today_successful']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-calendar-day fa-2x text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6">
                    <div class="card overflow-hidden">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="mt-2">
                                    <h6 class="">هذا الشهر</h6>
                                    <h2 class="mb-0 number-font">{{ number_format($stats['this_month_successful']) }}</h2>
                                </div>
                                <div class="ms-auto">
                                    <div class="chart-wrapper mt-1">
                                        <i class="fas fa-calendar-alt fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">فلترة الأكواد الناجحة</h3>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">المندوب</label>
                                    <select name="broker_id" class="form-select">
                                        <option value="">جميع المندوبين</option>
                                        @foreach($brokers as $broker)
                                            <option value="{{ $broker->id }}" {{ request('broker_id') == $broker->id ? 'selected' : '' }}>
                                                {{ $broker->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <a href="{{ route('admin.broker-management.successful-codes') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الأكواد الناجحة -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">الأكواد الناجحة ({{ $successfulCodes->total() }} كود)</h3>
                        </div>
                        <div class="card-body">
                            @if($successfulCodes->count())
                                <div class="table-responsive">
                                    <table class="table table-bordered text-nowrap border-bottom">
                                        <thead>
                                            <tr>
                                                <th>المندوب</th>
                                                <th>الإعلان</th>
                                                <th>صاحب الإعلان</th>
                                                <th>النقاط المدفوعة</th>
                                                <th>النقاط المكتسبة</th>
                                                <th>نسبة الاسترداد</th>
                                                <th>تاريخ إدخال الكود</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($successfulCodes as $code)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="flex-grow-1">
                                                                <strong>{{ $code->broker->name }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $code->broker->email }}</small>
                                                                <br>
                                                                <small class="text-info">{{ number_format($code->broker->points) }} نقطة</small>
                                                            </div>
                                                            <div class="ms-2">
                                                                <button type="button" class="btn btn-sm btn-warning"
                                                                        data-bs-toggle="modal" data-bs-target="#editPointsModal{{ $code->broker->id }}"
                                                                        title="تعديل النقاط">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            @if($code->ad)
                                                                <strong>{{ \Illuminate\Support\Str::limit($code->ad->title, 30) }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $code->ad->category->name ?? 'غير محدد' }}</small>
                                                                <br>
                                                                <small class="text-success">{{ number_format($code->ad->price) }} جنيه</small>
                                                            @else
                                                                <strong class="text-danger">إعلان محذوف</strong>
                                                                <br>
                                                                <small class="text-muted">الإعلان غير متاح</small>
                                                                <br>
                                                                <small class="text-muted">-</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            @if($code->ad && $code->ad->user)
                                                                <strong>{{ $code->ad->user->name }}</strong>
                                                                <br>
                                                                <small class="text-muted">{{ $code->ad->user->email }}</small>
                                                                @if($code->ad->user->is_trusted)
                                                                    <br>
                                                                    <span class="badge bg-primary">موثق</span>
                                                                @endif
                                                            @else
                                                                <strong class="text-danger">مستخدم محذوف</strong>
                                                                <br>
                                                                <small class="text-muted">المستخدم غير متاح</small>
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-danger fs-6">{{ number_format($code->points_paid) }} نقطة</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success fs-6">{{ number_format($code->points_earned) }} نقطة</span>
                                                    </td>
                                                    <td>
                                                        @php
                                                            $refundPercentage = $code->points_paid > 0 ? ($code->points_earned / $code->points_paid) * 100 : 0;
                                                        @endphp
                                                        <span class="badge {{ $refundPercentage == 100 ? 'bg-success' : 'bg-warning' }} fs-6">
                                                            {{ number_format($refundPercentage, 1) }}%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <strong>{{ $code->code_entered_at->format('Y-m-d H:i') }}</strong>
                                                        <br>
                                                        <small class="text-muted">{{ $code->code_entered_at->diffForHumans() }}</small>
                                                        <br>
                                                        @php
                                                            $daysSinceEntry = $code->code_entered_at->diffInDays(now());
                                                        @endphp
                                                        <small class="text-info">منذ {{ $daysSinceEntry }} يوم</small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            @if($code->ad)
                                                                <a href="{{ route('admin.ads.show', $code->ad->slug) }}"
                                                                   class="btn btn-sm btn-info" target="_blank" title="عرض الإعلان">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            @else
                                                                <span class="btn btn-sm btn-secondary disabled" title="الإعلان محذوف">
                                                                    <i class="fas fa-eye-slash"></i>
                                                                </span>
                                                            @endif
                                                            <a href="{{ route('admin.users.show', $code->broker->id) }}"
                                                               class="btn btn-sm btn-primary" target="_blank" title="عرض المندوب">
                                                                <i class="fas fa-user"></i>
                                                            </a>
                                                            @if($code->ad && $code->ad->user)
                                                                <a href="{{ route('admin.users.show', $code->ad->user->id) }}"
                                                                   class="btn btn-sm btn-secondary" target="_blank" title="عرض صاحب الإعلان">
                                                                    <i class="fas fa-user-tie"></i>
                                                                </a>
                                                            @else
                                                                <span class="btn btn-sm btn-secondary disabled" title="صاحب الإعلان محذوف">
                                                                    <i class="fas fa-user-slash"></i>
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- التصفح -->
                                <div class="mt-3">
                                    {{ $successfulCodes->withQueryString()->links() }}
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-key fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد أكواد ناجحة</h5>
                                    <p class="text-muted">لم يتم العثور على أي أكواد ناجحة تطابق معايير البحث</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals لتعديل النقاط -->
@foreach($successfulCodes->unique('broker_id') as $code)
    <div class="modal fade" id="editPointsModal{{ $code->broker->id }}" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="{{ route('admin.broker-management.update-points', $code->broker) }}">
                    @csrf
                    @method('PUT')
                    <div class="modal-header">
                        <h5 class="modal-title">تعديل نقاط {{ $code->broker->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">النقاط الحالية</label>
                            <input type="text" class="form-control" value="{{ number_format($code->broker->points) }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">النقاط الجديدة <span class="text-danger">*</span></label>
                            <input type="number" name="points" class="form-control"
                                   value="{{ $code->broker->points }}" min="0" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سبب التعديل <span class="text-danger">*</span></label>
                            <textarea name="reason" class="form-control" rows="3"
                                      placeholder="اذكر سبب تعديل النقاط..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التعديل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach

@endsection
