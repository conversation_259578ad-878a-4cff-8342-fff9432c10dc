<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->uuid('reviewer_id'); // المستخدم الذي يقيم
            $table->uuid('ad_id'); // الإعلان المُقيم
            $table->decimal('rating', 2, 1); // التقييم من 1.0 إلى 5.0
            $table->text('comment')->nullable(); // التعليق (اختياري)
            $table->timestamps();

            // فهارس للأداء
            $table->index(['ad_id', 'created_at']);
            $table->index('reviewer_id');

            // منع التقييم المتكرر من نفس المستخدم لنفس المنتج
            $table->unique(['reviewer_id', 'ad_id']);

            // العلاقات الخارجية
            $table->foreign('reviewer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('ad_id')->references('id')->on('ads')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_reviews');
    }
};
