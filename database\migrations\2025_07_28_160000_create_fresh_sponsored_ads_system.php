<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إنشاء جدول الإعلانات الممولة الجديد
        Schema::create('sponsored_ads', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('ad_id')->constrained('ads')->onDelete('cascade');
            $table->decimal('cost', 10, 2); // تكلفة الرعاية
            $table->integer('total_minutes'); // إجمالي الدقائق المشتراة (cost / 100 * 60)
            $table->timestamp('started_at')->nullable(); // وقت بداية الرعاية (عند موافقة الأدمن)
            $table->timestamp('expires_at')->nullable(); // وقت انتهاء الرعاية (started_at + total_minutes)
            $table->boolean('is_active')->default(false); // هل الرعاية نشطة
            $table->enum('status', ['pending', 'active', 'expired', 'cancelled'])->default('pending');
            $table->timestamps();

            // فهارس للبحث السريع
            $table->index(['is_active', 'status']);
            $table->index(['expires_at']);
            $table->index(['ad_id']);
        });

        // إضافة عمود notes لجدول الإعلانات
        Schema::table('ads', function (Blueprint $table) {
            $table->text('notes')->nullable()->after('description');
        });

        // حذف الأعمدة القديمة من جدول الإعلانات (إذا كانت موجودة)
        Schema::table('ads', function (Blueprint $table) {
            // التحقق من وجود الأعمدة قبل حذفها
            if (Schema::hasColumn('ads', 'is_sponsored')) {
                $table->dropColumn('is_sponsored');
            }
            if (Schema::hasColumn('ads', 'sponsorship_cost')) {
                $table->dropColumn('sponsorship_cost');
            }
            if (Schema::hasColumn('ads', 'sponsored_minutes')) {
                $table->dropColumn('sponsored_minutes');
            }
            if (Schema::hasColumn('ads', 'sponsored_end_at')) {
                $table->dropColumn('sponsored_end_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة الأعمدة القديمة
        Schema::table('ads', function (Blueprint $table) {
            $table->boolean('is_sponsored')->default(false);
            $table->decimal('sponsorship_cost', 8, 2)->nullable();
            $table->integer('sponsored_minutes')->default(0);
            $table->timestamp('sponsored_end_at')->nullable();
        });

        // حذف عمود notes
        Schema::table('ads', function (Blueprint $table) {
            $table->dropColumn('notes');
        });

        // حذف جدول الإعلانات الممولة
        Schema::dropIfExists('sponsored_ads');
    }
};
