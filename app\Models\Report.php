<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Traits\HasUuid;

class Report extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'reporter_id',
        'reportable_type',
        'reportable_id',
        'reason',
        'description',
        'status',
        'reviewed_by',
        'admin_notes',
        'reviewed_at',
        'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'reviewed_at' => 'datetime'
    ];

    // Report types constants
    const TYPE_AD = 'App\Models\Ad';
    const TYPE_COMMENT = 'App\Models\Comment';
    const TYPE_USER = 'App\Models\User';
    const TYPE_REVIEW = 'App\Models\ProductReview';
    const TYPE_USER_RATING = 'App\Models\UserRating';
    const TYPE_MESSAGE = 'App\Models\ChMessage';

    // Report reasons
    const REASONS = [
        'spam' => 'محتوى مزعج أو غير مرغوب فيه',
        'inappropriate' => 'محتوى غير لائق',
        'fake' => 'محتوى مزيف أو مضلل',
        'harassment' => 'تحرش أو إساءة',
        'violence' => 'عنف أو تهديد',
        'copyright' => 'انتهاك حقوق الطبع والنشر',
        'fraud' => 'احتيال أو نصب',
        'other' => 'أخرى'
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_REVIEWED = 'reviewed';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_DISMISSED = 'dismissed';

    /**
     * Get the user who made the report.
     */
    public function reporter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reporter_id');
    }

    /**
     * Get the admin who reviewed the report.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'reviewed_by');
    }

    /**
     * Get the reportable model (Ad, Comment, User, etc.).
     */
    public function reportable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the reason label.
     */
    public function getReasonLabelAttribute(): string
    {
        return self::REASONS[$this->reason] ?? $this->reason;
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_REVIEWED => 'تم المراجعة',
            self::STATUS_RESOLVED => 'تم الحل',
            self::STATUS_DISMISSED => 'تم الرفض',
            default => $this->status
        };
    }

    /**
     * Get the reportable type label.
     */
    public function getTypeLabel(): string
    {
        return match($this->reportable_type) {
            self::TYPE_AD => 'إعلان',
            self::TYPE_COMMENT => 'تعليق',
            self::TYPE_USER => 'مستخدم',
            self::TYPE_REVIEW => 'تقييم',
            self::TYPE_USER_RATING => 'تقييم مستخدم',
            self::TYPE_MESSAGE => 'رسالة',
            default => 'غير محدد'
        };
    }

    /**
     * Scope for pending reports.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for reports by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('reportable_type', $type);
    }
}
