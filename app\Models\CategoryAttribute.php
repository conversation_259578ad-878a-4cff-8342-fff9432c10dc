<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CategoryAttribute extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'attribute_name',
        'attribute_label',
        'attribute_type',
        'attribute_options',
        'sort_order',
        'is_required'
    ];

    protected $casts = [
        'attribute_options' => 'array',
        'is_required' => 'boolean'
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
