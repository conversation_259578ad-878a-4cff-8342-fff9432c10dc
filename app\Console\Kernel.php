<?php

namespace App\Console;

use App\Jobs\ProcessDailyPayouts;
use App\Jobs\ProcessHighestBidder;
use App\Models\Ad;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Delete expired ads (older than 30 days) with their images and reviews
        $schedule->command('ads:delete-expired')
                 ->daily()
                 ->withoutOverlapping()
                 ->runInBackground();

        $schedule->command('messages:delete-old')->daily();
        $schedule->job(new ProcessDailyPayouts)->dailyAt('00:00');
        $schedule->job(new ProcessHighestBidder)->hourly();
        $schedule->command('ads:manage-sponsored')->everyMinute();
        $schedule->command('broker:expire-interests')->daily();

        // تنظيف الإعلانات الممولة المنتهية كل ساعة
        $schedule->command('sponsored-ads:expire')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
