@extends('partials.app')

@section('title', 'التقديم كمندوب')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        التقديم كمندوب
                    </h2>
                    <p class="mb-0 mt-2">انضم إلى فريق المندوبين واحصل على فرص عمل مميزة</p>
                </div>

                <div class="card-body p-5">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($existingApplication)
                        @if($existingApplication->status === 'pending')
                            <div class="alert alert-info text-center">
                                <i class="fas fa-clock me-2"></i>
                                <h5>طلبك قيد المراجعة</h5>
                                <p class="mb-0">تم إرسال طلبك بتاريخ {{ $existingApplication->created_at->format('Y-m-d') }} وهو الآن قيد المراجعة من قبل الإدارة.</p>
                            </div>
                        @elseif($existingApplication->status === 'first_stage_accepted')
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle me-2"></i>
                                <h5>تم قبولك في المرحلة الأولى!</h5>
                                <p class="mb-3">تهانينا! تم قبول طلبك في المرحلة الأولى. الآن يجب عليك رفع المستندات المطلوبة.</p>
                                <a href="{{ route('broker-application.documents') }}" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>
                                    رفع المستندات
                                </a>
                            </div>
                        @elseif($existingApplication->status === 'second_stage_accepted')
                            <div class="alert alert-primary text-center">
                                <i class="fas fa-hourglass-half me-2"></i>
                                <h5>مستنداتك قيد المراجعة</h5>
                                <p class="mb-0">تم رفع مستنداتك بنجاح وهي الآن قيد المراجعة النهائية من قبل الإدارة.</p>
                            </div>
                        @elseif($existingApplication->status === 'rejected')
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <h5>تم رفض طلبك</h5>
                                <p><strong>سبب الرفض:</strong> {{ $existingApplication->rejection_reason }}</p>
                                
                                @if($existingApplication->canReapply())
                                    <p class="text-success">
                                        <i class="fas fa-info-circle me-2"></i>
                                        يمكنك التقديم مرة أخرى الآن
                                    </p>
                                @else
                                    <p class="text-warning">
                                        <i class="fas fa-clock me-2"></i>
                                        يمكنك التقديم مرة أخرى بعد {{ $existingApplication->daysUntilReapply() }} يوم
                                    </p>
                                @endif
                            </div>
                        @endif
                    @endif

                    @if(!$existingApplication || $existingApplication->canReapply())
                        <!-- نموذج التقديم -->
                        <form method="POST" action="{{ route('broker-application.store') }}">
                            @csrf
                            
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <label for="full_name" class="form-label fw-bold">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        الاسم الكامل *
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-lg @error('full_name') is-invalid @enderror" 
                                           id="full_name" 
                                           name="full_name" 
                                           value="{{ old('full_name', auth()->user()->name) }}" 
                                           required>
                                    @error('full_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="education" class="form-label fw-bold">
                                        <i class="fas fa-graduation-cap me-2 text-primary"></i>
                                        المؤهل التعليمي *
                                    </label>
                                    <textarea class="form-control @error('education') is-invalid @enderror" 
                                              id="education" 
                                              name="education" 
                                              rows="3" 
                                              placeholder="اذكر مؤهلك التعليمي بالتفصيل (الدرجة العلمية، التخصص، الجامعة، سنة التخرج...)"
                                              required>{{ old('education') }}</textarea>
                                    @error('education')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="experience" class="form-label fw-bold">
                                        <i class="fas fa-briefcase me-2 text-primary"></i>
                                        الخبرات والمهارات *
                                    </label>
                                    <textarea class="form-control @error('experience') is-invalid @enderror" 
                                              id="experience" 
                                              name="experience" 
                                              rows="4" 
                                              placeholder="اذكر خبراتك السابقة في مجال المبيعات أو التسويق أو أي مجال ذي صلة، والمهارات التي تؤهلك لتكون مندوباً ناجحاً..."
                                              required>{{ old('experience') }}</textarea>
                                    @error('experience')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-12 mb-4">
                                    <label for="additional_notes" class="form-label fw-bold">
                                        <i class="fas fa-sticky-note me-2 text-primary"></i>
                                        ملاحظات إضافية
                                    </label>
                                    <textarea class="form-control @error('additional_notes') is-invalid @enderror" 
                                              id="additional_notes" 
                                              name="additional_notes" 
                                              rows="3" 
                                              placeholder="أي معلومات إضافية تريد إضافتها لطلبك...">{{ old('additional_notes') }}</textarea>
                                    @error('additional_notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال الطلب
                                </button>
                            </div>
                        </form>
                    @endif

                    <!-- معلومات إضافية -->
                    <div class="mt-5 pt-4 border-top">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات مهمة
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        مراجعة الطلب تستغرق 3-5 أيام عمل
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        ستحصل على 1000 نقطة عند القبول
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        إمكانية التقديم مرة أخرى بعد أسبوعين من الرفض
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        مطلوب رفع مستندات في المرحلة الثانية
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
