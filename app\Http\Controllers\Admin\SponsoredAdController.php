<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SponsoredAd;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class SponsoredAdController extends Controller
{
    /**
     * عرض جميع الإعلانات الممولة مع التبويبات
     */
    public function index(Request $request): View
    {
        // تحديد التبويب النشط
        $activeTab = $request->get('tab', 'active');

        $query = SponsoredAd::with(['ad.user', 'ad.category', 'country', 'state'])
                            ->orderBy('priority', 'asc')
                            ->orderBy('created_at', 'desc');

        // فلترة حسب التبويب
        switch ($activeTab) {
            case 'active':
                $query->where('status', 'active')->where('is_active', true);
                break;
            case 'inactive':
                $query->where(function($q) {
                    $q->where('status', 'expired')
                      ->orWhere('status', 'paused')
                      ->orWhere('is_active', false);
                });
                break;
            case 'pending':
                $query->where('status', 'pending');
                break;
        }

        // فلترة حسب الأولوية
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // فلترة حسب الدولة
        if ($request->filled('country_id')) {
            $query->where('country_id', $request->country_id);
        }

        // البحث في عنوان الإعلان
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('ad', function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('id', $search);
            });
        }

        $sponsoredAds = $query->paginate(20)->appends($request->query());

        // إحصائيات محدثة
        $stats = [
            'total' => SponsoredAd::count(),
            'active' => SponsoredAd::where('status', 'active')->where('is_active', true)->count(),
            'inactive' => SponsoredAd::where(function($q) {
                $q->where('status', 'expired')
                  ->orWhere('status', 'paused')
                  ->orWhere('is_active', false);
            })->count(),
            'pending' => SponsoredAd::where('status', 'pending')->count(),
            'total_revenue' => SponsoredAd::sum('cost'),
        ];

        return view('admin.sponsored-ads.index', [
            'sponsoredAds' => $sponsoredAds,
            'stats' => $stats,
            'activeTab' => $activeTab,
            'countries' => Country::all(['id', 'name']),
            'priorities' => [
                1 => 'عالية جداً',
                2 => 'عالية',
                3 => 'متوسطة',
                4 => 'منخفضة',
                5 => 'منخفضة جداً'
            ],
            'statuses' => [
                'active' => 'نشط',
                'expired' => 'منتهي',
                'paused' => 'متوقف',
                'pending' => 'معلق'
            ]
        ]);
    }

    /**
     * عرض تفاصيل إعلان ممول
     */
    public function show(SponsoredAd $sponsoredAd): View
    {
        $sponsoredAd->load(['ad.user', 'ad.category', 'ad.media']);
        
        return view('admin.sponsored-ads.show', compact('sponsoredAd'));
    }

    /**
     * تفعيل الرعاية (قبول)
     */
    public function activate(SponsoredAd $sponsoredAd): RedirectResponse
    {
        if ($sponsoredAd->status !== 'pending') {
            return back()->with('error', 'يمكن تفعيل الإعلانات المعلقة فقط');
        }

        if ($sponsoredAd->ad->status->value !== 'published') {
            return back()->with('error', 'يجب أن يكون الإعلان منشوراً لتفعيل الرعاية');
        }

        $sponsoredAd->startSponsorship();

        return back()->with('success', 'تم قبول وتفعيل الرعاية بنجاح');
    }

    /**
     * رفض الرعاية
     */
    public function reject(SponsoredAd $sponsoredAd): RedirectResponse
    {
        if ($sponsoredAd->status !== 'pending') {
            return back()->with('error', 'يمكن رفض الإعلانات المعلقة فقط');
        }

        $sponsoredAd->rejectSponsorship();

        return back()->with('success', 'تم رفض الرعاية بنجاح');
    }

    /**
     * الموافقة على الإعلان وتفعيل الرعاية في خطوة واحدة
     */
    public function approveAndActivate(SponsoredAd $sponsoredAd): RedirectResponse
    {
        if ($sponsoredAd->status !== 'pending') {
            return back()->with('error', 'يمكن تفعيل الإعلانات المعلقة فقط');
        }

        // التحقق من حالة الإعلان
        if ($sponsoredAd->ad->status->value === 'published') {
            // الإعلان منشور بالفعل، فقط تفعيل الرعاية
            $sponsoredAd->startSponsorship();
            return back()->with('success', 'تم تفعيل الرعاية بنجاح');
        }

        // الإعلان غير منشور، نشره أولاً ثم تفعيل الرعاية
        try {
            // نشر الإعلان
            $sponsoredAd->ad->update([
                'status' => \App\Enums\AdStatus::PUBLISHED
            ]);

            // تحديث عدد الإعلانات للمستخدم
            if ($sponsoredAd->ad->user) {
                $sponsoredAd->ad->user->increment('Number_Ads');
            }

            // تفعيل الرعاية
            $sponsoredAd->startSponsorship();

            return back()->with('success', 'تم نشر الإعلان وتفعيل الرعاية بنجاح');

        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء نشر الإعلان: ' . $e->getMessage());
        }
    }

    /**
     * إيقاف الرعاية
     */
    public function deactivate(SponsoredAd $sponsoredAd): RedirectResponse
    {
        if (!$sponsoredAd->is_active) {
            return back()->with('error', 'الرعاية غير نشطة بالفعل');
        }

        $sponsoredAd->expireSponsorship();

        return back()->with('success', 'تم إيقاف الرعاية بنجاح');
    }

    /**
     * حذف سجل الرعاية فقط (بدون حذف الإعلان)
     */
    public function destroy(SponsoredAd $sponsoredAd): RedirectResponse
    {
        $sponsoredAd->delete();

        return redirect()->route('admin.sponsored-ads.index')
            ->with('success', 'تم حذف سجل الرعاية بنجاح (الإعلان لا يزال موجود)');
    }

    /**
     * حذف الإعلان كاملاً مع سجل الرعاية
     */
    public function destroyAd(SponsoredAd $sponsoredAd): RedirectResponse
    {
        $ad = $sponsoredAd->ad;

        // حذف سجل الرعاية أولاً
        $sponsoredAd->delete();

        // ثم حذف الإعلان
        if ($ad) {
            $ad->delete();
        }

        return redirect()->route('admin.sponsored-ads.index')
            ->with('success', 'تم حذف الإعلان وسجل الرعاية بنجاح');
    }

    /**
     * عرض نموذج تعديل إعلان ممول
     */
    public function edit(SponsoredAd $sponsoredAd): View
    {
        $sponsoredAd->load(['ad', 'country', 'state']);

        return view('admin.sponsored-ads.edit', [
            'sponsoredAd' => $sponsoredAd,
            'countries' => Country::all(['id', 'name']),
            'categories' => Category::whereNull('parent_id')->with('children')->get(),
            'priorities' => [
                1 => 'عالية جداً',
                2 => 'عالية',
                3 => 'متوسطة',
                4 => 'منخفضة',
                5 => 'منخفضة جداً'
            ]
        ]);
    }

    /**
     * تحديث إعلان ممول
     */
    public function update(Request $request, SponsoredAd $sponsoredAd): RedirectResponse
    {
        $validated = $request->validate([
            'priority' => 'required|integer|between:1,5',
            'country_id' => 'nullable|exists:countries,id',
            'state_id' => 'nullable|exists:states,id',
            'city_id' => 'nullable|string',
            'show_in_search' => 'boolean',
            'show_in_details' => 'boolean',
            'target_categories' => 'nullable|array',
            'target_categories.*' => 'exists:categories,id',
            'status' => 'required|in:active,expired,paused,pending'
        ]);

        // تحويل checkboxes إلى boolean
        $validated['show_in_search'] = $request->has('show_in_search');
        $validated['show_in_details'] = $request->has('show_in_details');

        $sponsoredAd->update($validated);

        return redirect()
            ->route('admin.sponsored-ads.index')
            ->with('success', 'تم تحديث الإعلان الممول بنجاح');
    }

    /**
     * تغيير حالة إعلان ممول
     */
    public function toggleStatus(SponsoredAd $sponsoredAd): JsonResponse
    {
        $newStatus = $sponsoredAd->status === 'active' ? 'paused' : 'active';
        $sponsoredAd->update(['status' => $newStatus]);

        return response()->json([
            'success' => true,
            'status' => $newStatus,
            'message' => 'تم تغيير حالة الإعلان بنجاح'
        ]);
    }

    /**
     * تحديث أولوية إعلان ممول
     */
    public function updatePriority(Request $request, SponsoredAd $sponsoredAd): JsonResponse
    {
        $request->validate([
            'priority' => 'required|integer|between:1,5'
        ]);

        $sponsoredAd->update(['priority' => $request->priority]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الأولوية بنجاح'
        ]);
    }

    /**
     * الحصول على الولايات حسب الدولة
     */
    public function getStates(Country $country): JsonResponse
    {
        $states = State::where('country_id', $country->id)
                      ->select('id', 'name')
                      ->get();

        return response()->json($states);
    }

    /**
     * الحصول على المدن حسب الولاية
     */
    public function getCities(State $state): JsonResponse
    {
        $cities = City::where('state_id', $state->id)
                     ->select('id', 'name')
                     ->get();

        return response()->json($cities);
    }

    /**
     * تحديث جميع الإعلانات الممولة
     */
    public function updateAll(): RedirectResponse
    {
        $activeSponsoredAds = SponsoredAd::active()->get();
        $expiredCount = 0;

        foreach ($activeSponsoredAds as $sponsoredAd) {
            if ($sponsoredAd->isExpired()) {
                $sponsoredAd->expireSponsorship();
                $expiredCount++;
            }
        }

        return back()->with('success', "تم تحديث الإعلانات الممولة. انتهت صلاحية {$expiredCount} إعلان");
    }
}
