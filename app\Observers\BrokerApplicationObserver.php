<?php

namespace App\Observers;

use App\Models\BrokerApplication;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class BrokerApplicationObserver
{
    /**
     * Handle the BrokerApplication "created" event.
     */
    public function created(BrokerApplication $brokerApplication): void
    {
        Log::info("تم إنشاء طلب مندوب جديد للمستخدم: {$brokerApplication->user->name} (ID: {$brokerApplication->id})");
    }

    /**
     * Handle the BrokerApplication "updated" event.
     */
    public function updated(BrokerApplication $brokerApplication): void
    {
        // تسجيل تغيير الحالة
        if ($brokerApplication->isDirty('status')) {
            $oldStatus = $brokerApplication->getOriginal('status');
            $newStatus = $brokerApplication->status;
            
            Log::info("تم تغيير حالة طلب المندوب (ID: {$brokerApplication->id}) من {$oldStatus} إلى {$newStatus}");
        }
    }

    /**
     * Handle the BrokerApplication "deleted" event.
     */
    public function deleted(BrokerApplication $brokerApplication): void
    {
        try {
            // حذف جميع المستندات المرفوعة
            if ($brokerApplication->documents && is_array($brokerApplication->documents)) {
                
                // حذف شهادة التعليم
                if (isset($brokerApplication->documents['education_certificate'])) {
                    $this->deleteDocument($brokerApplication->documents['education_certificate'], 'شهادة التعليم');
                }

                // حذف إثبات الخبرة
                if (isset($brokerApplication->documents['experience_proof'])) {
                    $this->deleteDocument($brokerApplication->documents['experience_proof'], 'إثبات الخبرة');
                }

                // حذف البطاقة الشخصية
                if (isset($brokerApplication->documents['id_card'])) {
                    $this->deleteDocument($brokerApplication->documents['id_card'], 'البطاقة الشخصية');
                }

                // حذف المستندات الإضافية
                if (isset($brokerApplication->documents['additional_documents']) && is_array($brokerApplication->documents['additional_documents'])) {
                    foreach ($brokerApplication->documents['additional_documents'] as $index => $document) {
                        $this->deleteDocument($document, "مستند إضافي رقم " . ($index + 1));
                    }
                }
            }

            Log::info("تم حذف طلب المندوب وجميع مستنداته (ID: {$brokerApplication->id}) للمستخدم: {$brokerApplication->user->name}");

        } catch (\Exception $e) {
            Log::error("خطأ في حذف مستندات طلب المندوب (ID: {$brokerApplication->id}): " . $e->getMessage());
        }
    }

    /**
     * Handle the BrokerApplication "restored" event.
     */
    public function restored(BrokerApplication $brokerApplication): void
    {
        //
    }

    /**
     * Handle the BrokerApplication "force deleted" event.
     */
    public function forceDeleted(BrokerApplication $brokerApplication): void
    {
        // نفس منطق الحذف العادي
        $this->deleted($brokerApplication);
    }

    /**
     * حذف مستند واحد
     */
    private function deleteDocument(string $documentPath, string $documentType): void
    {
        try {
            if (Storage::disk('public')->exists($documentPath)) {
                Storage::disk('public')->delete($documentPath);
                Log::info("تم حذف {$documentType}: {$documentPath}");
            }
        } catch (\Exception $e) {
            Log::error("خطأ في حذف {$documentType} ({$documentPath}): " . $e->getMessage());
        }
    }
}
