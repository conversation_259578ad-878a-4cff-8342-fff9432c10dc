<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BrokerApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'full_name',
        'national_id',
        'education',
        'experience',
        'additional_notes',
        'status',
        'rejection_reason',
        'rejected_at',
        'documents',
    ];

    protected $casts = [
        'documents' => 'array',
        'rejected_at' => 'datetime',
    ];

    // الحالات المختلفة
    const STATUS_PENDING = 'pending';
    const STATUS_FIRST_STAGE_ACCEPTED = 'first_stage_accepted';
    const STATUS_SECOND_STAGE_ACCEPTED = 'second_stage_accepted';
    const STATUS_REJECTED = 'rejected';

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * التحقق من إمكانية التقديم مرة أخرى
     */
    public function canReapply(): bool
    {
        if ($this->status !== self::STATUS_REJECTED || !$this->rejected_at) {
            return false;
        }

        return $this->rejected_at->addWeeks(2)->isPast();
    }

    /**
     * الحصول على أيام متبقية للتقديم مرة أخرى
     */
    public function daysUntilReapply(): int
    {
        if ($this->status !== self::STATUS_REJECTED || !$this->rejected_at) {
            return 0;
        }

        $reapplyDate = $this->rejected_at->addWeeks(2);
        return max(0, now()->diffInDays($reapplyDate, false));
    }

    /**
     * الحصول على نص الحالة
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'في انتظار المراجعة',
            self::STATUS_FIRST_STAGE_ACCEPTED => 'مقبول - المرحلة الأولى',
            self::STATUS_SECOND_STAGE_ACCEPTED => 'مقبول - المرحلة الثانية',
            self::STATUS_REJECTED => 'مرفوض',
            default => 'غير محدد'
        };
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_FIRST_STAGE_ACCEPTED => 'info',
            self::STATUS_SECOND_STAGE_ACCEPTED => 'primary',
            self::STATUS_REJECTED => 'danger',
            default => 'secondary'
        };
    }
}
