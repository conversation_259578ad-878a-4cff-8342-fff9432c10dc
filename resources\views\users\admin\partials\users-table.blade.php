@php
    use Illuminate\Support\Facades\Storage;
@endphp

@if(count($users) > 0)
<div class="table-responsive">
    <table class="table table-bordered text-nowrap mb-0">
        <thead class="border-top">
            <tr>
                <th class="bg-transparent border-bottom-0" style="width: 5%;">الاسم الكامل</th>
                <th class="bg-transparent border-bottom-0">البريد الإلكتروني</th>
                <th class="bg-transparent border-bottom-0">الهاتف</th>
                <th class="bg-transparent border-bottom-0">الرقم القومي</th>
                <th class="bg-transparent border-bottom-0">الدولة</th>
                @if($userType === 'broker')
                <th class="bg-transparent border-bottom-0">نوع المندوب</th>
                @endif
                @if($userType === 'vip')
                <th class="bg-transparent border-bottom-0">رتبة VIP</th>
                @endif
                <th class="bg-transparent border-bottom-0" style="width: 10%;">الحالة</th>
                <th class="bg-transparent border-bottom-0" style="width: 5%;">الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($users as $user)
            <tr class="border-bottom">
                <td>
                    <div class="d-flex">
                        <span class="avatar bradius" style="background-image: url({{ $user->avatar ? Storage::url($user->avatar) : get_random_avatar() }})"></span>
                        <div class="ms-3 mt-0 mt-sm-2 d-block">
                            <h6 class="mb-0 fs-14 fw-semibold">{{$user->name}}</h6>
                            @if($userType === 'regular')
                                <small class="text-muted">مستخدم عادي</small>
                            @elseif($userType === 'broker')
                                <small class="text-warning">مندوب</small>
                            @elseif($userType === 'vip')
                                <small class="text-success">VIP</small>
                            @endif
                        </div>
                    </div>
                </td>
                <td><span class="mt-sm-2 d-block">{{ $user->email }}</span></td>
                <td><span class="mt-sm-2 d-block">{{ $user->mobile }}</span></td>
                <td>
                    @if($user->national_id)
                        <span class="mt-sm-2 d-block">{{ $user->national_id }}</span>
                    @else
                        <span class="mt-sm-2 d-block text-muted">غير محدد</span>
                    @endif
                </td>
                <td>
                    @if($user->country)
                        <span class="mt-sm-2 d-block">{{ $user->country->emoji }} {{ $user->country->name }}</span>
                    @else
                        <span class="mt-sm-2 d-block">لا توجد دولة</span>
                    @endif
                </td>
                @if($userType === 'broker')
                <td>
                    <span class="badge bg-warning">مندوب معتمد</span>
                </td>
                @endif
                @if($userType === 'vip')
                <td>
                    <span class="badge bg-success">رتبة {{ $user->rank }}</span>
                </td>
                @endif
                <td>
                    <div class="d-flex">
                        <span class="badge bg-{{ $user->is_active ? 'success' : 'danger' }} me-2">
                            {{ $user->is_active ? 'نشط' : 'غير نشط' }}
                        </span>
                        @if($user->is_trusted)
                            <span class="badge bg-info">موثق</span>
                        @endif
                    </div>
                </td>
                <td>
                    <div class="mt-sm-1 d-block">
                        <a href="{{ route('admin.users.show', $user->id) }}" class="btn text-dark btn-sm"
                           data-bs-toggle="tooltip" data-bs-original-title="عرض">
                            <span class="fa-regular fa-eye fs-14"></span>
                        </a>
                        <a href="{{ route('admin.users.edit', $user->id) }}" class="btn text-primary btn-sm"
                           data-bs-toggle="tooltip" data-bs-original-title="تعديل">
                            <span class="fa-regular fa-edit fs-14"></span>
                        </a>
                    </div>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    
    <!-- Pagination -->
    <div class="mt-3">
        {{ $users->links('pagination.admin') }}
    </div>
</div>
@else
<div class="text-center p-4">
    <img src="{{ asset('assets/images/icons/man.svg') }}" class="w-25" alt="empty">
    <h4 class="mt-3">
        @if($userType === 'regular')
            لا توجد مستخدمون عاديون
        @elseif($userType === 'broker')
            لا توجد مندوبون
        @elseif($userType === 'vip')
            لا توجد مستخدمون VIP
        @else
            لا توجد مستخدمون
        @endif
    </h4>
</div>
@endif
