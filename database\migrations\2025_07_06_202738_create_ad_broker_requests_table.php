<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ad_broker_requests', function (Blueprint $table) {
            $table->id();
            $table->uuid('ad_id');
            $table->uuid('user_id');
            $table->text('note')->nullable();
            $table->timestamps();
        
            $table->foreign('ad_id')->references('id')->on('ads')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        
            $table->unique(['ad_id', 'user_id']); // كل مندوب يكتب لمرة واحدة على إعلان واحد
        });
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_broker_requests');
    }
};
