<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserWarning extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'user_id',
        'admin_id',
        'report_id',
        'type',
        'title',
        'reason',
        'admin_notes',
        'expires_at',
        'is_active'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    // Warning types
    const TYPE_POSTING_BAN = 'posting_ban';
    const TYPE_CHAT_BAN = 'chat_ban';
    const TYPE_BIDDING_BAN = 'bidding_ban';
    const TYPE_COMMENT_BAN = 'comment_ban';
    const TYPE_GENERAL_WARNING = 'general_warning';

    const TYPES = [
        self::TYPE_POSTING_BAN => 'منع النشر',
        self::TYPE_CHAT_BAN => 'منع الشات',
        self::TYPE_BIDDING_BAN => 'منع المزايدة',
        self::TYPE_COMMENT_BAN => 'منع التعليق',
        self::TYPE_GENERAL_WARNING => 'تحذير عام'
    ];

    /**
     * Get the user that owns the warning.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin that created the warning.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * Get the report that caused this warning.
     */
    public function report(): BelongsTo
    {
        return $this->belongsTo(Report::class);
    }

    /**
     * Check if warning is currently active.
     */
    public function isActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            // Auto-deactivate expired warnings
            $this->update(['is_active' => false]);
            return false;
        }

        return true;
    }

    /**
     * Get type label in Arabic.
     */
    public function getTypeLabelAttribute(): string
    {
        return self::TYPES[$this->type] ?? 'غير محدد';
    }

    /**
     * Get remaining time for warning.
     */
    public function getRemainingTimeAttribute(): ?string
    {
        if (!$this->expires_at) {
            return 'دائم';
        }

        if ($this->expires_at->isPast()) {
            return 'منتهي';
        }

        return $this->expires_at->diffForHumans();
    }

    /**
     * Scope for active warnings.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope for specific warning type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for user warnings.
     */
    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }
}
