<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\CategoryAttribute;

class CompleteAttributesSeederPart2 extends Seeder
{
    public function addMobileTabletAttributes()
    {
        echo "📱 Adding mobile & tablet attributes...\n";
        
        // Mobile Phones
        $this->addAttributesToCategory('mobaylat', [
            ['brand', 'نوع الموبايل', 'select', [
                'apple' => 'آيفون',
                'samsung' => 'سامسونج',
                'huawei' => 'هواوي',
                'xiaomi' => 'شاومي',
                'oppo' => 'أوبو',
                'vivo' => 'فيفو',
                'oneplus' => 'ون بلس',
                'google' => 'جوجل',
                'sony' => 'سوني',
                'lg' => 'إل جي',
                'nokia' => 'نوكيا',
                'motorola' => 'موتورولا',
                'realme' => 'ريلمي',
                'honor' => 'هونر',
                'other' => 'أخرى'
            ], 1, true],
            ['model', 'الموديل', 'input', null, 2, false],
            ['storage', 'مساحة التخزين', 'select', [
                '16gb' => '16 جيجا',
                '32gb' => '32 جيجا',
                '64gb' => '64 جيجا',
                '128gb' => '128 جيجا',
                '256gb' => '256 جيجا',
                '512gb' => '512 جيجا',
                '1tb' => '1 تيرا'
            ], 3, false],
            ['ram', 'حجم الرام', 'select', [
                '1gb' => '1 جيجا',
                '2gb' => '2 جيجا',
                '3gb' => '3 جيجا',
                '4gb' => '4 جيجا',
                '6gb' => '6 جيجا',
                '8gb' => '8 جيجا',
                '12gb' => '12 جيجا',
                '16gb' => '16 جيجا'
            ], 4, false],
            ['condition', 'حالة الموبايل', 'select', [
                'new' => 'جديد',
                'like_new' => 'كالجديد',
                'excellent' => 'ممتاز',
                'good' => 'جيد',
                'fair' => 'مقبول',
                'poor' => 'يحتاج إصلاح'
            ], 5, true],
            ['color', 'لون الموبايل', 'select', [
                'black' => 'أسود',
                'white' => 'أبيض',
                'gold' => 'ذهبي',
                'silver' => 'فضي',
                'blue' => 'أزرق',
                'red' => 'أحمر',
                'green' => 'أخضر',
                'purple' => 'بنفسجي',
                'pink' => 'وردي',
                'gray' => 'رمادي',
                'other' => 'لون آخر'
            ], 6, false],
            ['screen_size', 'حجم الشاشة', 'select', [
                '4_inch' => '4 بوصة',
                '4.5_inch' => '4.5 بوصة',
                '5_inch' => '5 بوصة',
                '5.5_inch' => '5.5 بوصة',
                '6_inch' => '6 بوصة',
                '6.5_inch' => '6.5 بوصة',
                '7_inch' => '7 بوصة'
            ], 7, false],
            ['battery_capacity', 'سعة البطارية', 'select', [
                '2000mah' => '2000 مللي أمبير',
                '3000mah' => '3000 مللي أمبير',
                '4000mah' => '4000 مللي أمبير',
                '5000mah' => '5000 مللي أمبير',
                '6000mah' => '6000 مللي أمبير'
            ], 8, false],
            ['camera_megapixels', 'دقة الكاميرا', 'select', [
                '8mp' => '8 ميجا بكسل',
                '12mp' => '12 ميجا بكسل',
                '16mp' => '16 ميجا بكسل',
                '20mp' => '20 ميجا بكسل',
                '48mp' => '48 ميجا بكسل',
                '64mp' => '64 ميجا بكسل',
                '108mp' => '108 ميجا بكسل'
            ], 9, false],
            ['operating_system', 'نظام التشغيل', 'select', [
                'ios' => 'iOS',
                'android' => 'أندرويد',
                'other' => 'أخرى'
            ], 10, false],
            ['dual_sim', 'شريحتين', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 11, false],
            ['warranty', 'الضمان', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر',
                'expired' => 'منتهي'
            ], 12, false],
            ['accessories_included', 'الإكسسوارات المرفقة', 'select', [
                'complete' => 'كاملة',
                'charger_only' => 'الشاحن فقط',
                'box_only' => 'العلبة فقط',
                'phone_only' => 'الجهاز فقط'
            ], 13, false],
            ['network_type', 'نوع الشبكة', 'select', [
                '5g' => '5G',
                '4g' => '4G',
                '3g' => '3G',
                '2g' => '2G'
            ], 14, false],
            ['fingerprint', 'بصمة الإصبع', 'select', [
                'yes' => 'متوفرة',
                'no' => 'غير متوفرة'
            ], 15, false],
            ['face_unlock', 'فتح بالوجه', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 16, false],
            ['water_resistant', 'مقاوم للماء', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 17, false],
            ['wireless_charging', 'الشحن اللاسلكي', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 18, false],
            ['fast_charging', 'الشحن السريع', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 19, false],
            ['headphone_jack', 'منفذ السماعات', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 20, false]
        ]);
        
        // Tablets
        $this->addAttributesToCategory('tablt', [
            ['brand', 'نوع التابلت', 'select', [
                'apple' => 'آيباد',
                'samsung' => 'سامسونج',
                'huawei' => 'هواوي',
                'lenovo' => 'لينوفو',
                'microsoft' => 'مايكروسوفت',
                'amazon' => 'أمازون',
                'other' => 'أخرى'
            ], 1, true],
            ['model', 'الموديل', 'input', null, 2, false],
            ['screen_size', 'حجم الشاشة', 'select', [
                '7_inch' => '7 بوصة',
                '8_inch' => '8 بوصة',
                '9_inch' => '9 بوصة',
                '10_inch' => '10 بوصة',
                '11_inch' => '11 بوصة',
                '12_inch' => '12 بوصة',
                '13_inch' => '13 بوصة'
            ], 3, false],
            ['storage', 'مساحة التخزين', 'select', [
                '16gb' => '16 جيجا',
                '32gb' => '32 جيجا',
                '64gb' => '64 جيجا',
                '128gb' => '128 جيجا',
                '256gb' => '256 جيجا',
                '512gb' => '512 جيجا',
                '1tb' => '1 تيرا'
            ], 4, false],
            ['ram', 'حجم الرام', 'select', [
                '2gb' => '2 جيجا',
                '3gb' => '3 جيجا',
                '4gb' => '4 جيجا',
                '6gb' => '6 جيجا',
                '8gb' => '8 جيجا',
                '16gb' => '16 جيجا'
            ], 5, false],
            ['condition', 'حالة التابلت', 'select', [
                'new' => 'جديد',
                'like_new' => 'كالجديد',
                'excellent' => 'ممتاز',
                'good' => 'جيد',
                'fair' => 'مقبول'
            ], 6, true],
            ['connectivity', 'الاتصال', 'select', [
                'wifi_only' => 'واي فاي فقط',
                'wifi_cellular' => 'واي فاي + شبكة خلوية'
            ], 7, false],
            ['operating_system', 'نظام التشغيل', 'select', [
                'ios' => 'iOS',
                'android' => 'أندرويد',
                'windows' => 'ويندوز',
                'other' => 'أخرى'
            ], 8, false],
            ['keyboard_included', 'لوحة مفاتيح مرفقة', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 9, false],
            ['stylus_included', 'قلم رقمي مرفق', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 10, false],
            ['camera', 'الكاميرا', 'select', [
                'front_back' => 'أمامية وخلفية',
                'front_only' => 'أمامية فقط',
                'back_only' => 'خلفية فقط',
                'none' => 'لا توجد'
            ], 11, false],
            ['battery_life', 'عمر البطارية', 'select', [
                '6_hours' => '6 ساعات',
                '8_hours' => '8 ساعات',
                '10_hours' => '10 ساعات',
                '12_hours' => '12 ساعة',
                '15_hours' => '15 ساعة'
            ], 12, false],
            ['warranty', 'الضمان', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 13, false]
        ]);
        
        // Mobile & Tablet Accessories
        $this->addAttributesToCategory('akssoarat-mobayl-otablt', [
            ['accessory_type', 'نوع الإكسسوار', 'select', [
                'case' => 'جراب',
                'screen_protector' => 'واقي شاشة',
                'charger' => 'شاحن',
                'power_bank' => 'باور بانك',
                'headphones' => 'سماعات',
                'bluetooth_speaker' => 'سماعة بلوتوث',
                'car_mount' => 'حامل سيارة',
                'selfie_stick' => 'عصا سيلفي',
                'cable' => 'كابل',
                'memory_card' => 'كارت ذاكرة',
                'other' => 'أخرى'
            ], 1, true],
            ['brand', 'الماركة', 'input', null, 2, false],
            ['compatible_devices', 'متوافق مع الأجهزة', 'input', null, 3, false],
            ['condition', 'الحالة', 'select', [
                'new' => 'جديد',
                'used' => 'مستعمل'
            ], 4, true],
            ['color', 'اللون', 'input', null, 5, false],
            ['warranty', 'الضمان', 'select', [
                'yes' => 'متوفر',
                'no' => 'غير متوفر'
            ], 6, false]
        ]);
        
        // Mobile Numbers
        $this->addAttributesToCategory('arkam-mobayl', [
            ['number_type', 'نوع الرقم', 'select', [
                'vip' => 'VIP',
                'golden' => 'ذهبي',
                'silver' => 'فضي',
                'special' => 'مميز',
                'regular' => 'عادي'
            ], 1, true],
            ['network_operator', 'شركة الاتصالات', 'select', [
                'vodafone' => 'فودافون',
                'orange' => 'أورانج',
                'etisalat' => 'اتصالات',
                'we' => 'WE'
            ], 2, true],
            ['number_pattern', 'نمط الرقم', 'select', [
                'repeated_digits' => 'أرقام متكررة',
                'sequential' => 'متسلسل',
                'easy_to_remember' => 'سهل الحفظ',
                'special_date' => 'تاريخ مميز',
                'other' => 'أخرى'
            ], 3, false],
            ['price_negotiable', 'السعر قابل للتفاوض', 'select', [
                'yes' => 'نعم',
                'no' => 'لا'
            ], 4, false]
        ]);
    }
    
    private function addAttributesToCategory($slug, $attributes)
    {
        $category = Category::where('slug', $slug)->first();
        if (!$category) {
            echo "Category not found: {$slug}\n";
            return;
        }
        
        foreach ($attributes as $attr) {
            CategoryAttribute::create([
                'category_id' => $category->id,
                'attribute_name' => $attr[0],
                'attribute_label' => $attr[1],
                'attribute_type' => $attr[2],
                'attribute_options' => $attr[3],
                'sort_order' => $attr[4],
                'is_required' => $attr[5]
            ]);
        }
        
        echo "  ✅ Added " . count($attributes) . " attributes to {$category->name}\n";
    }
}
