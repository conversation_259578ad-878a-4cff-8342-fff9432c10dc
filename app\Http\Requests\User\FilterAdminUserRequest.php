<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class FilterAdminUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'nullable|string',
            'national_id' => 'nullable|string|max:14',
            'country_id' => 'nullable|integer|exists:countries,id',
            'status' => 'nullable|string|in:active,inactive',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date',
        ];
    }
}
